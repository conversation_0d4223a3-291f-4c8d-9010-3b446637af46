[{"paperTitle": "Hot or Cold? Adaptive Temperature Sampling for Code Generation with Large Language Models", "paperSlug": "Adaptive_Temperature_Sampling", "methodName": "Adaptive Temperature Sampling (AdapT)", "simplifiedExplanation": "Think of code generation as driving on a road where intersections (block starts) are tricky and straight segments (inside blocks) are easy. AdapT tells the driver to slow down and look around at intersections (higher temperature—more choices) but speed up on straightaways (lower temperature—fewer random swerves), leading to both exploration and stability.", "prerequisites": "• Python 3.8+\n• PyTorch >= 1.13 with access to model logits\n• Pre-trained causal LLM (e.g., CodeGeeX-13B)\n• <PERSON><PERSON><PERSON> able to emit newline/indent tokens\n• GPU with ≥16 GB VRAM (optional but recommended)\n• Familiarity with softmax temperature and top-p sampling", "stepByStepGuide": "1. Load your causal LLM with `output_scores=True`.\n2. Initialise two floats: `a=0.8`, `b=0.5` (or tune later).\n3. While generating, detect if the previous token was a newline and if indentation/braces increased—flag `is_block_start`.\n4. Compute `T = a if is_block_start else b`.\n5. Divide logits by T, then apply top-p filtering (p=0.95).\n6. Sample next token, append to sequence, update indentation tracker.\n7. Repeat to max length; optionally generate multiple samples in batch.", "practicalExample": {"scenarioDescription": "You want an LLM to generate a C++ audio-DSP `processBlock` function for a JUCE VST, and you plan to sample 15 candidates, hoping at least one compiles and passes unit tests.", "implementationCode": "```python\nfrom transformers import AutoModelForCausalLM, AutoTokenizer, top_k_top_p_filtering\nimport torch, re\nmodel_id = \"deepseek-ai/codegeex-13b\"\nmodel = AutoModelForCausalLM.from_pretrained(model_id, torch_dtype=torch.float16).cuda()\nTok = AutoTokenizer.from_pretrained(model_id)\nprompt = \"// Write the processBlock method for a JUCE AudioProcessor...\\nvoid MyPlugin::processBlock (AudioBuffer<float>& buffer, MidiBuffer& midi) {\\n\"\ninput_ids = Tok(prompt, return_tensors=\"pt\").input_ids.cuda()\nindent = 1  # starting inside the function\noutputs = []\nfor _ in range(500):\n    out = model(input_ids, output_scores=True, return_dict=True)\n    logits = out.logits[:, -1, :]\n    last = Tok.decode(int(input_ids[0, -1]))\n    if last == \"\\n\":\n        # crude brace detector\n        line = Tok.decode(input_ids[0]).split(\"\\n\")[-2]\n        indent = indent + line.count(\"{\") - line.count(\"}\")\n    is_block_start = last == \"\\n\" and indent > 0 and re.match(r\"\\s*$\", Tok.decode(input_ids[0, -1]))\n    T = 0.8 if is_block_start else 0.5\n    logits = logits / T\n    filtered = top_k_top_p_filtering(logits, top_p=0.95)\n    next_id = torch.multinomial(torch.softmax(filtered, dim=-1), 1)\n    input_ids = torch.cat([input_ids, next_id], dim=-1)\n    if Tok.decode(int(next_id)) == \"}\" and indent == 0:\n        break\ncode = Tok.decode(input_ids[0])\nprint(code)\n```", "expectedOutcome": "Among 15 samples, 2–3 should compile in Clang without syntax errors, and at least one should pass the predefined unit tests for audio clipping and gain adjustment—an improvement over baseline sampling where often none compile cleanly."}}, {"paperTitle": "Can LLM Replace Stack Overflow? A Study on Robustness and Reliability of Large Language Model Code Generation", "paperSlug": "Can_ChatGPT_replace_StackOverflow", "methodsContent": "ROBUSTAPI leverages static analysis on abstract syntax trees to detect violations of formally defined API usage patterns. The key technical contribution is the automated derivation of call sequences and their alignment with rule graphs, enabling language-agnostic misuse detection. While the technique is well-grounded in prior program-analysis literature, its novelty lies in repurposing it as an evaluation harness for LLM-generated snippets.", "methodName": "Legacy AI Techniques Summary", "simplifiedExplanation": "This entry represents older, less structured method information.", "prerequisites": "N/A", "stepByStepGuide": "N/A", "practicalExample": {"scenarioDescription": "N/A", "implementationCode": "N/A", "expectedOutcome": "N/A"}}, {"paperTitle": "Chain-of-Verification Reduces Hallucination in Large Language Models", "paperSlug": "Chain_of_Verification", "methodsContent": "CoVe decomposes self-correction into generative reasoning stages inspired by chain-of-thought but specialised for factual verification. Core algorithms include: plan-and-verify prompting; factored execution where each verification question is answered in a fresh context to break information leakage; and factor+revise, an extra cross-consistency prompt per fact. Technically, each stage is a zero- or few-shot prompt to the same base decoder-only LLM (Llama-65B in experiments). The paper provides three execution variants (joint, two-step, factored) and shows through ablation that isolating contexts raises precision.", "methodName": "Legacy AI Techniques Summary", "simplifiedExplanation": "This entry represents older, less structured method information.", "prerequisites": "N/A", "stepByStepGuide": "N/A", "practicalExample": {"scenarioDescription": "N/A", "implementationCode": "N/A", "expectedOutcome": "N/A"}}, {"paperTitle": "Enhancing Zero-Shot Chain-of-Thought Reasoning in Large Language Models through Logic", "paperSlug": "Enhancing_Zero_Shot_Chain_of_Thought_Reasoning_in_Large", "methodsContent": "LoT augments zero-shot CoT with a formal logic wrapper. For each reasoning step *Ti*, the model constructs the conjunction *P ∧¬Ti* and invokes *reductio ad absurdum*: generate post-hoc explanations supporting both *Ti* and *¬Ti*, evaluate consistency, then adopt or revise. This converts verification into a preference-ranking sub-task that LLMs handle well. Key algorithms include Cmps-LoT (compose-only verification) and Adpt-LoT (full adopt-or-revise with chain regeneration).", "methodName": "Legacy AI Techniques Summary", "simplifiedExplanation": "This entry represents older, less structured method information.", "prerequisites": "N/A", "stepByStepGuide": "N/A", "practicalExample": {"scenarioDescription": "N/A", "implementationCode": "N/A", "expectedOutcome": "N/A"}}, {"paperTitle": "Few-shot Fine-tuning vs. In-context Learning: A Fair Comparison and Evaluation", "paperSlug": "Few_shot_Fine_tuning_vs_In_context_Learning_A_Fair_Comparison_and_Evaluation", "methodsContent": "The paper analyses two concrete adaptation algorithms. Pattern-based fine-tuning (PBFT) reformulates classification tasks as cloze questions, learning a lightweight verbaliser layer; the authors further combine PBFT with LoRA, injecting low-rank adaptation matrices into attention blocks for parameter-efficient updates. In-context learning is implemented with three prompt patterns (minimal, GPT-3, <PERSON>l-Ha<PERSON>ss) and up to 32 demonstrations, enabling a direct zero-parameter comparison. These methods are evaluated across 7 model scales, offering granular insight into scaling laws and sample efficiency.", "methodName": "Legacy AI Techniques Summary", "simplifiedExplanation": "This entry represents older, less structured method information.", "prerequisites": "N/A", "stepByStepGuide": "N/A", "practicalExample": {"scenarioDescription": "N/A", "implementationCode": "N/A", "expectedOutcome": "N/A"}}, {"paperTitle": "From Sparse to Dense: GPT-4 Summarization with Chain-of-Density Prompting", "paperSlug": "From_Sparse_to_Dense_GPT_4_Summarization_with_Chain_of_Density_Prompting", "methodsContent": "CoD is a *prompt-engineering algorithm* rather than a model architectural change. It couples entity extraction with iterative summary regeneration. The core algorithmic idea is to treat *average entities per token* as a controllable variable and to optimise it through five rewrite loops. Each loop adds missing entities (identified via lightweight NER or manual inspection) while enforcing a strict word budget; GPT-4 implicitly performs compression (dropping fillers), abstraction (paraphrasing phrases) and fusion (merging sentences). Technically, the method requires only GPT-4 and a standard NER tool such as spaCy; no finetuning or external optimisation is performed.", "methodName": "Legacy AI Techniques Summary", "simplifiedExplanation": "This entry represents older, less structured method information.", "prerequisites": "N/A", "stepByStepGuide": "N/A", "practicalExample": {"scenarioDescription": "N/A", "implementationCode": "N/A", "expectedOutcome": "N/A"}}, {"paperTitle": "Graph of Thoughts: Solving Elaborate Problems with Large Language Models", "paperSlug": "Graph_of_Thoughts", "methodsContent": "GoT’s core algorithmic novelty is to treat each intermediate LLM reply as a graph vertex and to let prompts explicitly reference multiple prior vertices when generating the next thought.  This enables **aggregation** (merging several thoughts into one), **refinement loops** (feedback on a node to improve itself) and **controlled generation** (k-branch exploration) within a *single* conversation context.  The approach generalises CoT and ToT without fine-tuning and is orthogonal to parameter-efficient tuning or chained-LLM methods.  Theoretical framing borrows from graph search (k-ary DAGs), with latency–volume trade-off analysis showing GoT attains O(log_k N) depth *and* full N-volume reachability.", "methodName": "Legacy AI Techniques Summary", "simplifiedExplanation": "This entry represents older, less structured method information.", "prerequisites": "N/A", "stepByStepGuide": "N/A", "practicalExample": {"scenarioDescription": "N/A", "implementationCode": "N/A", "expectedOutcome": "N/A"}}, {"paperTitle": "A Practical Guide for Large Language Models in Downstream NLP Tasks", "paperSlug": "Harnessing_the_Power_of_LLMs_in_Practice_A_Survey_on_ChatGPT_and_Beyond", "methodsContent": "The guide catalogues masked-language-model (MLM) pre-training, autoregressive next-token prediction, and reinforcement learning from human feedback (RLHF) as the three pillars underpinning modern LLMs. It clarifies when to employ zero-shot prompting versus in-context few-shot learning and contrasts these with task-specific fine-tuning. Although no new algorithms are introduced, the paper gives concrete parameter ranges (e.g., <20 B parameters counted as ‘fine-tunable’) and discusses LoRA, prefix-tuning and other parameter-efficient adapters.", "methodName": "Legacy AI Techniques Summary", "simplifiedExplanation": "This entry represents older, less structured method information.", "prerequisites": "N/A", "stepByStepGuide": "N/A", "practicalExample": {"scenarioDescription": "N/A", "implementationCode": "N/A", "expectedOutcome": "N/A"}}, {"paperTitle": "Less Likely Brainstorming: Using Language Models to Generate Alternative Hypotheses", "paperSlug": "Less_Likely_Brainstorming", "methodsContent": "BRAINSTORM augments seq2seq fine-tuning with two contrastive objectives. The *margin loss* enforces a probability gap m between the correct hypothesis given its indicator and the same hypothesis given the opposite indicator, preventing collapse to highly-likely outputs. The *similarity loss* is implemented in two flavours: in-batch (positive/negative pairs available) and indicator-flip (only one class present). It maximises agreement between (context, indicator) and target-hypothesis embeddings in the decoder while minimising similarity to negatives. Hyper-parameters (m, w_s, w_m) are tuned on validation sets. The design cleanly decomposes controllability (indicator conditioning) from language modelling capacity, and is model-agnostic beyond requiring access to hidden states.", "methodName": "Legacy AI Techniques Summary", "simplifiedExplanation": "This entry represents older, less structured method information.", "prerequisites": "N/A", "stepByStepGuide": "N/A", "practicalExample": {"scenarioDescription": "N/A", "implementationCode": "N/A", "expectedOutcome": "N/A"}}, {"paperTitle": "Meta-in-Context Learning in Large Language Models", "paperSlug": "Meta_in_context_learning", "methodsContent": "MICL relies on *prompt-level meta-learning*: the model is fed a series of N miniature few-shot tasks in a single, growing prompt. Each task consists of T demonstrations followed by a query. The method exploits transformer context-windows as an external memory, inducing a Bayesian-like posterior update within the forward pass. Core algorithms include Bayesian linear regression emulation for regression tasks and internal Kalman-filter–style belief tracking for bandits. The key innovation is showing that these mechanisms can themselves adapt—adjusting priors and exploration heuristics—when the prompt contains multiple tasks in sequence. No gradient updates or parameter-efficient fine-tuning are performed; the only control surface is the ordering and formatting of demonstrations.", "methodName": "Legacy AI Techniques Summary", "simplifiedExplanation": "This entry represents older, less structured method information.", "prerequisites": "N/A", "stepByStepGuide": "N/A", "practicalExample": {"scenarioDescription": "N/A", "implementationCode": "N/A", "expectedOutcome": "N/A"}}, {"paperTitle": "Reinforcement Learning in the Era of LLMs: What is Essential? What is needed? An RL Perspective on RLHF, Prompting, and Beyond.", "paperSlug": "Reinforcement_Learning_in_the_Era_of_LLMs", "methodsContent": "The paper positions RLHF as *online inverse reinforcement learning* with a known dynamics model. Core algorithms discussed include Be<PERSON>viour Cloning (BC), Generative Adversarial Imitation Learning (GAIL), PPO, TD-style value-based methods, and Hindsight relabelling. Technical novelty lies less in new algorithms than in the taxonomy that clarifies when each class of method is appropriate once the transition function is known. Prompt-OIRL introduces a practical inverse-RL pipeline: learn a reward model over prompt–response trajectories from offline human-rated data, then optimise prompts by maximising the learned reward at inference time.\n", "methodName": "Legacy AI Techniques Summary", "simplifiedExplanation": "This entry represents older, less structured method information.", "prerequisites": "N/A", "stepByStepGuide": "N/A", "practicalExample": {"scenarioDescription": "N/A", "implementationCode": "N/A", "expectedOutcome": "N/A"}}, {"paperTitle": "Re-Reading Improves Reasoning in Large Language Models", "paperSlug": "Re_Reading_Improves_Reasoning_in_Large_Language_Models", "methodsContent": "RE2’s core algorithm is a *prompt transform*: prepend the original query once more with an optional instruction (e.g., “Read the question again:”). In a decoder-only Transformer the second copy’s tokens can now attend to subsequent tokens from the first copy, approximating bidirectional attention without architecture changes. The technique is model-agnostic, requires no gradient updates, and relies solely on the native self-attention mechanism. Theoretical framing connects RE2 to query-augmentation in retrieval and to multi-glance reading models in NLP.", "methodName": "Legacy AI Techniques Summary", "simplifiedExplanation": "This entry represents older, less structured method information.", "prerequisites": "N/A", "stepByStepGuide": "N/A", "practicalExample": {"scenarioDescription": "N/A", "implementationCode": "N/A", "expectedOutcome": "N/A"}}, {"paperTitle": "Self-Taught Optimizer (STOP): Recursively Self-Improving Code Generation", "paperSlug": "Self_Taught_Optimizer_STOP", "methodsContent": "STOP frames the generation of code-solvers as an optimisation problem and leverages modern large-language-model (LLM) sampling to explore the search space. Key algorithms include beam search that retains a top-k frontier of programme mutations, genetic algorithms that rely on LLM-created crossover/mutation operations, and simulated annealing that maps temperature decay to model sampling temperature. The improver is itself expressed in Python, allowing arbitrary meta-heuristics to be composed with calls to GPT-4. The work also formalises the maximiser variant and provides PAC-style generalisation bounds for the meta-utility estimator.", "methodName": "Legacy AI Techniques Summary", "simplifiedExplanation": "This entry represents older, less structured method information.", "prerequisites": "N/A", "stepByStepGuide": "N/A", "practicalExample": {"scenarioDescription": "N/A", "implementationCode": "N/A", "expectedOutcome": "N/A"}}, {"paperTitle": "Summarization is (Almost) Dead", "paperSlug": "Summarization_is_Almost_Dead", "methodsContent": "LLMs are treated as few/zero-shot generators with minimal prompt engineering (e.g., \"Please write a concise summary of …\"). Core algorithms therefore rely on GPT-3, GPT-3.5, and GPT-4’s transformer-decoder architecture. The methodological novelty lies in the evaluation setup: brand-new post-cut-off datasets, exhaustive pair-wise human preference scoring, and hallucination annotation split into intrinsic vs. extrinsic categories. While no new model architecture is proposed, the study demonstrates how to leverage existing foundation models for accurate summarization without fine-tuning.", "methodName": "Legacy AI Techniques Summary", "simplifiedExplanation": "This entry represents older, less structured method information.", "prerequisites": "N/A", "stepByStepGuide": "N/A", "practicalExample": {"scenarioDescription": "N/A", "implementationCode": "N/A", "expectedOutcome": "N/A"}}, {"paperTitle": "Training Language Models with <PERSON><PERSON>s", "paperSlug": "Think_before_you_speak", "methodsContent": "Pause-training inserts a learnable <pause> sentinel outside the base vocabulary. During pre-training, the token is randomly injected (≈10 % of sequence length) and its prediction loss is excluded. At fine-tuning the developer chooses a fixed delay Mft; inference mirrors that delay (Minf). Each pause increases parallel attention computations (K→K+M) without parameter bloat—only one embedding vector is added. The method exploits latent representational capacity in self-attention, hypothesizing that wider computational width yields richer token interactions. Core algorithms remain standard Transformer blocks, ensuring compatibility with existing frameworks.", "methodName": "Legacy AI Techniques Summary", "simplifiedExplanation": "This entry represents older, less structured method information.", "prerequisites": "N/A", "stepByStepGuide": "N/A", "practicalExample": {"scenarioDescription": "N/A", "implementationCode": "N/A", "expectedOutcome": "N/A"}}, {"paperTitle": "TreePrompt: Learning to Compose Tree Prompts for Explainable Visual Grounding", "paperSlug": "TreePrompt", "methodsContent": "TreePrompt’s core algorithm decomposes prompt learning into *tree-structured composition*. A dependency parser converts each query sentence into a directed tree. Three parameter-efficient MLP-based modules (<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>) generate  **dp**-dimensional prompt fragments for their respective token classes, after L2-normalising concatenated word, POS and dependency-label embeddings. Child prompts are averaged and fed upward, enabling hierarchical aggregation. This design yields O(L·dp) parameters versus O(L·N·dp) for multi-layer prompts and affords node-level inspection of activations.", "methodName": "Legacy AI Techniques Summary", "simplifiedExplanation": "This entry represents older, less structured method information.", "prerequisites": "N/A", "stepByStepGuide": "N/A", "practicalExample": {"scenarioDescription": "N/A", "implementationCode": "N/A", "expectedOutcome": "N/A"}}, {"paperTitle": "MEMWALKER: Walking Down the Memory Maze Beyond the Context Window", "paperSlug": "Walking_Through_The_Memory_Maze_Beyond", "methodsContent": "MEMWALKER’s core algorithm combines hierarchical summarization with agent-style tree traversal. Using zero-shot prompts, the LLM first condenses fixed-size text chunks (500–1 200 tokens) into leaf summaries, then recursively summarizes groups (fan-out 5–8) until a single root is produced. At query time, a triage prompt displays child summaries; the LLM reasons in natural language, outputs an action (enter, revert, commit), and optionally an answer. This interaction is framed as sampling from p(reason, action|summary, query). Reversible moves enable backtracking, mitigating local-choice errors. A working-memory buffer concatenates previously visited content, refreshed each hop to fit the 4 k context window. No finetuning is required—only prompt engineering and top-p sampling.", "methodName": "Legacy AI Techniques Summary", "simplifiedExplanation": "This entry represents older, less structured method information.", "prerequisites": "N/A", "stepByStepGuide": "N/A", "practicalExample": {"scenarioDescription": "N/A", "implementationCode": "N/A", "expectedOutcome": "N/A"}}, {"paperTitle": "Why think step by step? Reasoning emerges from the locality of experience", "paperSlug": "Why_think_step_by_step", "methodsContent": "The core technical contribution is a formal bias-reduction proof for autoregressive density estimators trained on local samples of a chain-structured Bayes net. The proof shows that the empirical risk minimiser under cross-entropy with entropy regularisation interpolates between true local conditionals and the uniform distribution, leading to a *reasoning gap* for non-adjacent variables. The experimental section instantiates this theory with 100-variable Bay<PERSON> nets, variational observation distributions, and GPT-2–style transformers. This constitutes a concrete algorithmic recipe (synthetic graph generation → local sampling → CoT inference) that can be re-implemented by practitioners.", "methodName": "Legacy AI Techniques Summary", "simplifiedExplanation": "This entry represents older, less structured method information.", "prerequisites": "N/A", "stepByStepGuide": "N/A", "practicalExample": {"scenarioDescription": "N/A", "implementationCode": "N/A", "expectedOutcome": "N/A"}}, {"paperTitle": "Prompt Design and Engineering: Introduction and Advanced Methods", "paperSlug": "<PERSON><PERSON><PERSON><PERSON>in_Prompt_Design_and_Engineering_Introduction_and_Advanced_Methods", "methodsContent": "The paper catalogues a comprehensive set of prompt-engineering algorithms.  It contrasts Zero-shot vs. Manual CoT, introduces ToT as a branching search, and explains self-consistency, reflection, expert prompting, rails, and APE.  Technical insight focuses on why each algorithm reduces hallucination or improves reasoning by forcing the model to externalize intermediate thoughts or incorporate external knowledge.  Citations to <PERSON> et al. (CoT), <PERSON> et al. (ToT), and <PERSON> et al. (APE) ground the discussion.\n\nImplementation detail is conveyed through schematic figures and sample prompt templates such as \"Q: <repeat_question> Use this format: Let's think step by step…\" or \"<|endofprompt|>\" tokens, giving readers a concrete starting point.", "methodName": "Legacy AI Techniques Summary", "simplifiedExplanation": "This entry represents older, less structured method information.", "prerequisites": "N/A", "stepByStepGuide": "N/A", "practicalExample": {"scenarioDescription": "N/A", "implementationCode": "N/A", "expectedOutcome": "N/A"}}]