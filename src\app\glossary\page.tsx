'use client';

import React, { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import {
  BookOpenIcon,
  MagnifyingGlassIcon,
  AcademicCapIcon,
  CircleStackIcon,
  ComputerDesktopIcon,
  CpuChipIcon,
  CubeIcon,
  BeakerIcon,
  LightBulbIcon
} from '@heroicons/react/24/outline';
import { GlossaryTerm, glossaryTerms } from '@/data/glossaryData';

const GlossaryPage = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeCategory, setActiveCategory] = useState<'all' | 'ai' | 'ml' | 'audio' | 'general'>('all');
  const [filteredTerms, setFilteredTerms] = useState<GlossaryTerm[]>(glossaryTerms);
  const searchInputRef = useRef<HTMLInputElement>(null);
  
  // Filter terms based on search query and active category
  useEffect(() => {
    let filtered = [...glossaryTerms];
    
    // Filter by category
    if (activeCategory !== 'all') {
      filtered = filtered.filter(term => term.category === activeCategory);
    }
    
    // Filter by search query
    if (searchQuery.trim()) {
      const lowerCaseQuery = searchQuery.toLowerCase();
      filtered = filtered.filter(term => 
        term.term.toLowerCase().includes(lowerCaseQuery) || 
        (term.acronym && term.acronym.toLowerCase().includes(lowerCaseQuery)) ||
        term.definition.toLowerCase().includes(lowerCaseQuery)
      );
    }
    
    // Sort alphabetically
    filtered.sort((a, b) => a.term.localeCompare(b.term));
    
    setFilteredTerms(filtered);
  }, [searchQuery, activeCategory]);
  
  // Focus search input on page load
  useEffect(() => {
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, []);
  
  // Get category icon
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'ai':
        return <CircleStackIcon className="h-5 w-5 text-blue-500" />;
      case 'ml':
        return <CpuChipIcon className="h-5 w-5 text-purple-500" />;
      case 'audio':
        return <ComputerDesktopIcon className="h-5 w-5 text-green-500" />;
      case 'general':
        return <AcademicCapIcon className="h-5 w-5 text-gray-500" />;
      default:
        return <CubeIcon className="h-5 w-5 text-gray-500" />;
    }
  };
  
  // Get category name
  const getCategoryName = (category: string) => {
    switch (category) {
      case 'ai':
        return 'Artificial Intelligence';
      case 'ml':
        return 'Machine Learning';
      case 'audio':
        return 'Audio Technology';
      case 'general':
        return 'General Concepts';
      default:
        return 'Uncategorized';
    }
  };
  
  // Get category color
  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'ai':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 border-blue-200 dark:border-blue-800';
      case 'ml':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300 border-purple-200 dark:border-purple-800';
      case 'audio':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300 border-green-200 dark:border-green-800';
      case 'general':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300 border-gray-200 dark:border-gray-800';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300 border-gray-200 dark:border-gray-800';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-black font-roboto">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Navigation */}
        <div className="mb-8 font-sans">
          <Link 
            href="/" 
            className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 flex items-center group transition-colors"
          >
            <span className="mr-1 transform group-hover:-translate-x-1 transition-transform">←</span> Back to Home
          </Link>
        </div>

        {/* Title Section */}
        <div className="mb-12 font-merriweather">
          <div className="flex items-center mb-4">
            <BookOpenIcon className="h-10 w-10 text-blue-600 dark:text-blue-400 mr-3" />
            <h1 className="text-4xl font-extrabold text-gray-900 dark:text-white tracking-tight sm:text-5xl">Glossary of Terms</h1>
          </div>
          <p className="text-xl text-gray-500 dark:text-gray-400 max-w-3xl">
            Key terminology used throughout this research, with definitions tailored for audio technology and AI contexts.
          </p>
        </div>

        {/* Search and Filter Section */}
        <div className="mb-10 p-6 bg-white dark:bg-zinc-900 rounded-xl shadow-lg font-sans">
          {/* Search Input */}
          <div className="mb-6">
            <div className="relative flex-grow max-w-md">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
              </div>
              <input
                ref={searchInputRef}
                type="text"
                className="w-full px-4 py-3 pl-10 rounded-lg border border-gray-300 dark:border-zinc-700 focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 dark:bg-zinc-800 dark:text-white transition-shadow shadow-sm focus:shadow-md outline-none"
                placeholder="Search terms, acronyms, or definitions..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>

          {/* Category Filters */}
          <div>
            <h3 className="text-sm font-semibold text-gray-500 dark:text-gray-400 mb-3">Filter by Category:</h3>
            <div className="flex flex-wrap gap-2">
              <button
                onClick={() => setActiveCategory('all')}
                className={`px-3 py-1 rounded-full text-sm font-medium border ${
                  activeCategory === 'all' 
                    ? 'bg-blue-600 text-white dark:bg-blue-500 border-blue-600 dark:border-blue-500' 
                    : 'bg-white text-gray-700 dark:bg-zinc-800 dark:text-gray-300 border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-zinc-700'
                }`}
              >
                All Terms
              </button>
              <button
                onClick={() => setActiveCategory('ai')}
                className={`px-3 py-1 rounded-full text-sm font-medium border ${ 
                  activeCategory === 'ai' 
                    ? 'bg-sky-600 text-white dark:bg-sky-500 border-sky-600 dark:border-sky-500' 
                    : 'bg-white text-gray-700 dark:bg-zinc-800 dark:text-gray-300 border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-zinc-700'
                }`}
              >
                <span className="flex items-center">
                  <CircleStackIcon className="h-4 w-4 mr-1" />
                  AI
                </span>
              </button>
              <button
                onClick={() => setActiveCategory('ml')}
                className={`px-3 py-1 rounded-full text-sm font-medium border ${ 
                  activeCategory === 'ml' 
                    ? 'bg-purple-600 text-white dark:bg-purple-500 border-purple-600 dark:border-purple-500' 
                    : 'bg-white text-gray-700 dark:bg-zinc-800 dark:text-gray-300 border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-zinc-700'
                }`}
              >
                <span className="flex items-center">
                  <CpuChipIcon className="h-4 w-4 mr-1" />
                  Machine Learning
                </span>
              </button>
              <button
                onClick={() => setActiveCategory('audio')}
                className={`px-3 py-1 rounded-full text-sm font-medium border ${ 
                  activeCategory === 'audio' 
                    ? 'bg-green-600 text-white dark:bg-green-500 border-green-600 dark:border-green-500' 
                    : 'bg-white text-gray-700 dark:bg-zinc-800 dark:text-gray-300 border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-zinc-700'
                }`}
              >
                <span className="flex items-center">
                  <ComputerDesktopIcon className="h-4 w-4 mr-1" />
                  Audio Tech
                </span>
              </button>
               <button
                onClick={() => setActiveCategory('general')}
                className={`px-3 py-1 rounded-full text-sm font-medium border ${ 
                  activeCategory === 'general' 
                    ? 'bg-slate-600 text-white dark:bg-slate-500 border-slate-600 dark:border-slate-500' 
                    : 'bg-white text-gray-700 dark:bg-zinc-800 dark:text-gray-300 border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-zinc-700'
                }`}
              >
                <span className="flex items-center">
                  <AcademicCapIcon className="h-4 w-4 mr-1" />
                  General Concepts
                </span>
              </button>
            </div>
          </div>
        </div>

        {/* Glossary Terms */}
        <div className="space-y-6">
          {filteredTerms.length > 0 ? (
            filteredTerms.map((term, index) => (
              <div 
                key={index} 
                className="bg-white dark:bg-zinc-900 rounded-lg shadow-sm dark:shadow-zinc-800/30 overflow-hidden border border-gray-200 dark:border-gray-700"
              >
                <div className="p-6">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center">
                      {getCategoryIcon(term.category)}
                      <h2 className="ml-2 text-xl font-semibold text-gray-900 dark:text-white font-merriweather">
                        {term.term}
                        {term.acronym && (
                          <span className="ml-2 text-sm font-normal text-gray-500 dark:text-gray-400 font-sans">
                            ({term.acronym})
                          </span>
                        )}
                      </h2>
                    </div>
                    <span 
                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getCategoryColor(term.category)} font-sans`}
                    >
                      {getCategoryName(term.category)}
                    </span>
                  </div>
                  <p className="text-gray-700 dark:text-gray-300 mb-4 font-roboto">
                    {term.definition}
                  </p>
                  {term.relatedTerms && term.relatedTerms.length > 0 && (
                    <div className="mt-3">
                      <p className="text-sm text-gray-500 dark:text-gray-400 flex items-center font-roboto">
                        <LightBulbIcon className="h-4 w-4 mr-1" />
                        Related terms:
                      </p>
                      <div className="flex flex-wrap gap-2 mt-1 font-sans">
                        {term.relatedTerms.map((relatedTerm, idx) => (
                          <span
                            key={idx}
                            className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200"
                          >
                            {relatedTerm}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-12 bg-white dark:bg-zinc-900 rounded-lg shadow-sm dark:shadow-zinc-800/30 border border-gray-200 dark:border-gray-700 font-merriweather">
              <BeakerIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-lg font-medium text-gray-900 dark:text-gray-100">No terms found</h3>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Try adjusting your search query or category filter.
              </p>
            </div>
          )}
        </div>
        
        <footer className="mt-16 pt-8 border-t border-gray-200 dark:border-zinc-700/80 text-center text-gray-500 dark:text-gray-400 font-sans">
          <p>&copy; {new Date().getFullYear()} Thesis Narrative Site. All terms compiled for educational purposes.</p>
        </footer>
      </div>
    </div>
  );
};

export default GlossaryPage;
