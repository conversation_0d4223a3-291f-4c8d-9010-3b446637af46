'use client';

interface TooltipProps {
  text: string;
  children: React.ReactNode;
}

export default function Tooltip({ text, children }: TooltipProps) {
  return (
    <span className="relative group inline">
      {children}
      <span className="absolute z-[9999] invisible group-hover:visible opacity-0 group-hover:opacity-100 transition-opacity duration-200
                    bg-white dark:bg-zinc-900 text-gray-900 dark:text-gray-100 p-2 rounded-md shadow-lg dark:shadow-zinc-800/30
                    text-sm w-64 left-0 top-full mt-2 inline-block border border-gray-200 dark:border-gray-700">
        <span className="absolute -top-2 left-4 w-4 h-4 bg-white dark:bg-zinc-900 border-l border-t border-gray-200 dark:border-gray-700 transform rotate-45"></span>
        {text}
      </span>
    </span>
  );
}
