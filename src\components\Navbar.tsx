'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { SparklesIcon, InformationCircleIcon, EyeIcon, UserIcon } from '@heroicons/react/24/outline';
import SettingsMenu from '@/components/SettingsMenu'; 
import React, { useState, useRef, useEffect } from 'react'; 

interface NavLink {
  name: string;
  href: string;
  author: 'human' | 'ai' | 'both';
  subLinks?: NavLink[]; // Optional: for dropdown items
}

// Helper function to interpolate colors
const interpolateColor = (percentage: number, color1: [number, number, number], color2: [number, number, number]): string => {
  const r = Math.round(color1[0] + percentage * (color2[0] - color1[0]));
  const g = Math.round(color1[1] + percentage * (color2[1] - color1[1]));
  const b = Math.round(color1[2] + percentage * (color2[2] - color1[2]));
  return `rgb(${r}, ${g}, ${b})`;
};

const START_COLOR_RGB: [number, number, number] = [96, 165, 250]; // Tailwind blue-400
const END_COLOR_RGB: [number, number, number] = [192, 132, 252];   // Tailwind purple-400

const navLinks: NavLink[] = [
  { name: 'Home', href: '/', author: 'human' },
  { name: 'Introduction', href: '/introduction', author: 'human' },
  {
    name: 'Critical Review',
    href: '/critical-review',
    author: 'both'
  },
  { name: 'Results', href: '/results-human', author: 'human' }, 
  { name: 'Reflection', href: '/reflection', author: 'human' },
  { name: 'Ethics', href: '/ethics', author: 'human' },
  { name: 'Glossary', href: '/glossary', author: 'ai' },
];

export default function Navbar() {
  const pathname = usePathname();
  const [showInfoTooltip, setShowInfoTooltip] = useState(false);

  const linksContainerRef = useRef<HTMLDivElement>(null);
  const linkItemRefs = useRef<(HTMLDivElement | null)[]>([]);
  const [iconColors, setIconColors] = useState<Record<string, string>>({});

  useEffect(() => {
    const calculateColors = () => {
      if (!linksContainerRef.current) return;

      const containerRect = linksContainerRef.current.getBoundingClientRect();
      const newColors: Record<string, string> = {};

      linkItemRefs.current.forEach((itemElement, index) => {
        if (itemElement) {
          const link = navLinks[index]; // navLinks is stable from outer scope
          const itemRect = itemElement.getBoundingClientRect();
          
          // Calculate center of the item relative to the container's left edge
          const itemCenterXPx = (itemRect.left - containerRect.left) + (itemRect.width / 2);
          
          let percentage = itemCenterXPx / containerRect.width;
          percentage = Math.max(0, Math.min(1, percentage)); // Clamp between 0 and 1

          newColors[link.name] = interpolateColor(percentage, START_COLOR_RGB, END_COLOR_RGB);
        }
      });
      setIconColors(newColors);
    };

    // Initial calculation after layout is stable
    const rafId = requestAnimationFrame(calculateColors);
    
    window.addEventListener('resize', calculateColors);

    return () => {
      cancelAnimationFrame(rafId);
      window.removeEventListener('resize', calculateColors);
    };
  }, [pathname]); // Recalculate if pathname changes (e.g. active link styling changes affecting layout)

  return (
    <nav data-no-glossary className="fixed top-0 w-full bg-gradient-to-r from-blue-400 to-purple-400 z-50 h-16">
      <div className="relative w-full h-full bg-white dark:bg-zinc-900 shadow-sm dark:shadow-zinc-800/30">
        {/* Links are contained and spaced within this centered, max-width container */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 h-full">
          <div className="flex h-full items-center justify-between" ref={linksContainerRef}> {/* This will space out the links */}
            {navLinks.map((link, index) => {
              const isParentActive = pathname === link.href;
              const isSublinkActive = link.subLinks?.some(subLink => pathname === subLink.href);
              const isActive = isParentActive || isSublinkActive;

              return (
                <div 
                  key={link.name} 
                  className="h-full flex items-center relative group"
                  ref={el => { linkItemRefs.current[index] = el; }}
                >
                  <Link 
                    href={link.href} 
                    className={`flex items-center justify-center px-1 font-medium transition-colors text-center h-full ${isActive ? 'text-blue-600 dark:text-blue-400 font-semibold' : 'text-gray-900 dark:text-gray-100 hover:text-blue-600 dark:hover:text-blue-400'}`}
                  >
                    <span className="flex items-center">
                      {link.name}
                      {link.author === 'ai' && (
                        <SparklesIcon className={`ml-2 h-5 w-5`} style={{ color: iconColors[link.name] }} />
                      )}
                      {link.author === 'human' && (
                        <UserIcon className={`ml-2 h-5 w-5`} style={{ color: iconColors[link.name] }} />
                      )}
                      {link.author === 'both' && !link.subLinks && (
                        <>
                          <UserIcon className={`ml-2 h-5 w-5`} style={{ color: iconColors[link.name] }} />
                          <SparklesIcon className={`ml-1 h-5 w-5`} style={{ color: iconColors[link.name] }} />
                        </>
                      )}
                      {/* Specific icon handling for Critical Review parent with dropdown */}
                      {link.name === 'Critical Review' && link.subLinks && (
                         <>
                          <UserIcon className={`ml-2 h-5 w-5`} style={{ color: iconColors[link.name] }} />
                          <SparklesIcon className={`ml-1 h-5 w-5`} style={{ color: iconColors[link.name] }} />
                        </>
                      )}
                    </span>
                  </Link>
                  {/* Dropdown Menu */}
                  {link.subLinks && (
                    <div className="absolute top-full left-0 mt-0 hidden group-hover:block w-full bg-white dark:bg-zinc-900 shadow-lg rounded-b-md py-1 z-[51] border-x border-b border-gray-200 dark:border-zinc-700 border-t-white dark:border-t-zinc-900">
                      {link.subLinks.map((subLink) => {
                        const isCurrentSublinkActive = pathname === subLink.href;
                        return (
                          <Link
                            key={subLink.name}
                            href={subLink.href}
                            className={`block w-full text-left px-4 py-2.5 text-sm whitespace-nowrap ${isCurrentSublinkActive ? 'bg-blue-50 dark:bg-blue-900/50 text-blue-600 dark:text-blue-300 font-semibold' : 'text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-zinc-700 hover:text-gray-900 dark:hover:text-gray-50'}`}
                          >
                            <span className="flex items-center">
                              {subLink.name}
                              {subLink.author === 'ai' && (
                                <SparklesIcon className={`ml-2 h-5 w-5 text-red-500`} />
                              )}
                              {subLink.author === 'human' && (
                                <UserIcon className={`ml-2 h-5 w-5 text-red-500`} />
                              )}
                              {/* 'both' author for sublinks can be added here if needed */}
                            </span>
                          </Link>
                        );
                      })}
                    </div>
                  )}
                  <span className={`absolute inset-x-0 bottom-0 h-1 bg-blue-500 dark:bg-blue-600 transform origin-left transition-transform ${isActive ? 'scale-x-100' : 'scale-x-0 group-hover:scale-x-100'}`}></span>
                </div>
              );
            })}
          </div>
        </div>

        {/* Icons container - absolutely positioned to the far right */}
        <div className="absolute right-0 top-0 h-full flex items-center pr-4 sm:pr-6 lg:pr-8">
          <div className="flex items-center space-x-3">
            {/* Information Icon with Tooltip */}
            <div 
              className="relative"
              onMouseEnter={() => setShowInfoTooltip(true)}
              onMouseLeave={() => setShowInfoTooltip(false)}
            >
              <button className="p-1 rounded-full text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-zinc-700 hover:text-gray-700 dark:hover:text-gray-200 focus:outline-none">
                <InformationCircleIcon className="h-6 w-6" />
              </button>
              {showInfoTooltip && (
                <div className="absolute top-full right-0 mt-2 w-80 bg-white dark:bg-zinc-800 border border-gray-200 dark:border-zinc-700 rounded-md shadow-lg p-4 z-50">
                  <h3 className="text-md font-semibold text-gray-800 dark:text-gray-100 mb-2 flex items-center">
                    <EyeIcon className="h-5 w-5 mr-1 flex-shrink-0" />
                    Interpreting the Content
                  </h3>
                  <p className="text-xs text-gray-700 dark:text-gray-300 mb-2">
                    To ensure clarity about the origins of content, the site uses visual cues:
                  </p>
                  <ul className="list-none space-y-1 text-xs">
                    <li className="flex items-start">
                      <strong className="font-merriweather text-purple-600 dark:text-purple-300 mr-1 flex-shrink-0">Merriweather Font:</strong> <span className="text-gray-600 dark:text-gray-400">Indicates human-written text.</span>
                    </li>
                    <li className="flex items-center">
                      <UserIcon className="inline-block h-5 w-5 text-yellow-500 dark:text-yellow-400 mr-1.5 flex-shrink-0" />
                      <strong className="text-gray-700 dark:text-gray-200 mr-1">Person Icon</strong>
                      <span className="text-gray-600 dark:text-gray-400">Indicates content is human-written.</span>
                    </li>
                    <li className="flex items-start">
                      <strong className="font-roboto text-teal-600 dark:text-teal-300 mr-1 flex-shrink-0">Roboto Font:</strong> <span className="font-roboto text-gray-600 dark:text-gray-400">Indicates AI-generated text.</span>
                    </li>
                    <li className="flex items-center">
                      <SparklesIcon className="inline-block h-5 w-5 text-amber-500 dark:text-yellow-400 mx-1 flex-shrink-0" /> 
                      <strong className="text-gray-700 dark:text-gray-200 mr-1">Sparkles Icon</strong> <span className="text-gray-600 dark:text-gray-400">Indicates content is AI generated.</span>
                    </li>
                  </ul>
                  <p className="text-xs text-gray-700 dark:text-gray-300 mt-3">
                    At the top right of the page, there is also an information icon. Hovering over this will display the above information so that it is accessible on any page of the website. <br /><br /> Clarifying further, it means for instance that all text on this page is hand written by the author.
                  </p>
                </div>
              )}
            </div>
            <SettingsMenu />
          </div>
        </div>
        {/* Main Gradient Bar */}
        <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-400 to-purple-400"></div>
      </div>
    </nav>
  );
}
