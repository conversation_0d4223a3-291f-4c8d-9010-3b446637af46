'use client';

import React from 'react';
import { useModelContext, ModelType } from '@/context/ModelContext';

// Model display names and descriptions
const MODEL_INFO: Record<ModelType, { name: string; description: string }> = {
  'o3': { 
    name: 'o3', 
    description: 'OpenAI\'s o3 model' 
  },
  'Gemini': { 
    name: 'Gemini', 
    description: 'Google\'s Gemini Pro 2.5 model' 
  },
  'Sonnet': { 
    name: 'Sonnet', 
    description: 'Anthropic\'s Sonnet 3.7 Thinking model' 
  },
  'legacy': { 
    name: 'Legacy', 
    description: 'Original evaluations' 
  }
};

export default function ModelSelector() {
  const { selectedModel, setSelectedModel, availableModels, isModelSelectorReady } = useModelContext();

  // Don't render anything if not ready or only one model is available
  if (!isModelSelectorReady || availableModels.length <= 1) {
    return null;
  }

  return (
    <div className="flex items-center">
      <div className="relative" title={MODEL_INFO[selectedModel]?.description || ''}>
        <div className="flex items-center text-xs text-gray-700 dark:text-gray-300 mr-1">
          AI Model:
        </div>
        <select
          id="model-selector"
          value={selectedModel}
          onChange={(e) => setSelectedModel(e.target.value as ModelType)}
          className="block w-20 pl-1 pr-6 py-1 text-xs border-gray-300 dark:border-gray-700 focus:outline-none focus:ring-sky-500 focus:border-sky-500 rounded-md bg-white dark:bg-zinc-800 text-gray-900 dark:text-gray-100"
          title={MODEL_INFO[selectedModel]?.description || ''}
        >
          {availableModels.map((model) => (
            <option key={model} value={model}>
              {MODEL_INFO[model]?.name || model}
            </option>
          ))}
        </select>
        <div className="absolute inset-y-0 right-0 flex items-center pr-1 pointer-events-none">
          <svg className="h-3 w-3 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
        </div>
      </div>
    </div>
  );
}
