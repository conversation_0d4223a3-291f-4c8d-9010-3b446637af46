import React, { useState } from 'react';
import Link from 'next/link';
import { ChevronDownIcon, ChevronUpIcon } from '@heroicons/react/24/outline';
import DirectGlossaryTooltip from '@/components/DirectGlossaryTooltip';

interface CollapsiblePaperCardProps {
  title: string;
  slug: string;
  summary?: string;
  children: React.ReactNode;
  defaultExpanded?: boolean;
  onExpandChange?: (expanded: boolean, slug: string) => void;
  titleClassName?: string;
}

const CollapsiblePaperCard: React.FC<CollapsiblePaperCardProps> = ({
  title,
  slug,
  summary,
  children,
  defaultExpanded = false,
  onExpandChange,
  titleClassName,
}) => {
  const [expanded, setExpanded] = useState(defaultExpanded);

  return (
    <div className="bg-white dark:bg-zinc-800/50 rounded-lg shadow-md border border-gray-200 dark:border-gray-700/50 transition-all hover:shadow-lg mb-4">
      {/* Gradient Top Border - Always Visible */}
      <div className="h-1 w-full bg-gradient-to-r from-purple-400 to-blue-400 rounded-t-lg"></div>

      <div className="p-4 sm:p-5">
        {/* Card Header */}
        <div className="flex justify-between items-start">
          <div className="flex-1">
            <Link href={`/critical-review/source-material/${slug}`} className="hover:underline">
              <h3 className={titleClassName || "text-lg font-bold text-gray-900 dark:text-gray-100 line-clamp-1"}>
                <DirectGlossaryTooltip>{title}</DirectGlossaryTooltip>
              </h3>
            </Link>
            {summary && !expanded && (
              <p className="mt-1 text-gray-600 dark:text-gray-300 text-sm line-clamp-2">{summary}</p>
            )}
          </div>

          {/* Expand/Collapse Button */}
          <button
            onClick={() => {
              const newExpandedState = !expanded;
              setExpanded(newExpandedState);
              if (onExpandChange) {
                onExpandChange(newExpandedState, slug);
              }
            }}
            className="ml-4 p-1 rounded-full text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none flex-shrink-0"
            aria-expanded={expanded}
            aria-label={expanded ? "Collapse" : "Expand"}
          >
            {expanded ? (
              <ChevronUpIcon className="h-5 w-5" />
            ) : (
              <ChevronDownIcon className="h-5 w-5" />
            )}
          </button>
        </div>

        {/* Collapsible Content */}
        {expanded && (
          <div className="mt-4 border-t border-gray-200 dark:border-gray-700 pt-4">
            {children}
          </div>
        )}
      </div>
    </div>
  );
};

export default CollapsiblePaperCard;
