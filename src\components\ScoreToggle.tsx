import React from 'react';
import { Switch } from '@headlessui/react';

interface ScoreToggleProps {
  showAverageScores: boolean;
  setShowAverageScores: (show: boolean) => void;
}

const ScoreToggle: React.FC<ScoreToggleProps> = ({ 
  showAverageScores, 
  setShowAverageScores 
}) => {
  return (
    <div className="flex items-center space-x-3">
      <span className={`text-sm font-medium ${!showAverageScores ? 'text-gray-900 dark:text-white' : 'text-gray-500 dark:text-gray-400'}`}>
        Model Scores
      </span>
      <Switch
        checked={showAverageScores}
        onChange={setShowAverageScores}
        className={`${
          showAverageScores ? 'bg-blue-600' : 'bg-gray-200 dark:bg-gray-700'
        } relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2`}
      >
        <span
          className={`${
            showAverageScores ? 'translate-x-6' : 'translate-x-1'
          } inline-block h-4 w-4 transform rounded-full bg-white transition-transform`}
        />
      </Switch>
      <span className={`text-sm font-medium ${showAverageScores ? 'text-gray-900 dark:text-white' : 'text-gray-500 dark:text-gray-400'}`}>
        Average Scores
      </span>
    </div>
  );
};

export default ScoreToggle;
