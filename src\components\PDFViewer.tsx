'use client';

interface PDFViewerProps {
  url: string;
}

export default function PDFViewer({ url }: PDFViewerProps) {
  return (
    <div className="w-full h-full">
      <object
        data={url}
        type="application/pdf"
        className="w-full h-full"
      >
        <div className="flex items-center justify-center h-full">
          <p className="text-gray-500">
            PDF cannot be displayed. <a href={url} className="text-blue-500 hover:underline" target="_blank" rel="noopener noreferrer">Click here to open in new tab</a>
          </p>
        </div>
      </object>
    </div>
  );
}
