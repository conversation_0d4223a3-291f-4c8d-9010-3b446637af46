'use client';

import React, { useEffect, useState, useRef } from 'react';
import {
  ArrowPathIcon,
  BeakerIcon,
  ExclamationTriangleIcon,
  DocumentTextIcon,
  CommandLineIcon,
  LightBulbIcon,
  CheckCircleIcon,
  ClipboardDocumentListIcon,
  CodeBracketIcon,
  AcademicCapIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline';
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { dracula } from 'react-syntax-highlighter/dist/cjs/styles/prism';
import { useModelContext } from '@/context/ModelContext';
import CollapsiblePaperCard from '@/components/CollapsiblePaperCard';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import PaperNavigation from '@/components/PaperNavigation';
import InlineGlossaryTooltip from '@/components/InlineGlossaryTooltip';
import GlossaryEnhancedContent from '@/components/GlossaryEnhancedContent';

interface MethodEntry {
  paperTitle: string;
  paperSlug: string;
  fileBasedSlug?: string; // Add file-based slug for matching with URL parameters
  methodName: string;
  simplifiedExplanation?: string;
  prerequisites?: string[];
  stepByStepGuide?: string | string[];
  practicalExample?: {
    scenarioDescription?: string;
    implementationCode?: string;
    expectedOutcome?: string;
  };
  methodsContent?: string;
}

const MethodsPage = () => {
  const [methodsData, setMethodsData] = useState<MethodEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  // Explicitly initialize expandedCards to an empty set
  const [expandedCards, setExpandedCards] = useState<Set<string>>(new Set());
  const { selectedModel } = useModelContext();
  const searchParams = useSearchParams();
  const paperParam = searchParams.get('paper');
  const forceExpand = searchParams.get('forceExpand') === 'true';

  // Debug log for expandedCards state
  useEffect(() => {
    console.log('Methods page - Current expandedCards:', Array.from(expandedCards));
  }, [expandedCards]);

  // If a paper is specified in the URL, set it as the search query
  useEffect(() => {
    // Check for search parameter first (contains the paper title)
    const searchParam = searchParams.get('search');
    if (searchParam) {
      setSearchQuery(searchParam);
      console.log('Methods page - Search param detected:', searchParam);
    }
    // If no search parameter but paper parameter exists
    else if (paperParam) {
      // First try to get the stored paper title from localStorage
      const storedPaperTitle = typeof window !== 'undefined' ? localStorage.getItem('currentPaperTitle') : null;

      if (storedPaperTitle) {
        setSearchQuery(storedPaperTitle);
        console.log('Methods page - Using stored paper title for search:', storedPaperTitle);
      } else {
        // Fall back to converting the slug to a display name
        const displayName = paperParam
          .split('_')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' ');
        setSearchQuery(displayName);
        console.log('Methods page - Using formatted paper param for search:', displayName);
      }
      console.log('Methods page - Paper param detected:', paperParam);
    }
  }, [paperParam, searchParams]);

  // Reference to store the button element
  const expandButtonRef = useRef<HTMLButtonElement | null>(null);

  // Initialize expanded cards from URL parameters only when forceExpand is true
  useEffect(() => {
    try {
      console.log('Methods page - forceExpand parameter:', forceExpand);

      // Only expand cards when explicitly requested via forceExpand parameter
      if (paperParam && forceExpand) {
        console.log('Methods page - Paper parameter detected in URL with forceExpand:', paperParam);
        // Expand the card for the paper in the URL
        setExpandedCards(new Set([paperParam]));

        // Store this paper as the last visited paper
        if (typeof window !== 'undefined') {
          localStorage.setItem('lastVisitedPaper', paperParam);
        }
      } else {
        // In all other cases, don't automatically expand any cards
        // This ensures the mini-menu only appears when explicitly requested or when a card is manually expanded
        setExpandedCards(new Set());
      }
    } catch (e) {
      console.error('Error initializing expanded cards:', e);
    }
  }, [paperParam, forceExpand]);

  // We intentionally don't auto-expand cards when the component mounts
  // This ensures the mini-menu only appears when a user explicitly expands content
  // or navigates with forceExpand=true

  // Markdown renderer component with consistent styling
  const MarkdownRenderer = ({ content }: { content: string | undefined }) => {
    if (!content) return null;

    // Special renderer for step-by-step guides
    const StepByStepRenderer = ({ content }: { content: string }) => {
      // Parse the content to extract steps and their descriptions
      const lines = content.split('\n');
      const steps: {number: string; title: string; description: string}[] = [];

      let currentStep: {number: string; title: string; description: string} | null = null;

      // Process each line to identify steps and their descriptions
      lines.forEach(line => {
        // Match patterns like "1. Step Title" or "1. 1. Step Title"
        // Also handle the duplicate numbering pattern like "1. 1. Define Challenging Token Heuristic"
        const stepMatch = line.match(/^\s*(\d+\.)(?:\s+\d+\.)?\s+\*\*([^*]+)\*\*/) ||
                         line.match(/^\s*(\d+\.)\s+(\d+\.)\s+([^\n]+)/) ||
                         line.match(/^\s*(\d+\.)\s+([^\n]+)/);
        if (stepMatch) {
          // If we found a new step and already have a current step, push it to steps array
          if (currentStep) {
            steps.push(currentStep);
          }

          // Extract the step title based on which regex pattern matched
          let number = stepMatch[1];
          let title = '';

          if (stepMatch[0].includes('**')) {
            // This is the first pattern with bold formatting
            title = stepMatch[2];
          } else if (stepMatch[2] && stepMatch[2].match(/\d+\./)) {
            // This is the second pattern with duplicate numbering
            title = stepMatch[3];
          } else {
            // This is the third pattern with simple numbering
            title = stepMatch[2];
          }

          // Start a new step
          currentStep = {
            number: number,
            title: title,
            description: ''
          };
        } else if (currentStep && line.trim() !== '') {
          // This is content for the current step
          currentStep.description += (currentStep.description ? '\n' : '') + line;
        }
      });

      // Don't forget to add the last step
      if (currentStep) {
        steps.push(currentStep);
      }

      // Render the steps with proper formatting
      return (
        <ol className="list-none pl-0 space-y-4 text-gray-700 dark:text-gray-300">
          {steps.map((step, index) => (
            <li key={index} className="mb-4">
              <div className="font-semibold text-gray-800 dark:text-gray-200 mb-2">
                <span className="text-blue-600 dark:text-blue-400">{step.number}</span> <InlineGlossaryTooltip>{step.title}</InlineGlossaryTooltip>
              </div>
              {step.description && (
                <div className="pl-6 text-gray-700 dark:text-gray-300">
                  <InlineGlossaryTooltip>{step.description}</InlineGlossaryTooltip>
                </div>
              )}
            </li>
          ))}
        </ol>
      );
    };

    // Special handling for step-by-step content
    const isStepByStepGuide = content.includes('Step-by-Step Guide') ||
                             content.match(/\d+\.\s+\d+\.\s+/) ||
                             content.match(/^\d+\.\s+/m);

    if (isStepByStepGuide) {
      // For step-by-step guides, use the custom renderer
      return <StepByStepRenderer content={content} />;
    }

    // For regular content, use GlossaryEnhancedContent with custom components
    return (
      <GlossaryEnhancedContent
        content={content}
        className="prose dark:prose-invert max-w-none [&_code]:bg-gray-100 [&_code]:dark:bg-gray-800 [&_code]:px-1 [&_code]:py-0.5 [&_code]:rounded [&_code]:text-sm [&_p]:text-gray-700 [&_p]:dark:text-gray-300 [&_p]:mb-4 [&_ul]:list-disc [&_ul]:pl-5 [&_ul]:mb-4 [&_ul]:text-gray-700 [&_ul]:dark:text-gray-300 [&_ol]:list-decimal [&_ol]:pl-5 [&_ol]:mb-4 [&_ol]:text-gray-700 [&_ol]:dark:text-gray-300 [&_li]:mb-2 [&_strong]:font-semibold [&_strong]:text-gray-800 [&_strong]:dark:text-gray-200 [&_h1]:text-2xl [&_h1]:font-bold [&_h1]:text-gray-900 [&_h1]:dark:text-gray-100 [&_h1]:mb-4 [&_h2]:text-xl [&_h2]:font-semibold [&_h2]:text-gray-800 [&_h2]:dark:text-gray-200 [&_h2]:mb-3 [&_h3]:text-lg [&_h3]:font-medium [&_h3]:text-gray-800 [&_h3]:dark:text-gray-200 [&_h3]:mb-2"
      />
    );
  };

  // Handler for card expansion state changes
  const handleCardExpandChange = (expanded: boolean, slug: string) => {
    console.log(`Methods page - Card expansion change: ${expanded ? 'expanded' : 'collapsed'} for ${slug}`);

    if (expanded) {
      // When expanding a card, clear any previously expanded cards and only show this one
      // This ensures only one card can be expanded at a time
      setExpandedCards(new Set([slug]));

      // Store this paper information in localStorage
      if (typeof window !== 'undefined') {
        localStorage.setItem('lastVisitedPaper', slug);

        // Also store the paper title if we have it
        const paperGroup = filteredPaperGroups.find(group => group.slug === slug);
        if (paperGroup) {
          localStorage.setItem('currentPaperTitle', paperGroup.title);
        }
      }
    } else {
      // When collapsing a card, remove it from the expanded cards set
      // This will hide the mini-menu when all cards are collapsed
      setExpandedCards(prevExpandedCards => {
        const newExpandedCards = new Set(prevExpandedCards);
        newExpandedCards.delete(slug);
        return newExpandedCards;
      });
    }

    console.log('Methods page - Expanded cards after change:', expanded ? [slug] : []);
  };

  // Group methods by paper
  const paperGroups = React.useMemo(() => {
    const groups: Record<string, { title: string; slug: string; methods: MethodEntry[] }> = {};

    methodsData.forEach(method => {
      if (!groups[method.paperSlug]) {
        groups[method.paperSlug] = {
          title: method.paperTitle,
          slug: method.paperSlug,
          methods: []
        };
      }
      groups[method.paperSlug].methods.push(method);
    });

    // Log paper slugs for debugging
    if (paperParam) {
      console.log('Paper groups created with slugs:', Object.values(groups).map(g => g.slug));
      console.log('Looking for paper param:', paperParam);

      // Log file-based slugs for debugging
      const fileBasedSlugs = methodsData.map(m => m.fileBasedSlug).filter(Boolean);
      console.log('Available file-based slugs:', fileBasedSlugs);

      // Check if paperParam matches any file-based slug
      const matchingMethod = methodsData.find(m => m.fileBasedSlug === paperParam);
      if (matchingMethod) {
        console.log('Found matching method with file-based slug:', matchingMethod.paperTitle);
      }
    }

    // Convert the groups object to an array
    return Object.values(groups);
  }, [methodsData, paperParam]);

  // Filter papers and methods based on search query
  const filteredPaperGroups = React.useMemo(() => {
    if (!searchQuery.trim()) return paperGroups;

    const lowerCaseQuery = searchQuery.toLowerCase();

    // If we have a paper parameter, also check for file-based slug matches
    const isPaperParamSearch = !!paperParam && searchQuery.toLowerCase().includes(paperParam.toLowerCase());

    return paperGroups
      .map(group => {
        // Check if this group matches the paper parameter directly
        const groupMatchesPaperParam = isPaperParamSearch && (
          group.slug === paperParam ||
          group.slug.toLowerCase().includes(paperParam.toLowerCase()) ||
          paperParam.toLowerCase().includes(group.slug.toLowerCase())
        );

        // Filter methods within each paper group
        const filteredMethods = group.methods.filter(method => {
          // Check if this method matches by file-based slug
          const methodMatchesByFileSlug = isPaperParamSearch &&
            method.fileBasedSlug && (
              method.fileBasedSlug === paperParam ||
              method.fileBasedSlug.toLowerCase().includes(paperParam.toLowerCase()) ||
              paperParam.toLowerCase().includes(method.fileBasedSlug.toLowerCase())
            );

          // Standard content matching
          const contentMatches =
            method.methodName.toLowerCase().includes(lowerCaseQuery) ||
            (method.simplifiedExplanation?.toLowerCase().includes(lowerCaseQuery) ?? false) ||
            (method.stepByStepGuide?.toString().toLowerCase().includes(lowerCaseQuery) ?? false);

          return methodMatchesByFileSlug || contentMatches;
        });

        // If paper title matches, group matches paper param, or any methods match, include this paper group
        if (group.title.toLowerCase().includes(lowerCaseQuery) ||
            groupMatchesPaperParam ||
            filteredMethods.length > 0) {
          return {
            ...group,
            methods: filteredMethods.length > 0 ? filteredMethods : group.methods
          };
        }

        return null;
      })
      .filter(Boolean) as typeof paperGroups;
  }, [paperGroups, searchQuery]);

  useEffect(() => {
    async function fetchAllMethodsData() {
      setLoading(true);
      setError(null);
      try {
        const responseFilenames = await fetch(`/api/list-evaluations?model=${selectedModel}`);
        if (!responseFilenames.ok) {
          throw new Error(`Failed to fetch evaluation files: ${responseFilenames.statusText}`);
        }
        const data = await responseFilenames.json();
        const files = data.files || [];

        if (files.length === 0) {
          setMethodsData([]);
          console.log(`No evaluation files found for methods with model: ${selectedModel}`);
          return;
        }

        const allMethods: MethodEntry[] = [];
        for (const filePath of files) {
          try {
            // Handle both legacy and new directory structure
            const evaluationPath = filePath.includes('/')
              ? `/papers/evaluations/${filePath}`
              : `/papers/evaluations/${filePath}`;

            const responseEvaluation = await fetch(evaluationPath);
            if (!responseEvaluation.ok) {
              throw new Error(`Failed to fetch evaluation data: ${responseEvaluation.statusText}`);
            }

            let evaluationData;
            try {
              const text = await responseEvaluation.text();
              evaluationData = JSON.parse(text);
            } catch (jsonError) {
              console.error(`Failed to parse JSON from evaluation file ${filePath}:`, jsonError);
              continue;
            }

            // Extract the paper title and slug from the metadata section
            const metadata = evaluationData.metadata || {};
            const paperTitle = metadata.title || 'Unknown Paper';

            // Generate a proper slug for the paper
            let paperSlug = '';
            if (metadata.paperSlug) {
              paperSlug = metadata.paperSlug;
            } else {
              // Extract the paper name from the file path
              const fileName = filePath.replace('.json', '').split('/').pop() || '';

              // Convert from 'Adaptive_Temperature_Sampling' to 'adaptive_temperature_sampling'
              paperSlug = fileName
                .replace(/^\d+_/, '') // Remove number prefix like '1_'
                .replace(/Evaluation_(.*)_Gemini$/, '$1') // Remove Evaluation_ prefix and _Gemini suffix
                .toLowerCase();
            }

            // Store the original file-based slug for matching with URL parameters
            const fileNameSlug = filePath.replace('.json', '').split('/').pop() || '';
            const fileBasedSlug = fileNameSlug
              .replace(/^\d+_/, '') // Remove number prefix like '1_'
              .replace(/Evaluation_(.*)_Gemini$/, '$1') // Remove Evaluation_ prefix and _Gemini suffix;

            // Check if the evaluation has a methodologicalDeepDive section
            if (evaluationData.methodologicalDeepDive) {
              const methodsData = evaluationData.methodologicalDeepDive;

              // If it's a single method, wrap it in an array
              const methods = Array.isArray(methodsData) ? methodsData : [methodsData];

              // Process each method
              methods.forEach(method => {
                if (method && method.methodName) {
                  allMethods.push({
                    paperTitle,
                    paperSlug,
                    fileBasedSlug: fileBasedSlug, // Add the file-based slug
                    methodName: method.methodName,
                    simplifiedExplanation: method.simplifiedExplanation,
                    prerequisites: method.prerequisites,
                    stepByStepGuide: method.stepByStepGuide,
                    practicalExample: method.practicalExample
                  });
                }
              });
            } else {
              console.log(`No methodologicalDeepDive found in ${filePath}`);
            }

          } catch (err) {
            console.error(`Error processing evaluation file ${filePath}:`, err);
          }
        }

        setMethodsData(allMethods);
      } catch (err) {
        console.error('Error fetching methods data:', err);
        setError(`Failed to fetch methods data: ${err instanceof Error ? err.message : String(err)}`);
      } finally {
        setLoading(false);
      }
    }

    fetchAllMethodsData();
  }, [selectedModel]); // Re-fetch when selected model changes

  // Find the selected paper if a paper parameter is provided
  const selectedPaper = paperParam && filteredPaperGroups.find(group => group.slug === paperParam);

  // Debug information to help identify why cards aren't expanding
  useEffect(() => {
    if (paperParam) {
      console.log('Methods page - Paper param:', paperParam);
      console.log('Methods page - Available groups:', filteredPaperGroups.map(g => g.slug));
      console.log('Methods page - Selected paper:', selectedPaper ? selectedPaper.slug : 'none');
    }
  }, [paperParam, filteredPaperGroups, selectedPaper]);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-black font-roboto">
      {/* Banner Section - Copied from Introduction/Source Material/Results */}
      <section className="relative isolate text-white pt-12 md:pt-16 pb-16 md:pb-20 bg-[url('/images/banner-bg.jpg')] bg-cover bg-center">
        {/* Gradient Overlay */}
        <div className="absolute inset-0 bg-gradient-to-b from-sky-700/50 via-indigo-800/70 to-purple-900/90 dark:from-sky-700/60 dark:via-indigo-800/80 dark:to-purple-900/95 backdrop-blur-sm"></div>

        {/* Content Container */}
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-4xl font-bold text-black dark:text-white mb-4">
              Methodologies
            </h1>
            <p className="text-xl text-black dark:text-white max-w-3xl mx-auto">
              Detailed explanations of the methods, techniques, and processes used in the analyzed research.
            </p>
          </div>
        </div>

        {/* Bottom Gradient Bar */}
        <div className="absolute bottom-0 left-0 right-0 h-1 w-full bg-gradient-to-l from-blue-400 to-purple-400"></div>
      </section>

      {/* Main Content Wrapper - Constrained Width */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Paper Navigation (if applicable) - MOVED HERE */}
        {(paperParam || expandedCards.size > 0) && filteredPaperGroups.length > 0 && (
          <div className="mb-8">
            <PaperNavigation
              paperSlug={expandedCards.size > 0 ? Array.from(expandedCards)[0] : (paperParam || '')}
              paperTitle={(() => {
                const expandedSlug = expandedCards.size > 0 ? Array.from(expandedCards)[0] : null;
                if (expandedSlug) {
                  const paperGroup = filteredPaperGroups.find(group => group.slug === expandedSlug);
                  return paperGroup ? paperGroup.title : undefined;
                }
                return undefined;
              })()}
            />
          </div>
        )}

        {/* Search Bar - MOVED HERE */}
        <div className="mb-8">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400 dark:text-gray-500" aria-hidden="true" />
            </div>
            <input
              type="text"
              placeholder={`Search methods by paper title or keyword...`}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="block w-full pl-10 pr-3 py-2.5 border border-gray-300 dark:border-zinc-700 rounded-lg shadow-sm leading-5 bg-white dark:bg-zinc-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-colors duration-150"
            />
          </div>
        </div>

        {/* Loading and Error States, followed by Methods List */}
        {/* The <section className="py-12"> that previously wrapped search and content is effectively removed or its role changed. */}
        {loading && (
          <div className="py-8 text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-gray-300 dark:border-gray-600 border-t-sky-500 dark:border-t-sky-400"></div>
            <p className="mt-2 text-gray-600 dark:text-gray-300">Loading methods...</p>
          </div>
        )}

        {error && (
          <div className="py-8 text-center">
            <p className="text-red-500 dark:text-red-400">{error}</p>
          </div>
        )}

        {/* Empty state */}
        {!loading && !error && paperGroups.length === 0 && (
          <div className="py-8 text-center">
            <p className="text-gray-600 dark:text-gray-300">No methods data found. Please check your evaluation files.</p>
          </div>
        )}

        {/* Content - Paper Cards with Methods */}
        {!loading && !error && filteredPaperGroups.length > 0 && (
          <div className="space-y-4">
            {filteredPaperGroups.map((group, groupIndex) => (
              <CollapsiblePaperCard
                key={group.slug + groupIndex}
                title={group.title}
                titleClassName="text-lg md:text-xl"
                slug={group.slug}
                defaultExpanded={forceExpand === true || (!!paperParam && (
                  // Check exact match with group.slug
                  paperParam === group.slug ||
                  // Check if any method in the group has a matching fileBasedSlug
                  group.methods.some(m => m.fileBasedSlug && paperParam === m.fileBasedSlug)
                ))} // Auto-expand if forceExpand is true or if accessed via the mini-menu with a specific paper parameter
                onExpandChange={(expanded, slug) => {
                  console.log(`Card expansion change: ${expanded ? 'expanded' : 'collapsed'} for ${group.slug}`);
                  handleCardExpandChange(expanded, group.slug);
                }}
              >
                <div className="space-y-6">
                  {group.methods.map((method, methodIndex) => (
                    <div
                      key={method.methodName + methodIndex}
                      className="bg-white dark:bg-zinc-900 rounded-lg shadow-md border border-gray-200 dark:border-zinc-700 overflow-hidden mb-6"
                    >
                      <div className="h-1 w-full bg-gradient-to-r from-blue-400 to-purple-400"></div> {/* Gradient top border */}
                      <div className="p-6">
                        <h4 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
                          <CodeBracketIcon className="h-6 w-6 text-blue-500 dark:text-blue-400 mr-2" />
                          <InlineGlossaryTooltip>{method.methodName}</InlineGlossaryTooltip>
                        </h4>

                        {method.simplifiedExplanation && (
                          <div className="mb-6">
                            <h5 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2 flex items-center">
                              <AcademicCapIcon className="h-5 w-5 mr-2 text-blue-500 flex-shrink-0" /> Simplified Explanation
                            </h5>
                            <p className="text-gray-700 dark:text-gray-300">
                              <InlineGlossaryTooltip>{method.simplifiedExplanation}</InlineGlossaryTooltip>
                            </p>
                          </div>
                        )}

                        {method.prerequisites && Array.isArray(method.prerequisites) && method.prerequisites.length > 0 && (
                          <div className="mb-6">
                            <h5 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2 flex items-center">
                              <ClipboardDocumentListIcon className="h-5 w-5 mr-2 text-green-500 flex-shrink-0" /> Prerequisites
                            </h5>
                            <ul className="list-disc pl-5 text-gray-700 dark:text-gray-300">
                              {method.prerequisites.map((prerequisite, i) => (
                                <li key={i} className="mb-2">
                                  <InlineGlossaryTooltip>{prerequisite}</InlineGlossaryTooltip>
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}

                        {method.stepByStepGuide && (
                          <div className="mb-6">
                            <h5 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2 flex items-center">
                              <ArrowPathIcon className="h-5 w-5 mr-2 text-yellow-500 flex-shrink-0" /> Step-by-Step Guide
                            </h5>
                            <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-zinc-700/50">
                              {Array.isArray(method.stepByStepGuide) ? (
                                <div className="space-y-4">
                                  {method.stepByStepGuide.map((step, i) => {
                                    // Each step in the array is already formatted with step number and bold title
                                    // We need to parse it to properly render the formatting
                                    const stepMatch = step.match(/^(\d+\.\s+\*\*)([^*]+)(\*\*)(.*)$/);

                                    if (stepMatch) {
                                      const [, number, title, , description] = stepMatch;
                                      return (
                                        <div key={i} className="mb-4">
                                          <div className="font-bold text-white dark:text-white">
                                            <InlineGlossaryTooltip>{number.replace(/\*\*/g, '')} {title}</InlineGlossaryTooltip>
                                          </div>
                                          {description && (
                                            <div className="pl-6 text-gray-300 dark:text-gray-400">
                                              <MarkdownRenderer content={description} />
                                            </div>
                                          )}
                                        </div>
                                      );
                                    }

                                    // Fallback if the step doesn't match our expected format
                                    return (
                                      <div key={i} className="mb-4">
                                        <div className="text-gray-300 dark:text-gray-300">
                                          <MarkdownRenderer content={step} />
                                        </div>
                                      </div>
                                    );
                                  })}
                                </div>
                              ) : (
                                <div className="space-y-4">
                                  {typeof method.stepByStepGuide === 'string' && (() => {
                                    // Process the step-by-step guide content
                                    const content = method.stepByStepGuide;

                                    // Split into lines and process each line
                                    return content.split('\n')
                                      .map((line, index) => {
                                        // Check if this is a step line (starts with a number)
                                        if (!line.trim().match(/^\d+\./)) {
                                          // Not a step line, return as regular text
                                          return line.trim() ? (
                                            <div key={`text-${index}`} className="pl-6 text-gray-300 dark:text-gray-400">
                                              <InlineGlossaryTooltip>{line}</InlineGlossaryTooltip>
                                            </div>
                                          ) : null;
                                        }

                                        // Clean up the line - remove duplicate numbering
                                        const cleanLine = line.replace(/(\d+\.)\s+(\d+\.)\s+/, '$1 ');

                                        // Extract the step number and content
                                        const match = cleanLine.match(/^(\d+\.)\s+(.+)$/);
                                        if (!match) return null;

                                        const [, number, content] = match;

                                        // Render the step with bold white title
                                        return (
                                          <div key={`step-${index}`} className="mb-4">
                                            <div className="font-bold text-white dark:text-white">
                                              <InlineGlossaryTooltip>{number} {content}</InlineGlossaryTooltip>
                                            </div>
                                          </div>
                                        );
                                      })
                                      .filter(Boolean);
                                  })()
                                  }
                                </div>
                              )}
                            </div>
                          </div>
                        )}

                        {method.practicalExample && (
                          <div className="mb-4">
                            <h5 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2 flex items-center">
                              <CodeBracketIcon className="h-5 w-5 mr-2 text-purple-500 flex-shrink-0" /> Practical Example
                            </h5>
                            <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-zinc-700/50">
                              {method.practicalExample.scenarioDescription && (
                                <div className="mb-4">
                                  <h6 className="text-md font-medium text-gray-800 dark:text-gray-200 mb-2 flex items-center">
                                    <DocumentTextIcon className="h-4 w-4 mr-2 text-blue-500 flex-shrink-0" /> Scenario
                                  </h6>
                                  <p className="text-gray-700 dark:text-gray-300">
                                    <InlineGlossaryTooltip>{method.practicalExample.scenarioDescription}</InlineGlossaryTooltip>
                                  </p>
                                </div>
                              )}

                              {method.practicalExample.implementationCode && (
                                <div className="mb-4">
                                  <h6 className="text-md font-medium text-gray-800 dark:text-gray-200 mb-2 flex items-center">
                                    <CodeBracketIcon className="h-4 w-4 mr-2 text-green-500 flex-shrink-0" /> Implementation
                                  </h6>
                                  <SyntaxHighlighter language="javascript" style={dracula} className="rounded-md">
                                    {method.practicalExample.implementationCode}
                                  </SyntaxHighlighter>
                                </div>
                              )}

                              {method.practicalExample.expectedOutcome && (
                                <div>
                                  <h6 className="text-md font-medium text-gray-800 dark:text-gray-200 mb-2 flex items-center">
                                    <CheckCircleIcon className="h-4 w-4 mr-2 text-purple-500 flex-shrink-0" /> Expected Outcome
                                  </h6>
                                  <p className="text-gray-700 dark:text-gray-300">
                                    <InlineGlossaryTooltip>{method.practicalExample.expectedOutcome}</InlineGlossaryTooltip>
                                  </p>
                                </div>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CollapsiblePaperCard>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default MethodsPage;
