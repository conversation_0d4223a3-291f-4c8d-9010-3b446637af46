{"table_of_contents": [], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 124], ["Line", 46], ["Text", 17]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 225], ["Line", 106], ["Text", 22]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 266], ["Line", 56], ["Text", 25]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 215], ["Line", 71], ["Text", 47]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 167], ["Line", 49], ["Text", 20]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 257], ["Line", 78], ["Text", 56]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 496], ["Line", 66], ["Text", 31]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 375], ["Line", 52], ["Text", 17]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 179], ["Line", 69], ["Text", 43]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 7], ["Text", 4], ["Line", 4]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 176], ["Line", 56], ["Text", 34]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 108], ["Line", 29], ["Text", 22]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 217], ["Line", 58], ["Text", 41]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}], "debug_data_path": "debug_data\\10. Meta-in-context learning"}