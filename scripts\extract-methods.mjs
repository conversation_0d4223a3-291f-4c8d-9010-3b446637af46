import fs from 'fs/promises';
import path from 'path';

const evaluationsDir = path.join(process.cwd(), 'public', 'papers', 'evaluations');
const outputFile = path.join(process.cwd(), 'public', 'data', 'methods-compilation.json');

async function extractMethods() {
  try {
    const files = await fs.readdir(evaluationsDir);
    const evaluationFiles = files.filter(file => file.startsWith('Evaluation_') && file.endsWith('.json'));

    const allMethodsData = [];

    for (const file of evaluationFiles) {
      try {
        const filePath = path.join(evaluationsDir, file);
        const fileContent = await fs.readFile(filePath, 'utf-8');
        const evaluationData = JSON.parse(fileContent);

        const paperTitle = evaluationData.metadata?.title;
        const paperSlug = file.replace(/^Evaluation_/, '').replace(/\.json$/, '');
        const methodologicalDeepDive = evaluationData.methodologicalDeepDive;

        if (paperTitle && methodologicalDeepDive && Array.isArray(methodologicalDeepDive)) {
          for (const method of methodologicalDeepDive) {
            if (method.methodName) { // Basic check to ensure the method object is somewhat valid
              allMethodsData.push({
                paperTitle,
                paperSlug,
                methodName: method.methodName,
                simplifiedExplanation: method.simplifiedExplanation,
                prerequisites: method.prerequisites,
                stepByStepGuide: method.stepByStepGuide,
                practicalExample: method.practicalExample, // This is an object
              });
            } else {
              console.warn(`Skipping a method in ${file}: Missing methodName.`);
            }
          }
        } else {
          // Check if it's an old format file we might want to partially support or just warn
          const oldMethodsContent = evaluationData.detailed_analysis?.ai_techniques?.methods;
          if (paperTitle && oldMethodsContent) {
            // Push a compatibility entry for older formats if desired, or just warn
            allMethodsData.push({
              paperTitle,
              paperSlug,
              // Keep methodsContent for backward compatibility / differentiation if needed
              methodsContent: oldMethodsContent, 
              methodName: "Legacy AI Techniques Summary", // Placeholder name
              simplifiedExplanation: "This entry represents older, less structured method information.",
              prerequisites: "N/A",
              stepByStepGuide: "N/A",
              practicalExample: { scenarioDescription: "N/A", implementationCode: "N/A", expectedOutcome: "N/A" }
            });
            console.warn(`Processed ${file} using legacy 'detailed_analysis.ai_techniques.methods'. Consider updating to 'methodologicalDeepDive' format.`);
          } else if (!paperTitle) {
            console.warn(`Skipping ${file}: Missing title.`);
          } else {
            console.warn(`Skipping ${file}: Missing 'methodologicalDeepDive' array or it's not an array.`);
          }
        }
      } catch (parseError) {
        console.error(`Error parsing ${file}:`, parseError.message);
      }
    }

    await fs.writeFile(outputFile, JSON.stringify(allMethodsData, null, 2));
    console.log(`Successfully extracted methods to ${outputFile}`);
    console.log(`Found and processed ${allMethodsData.length} papers.`);

  } catch (error) {
    console.error('Error extracting methods:', error);
    if (error.code === 'ENOENT' && error.path === evaluationsDir) {
      console.error(`Error: The directory ${evaluationsDir} was not found. Please ensure your evaluation JSON files are in the correct location.`);
    }
  }
}

extractMethods();
