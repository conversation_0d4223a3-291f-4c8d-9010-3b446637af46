'use client';

import React, { ReactNode, useEffect, useState } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import InlineGlossaryTooltip from '@/components/InlineGlossaryTooltip';
import DirectGlossaryTooltip from '@/components/DirectGlossaryTooltip';
import {
  DocumentTextIcon,
  BeakerIcon,
  TableCellsIcon,
  LightBulbIcon,
  ClipboardDocumentListIcon,
  PuzzlePieceIcon,
  FlagIcon
} from '@heroicons/react/24/outline';
import SparklesIconWithTooltip from '@/components/SparklesIconWithTooltip';

// We'll use CSS to style the markdown content instead of custom components

// Helper function to parse and render strategy/takeaway items
function parseAndRenderStrategyItemWithTooltips(itemText: string): React.ReactNode {
  console.log('parseAndRenderStrategyItemWithTooltips received itemText:', itemText, 'typeof:', typeof itemText);

  if (typeof itemText !== 'string') {
    console.error('itemText is not a string:', itemText);
    return <InlineGlossaryTooltip>{String(itemText)}</InlineGlossaryTooltip>; // Should display as string "[object Object]" if itemText is obj
  }

  // MINIMAL DEBUG LOGGING START
  console.log('PaperEvaluation.tsx:25 DEBUG itemText (first 5 chars): ' + itemText.substring(0,5));
  // MINIMAL DEBUG LOGGING END

  // Regex to capture "**Title:** Description" with optional numbered or hyphenated prefixes.
  // This version specifically handles cases like "**Title:**** Description" where an extra "**" follows the colon within the bolded title.
  // Group 1: Numbered prefix (e.g., "1. ")
  // Group 2: Hyphenated prefix (e.g., "- ")
  // Group 3: Title (characters before the colon inside the bold markdown)
  // Group 4: Description (text after the ':**"' structure)
  const match = itemText.match(/^\s*(?:(\d+\.\s+)|(-\s+))?\*\*([^*]+?):\*\*\s*([\s\S]*)/);

  // Original regex that assumed colon was outside: /^(?:(\d+\.\s+)|(-\s+))?\*\*([^*]+)\*\*\s*:\s*([\s\S]*)/

  if (match && match[3] && match[4]) {
    const title = match[3].trim();
    const description = match[4].trim();
    console.log(`PaperEvaluation.tsx:26 Regex matched. Title: ${title}, typeof: ${typeof title}`);
    console.log(`PaperEvaluation.tsx:27 Description: ${description}, typeof: ${typeof description}`);
    if (match[1]) {
      console.log(`PaperEvaluation.tsx:29 Numbered prefix: ${match[1]}`);
    } else if (match[2]) {
      console.log(`PaperEvaluation.tsx:31 Hyphenated prefix: ${match[2]}`);
    }

    // Re-introduce InlineGlossaryTooltip for title and description
    return (
      <>
        <strong>
          <InlineGlossaryTooltip>{title}</InlineGlossaryTooltip>
        </strong>
        {': '}
        <InlineGlossaryTooltip>{description}</InlineGlossaryTooltip>
      </>
    );
  } else {
    console.log(`PaperEvaluation.tsx:43 Regex did not match for itemText: ${itemText}`);
    // Fallback: Render the original item text if no regex matches
    return <InlineGlossaryTooltip>{itemText}</InlineGlossaryTooltip>;
  }
}

// Component that renders markdown content with proper formatting
function MarkdownContent({ content }: { content: string }) {
  // Create custom components for ReactMarkdown
  const customComponents = {
    // Make sure bold text is properly styled
    strong: ({ children }: any) => (
      <strong className="font-bold text-gray-900 dark:text-gray-100">
        {children}
      </strong>
    )
  };

  return (
    <div className="prose dark:prose-invert max-w-none">
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        components={customComponents}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
}

// Component that adds tooltips to text content
function TooltipWrapper({ children }: { children: React.ReactNode }) {
  // Only apply tooltips to string content
  if (typeof children === 'string') {
    return <InlineGlossaryTooltip>{children}</InlineGlossaryTooltip>;
  }

  // For non-string content, just return it as is
  return <>{children}</>;
}

// Score description utility function
function getScoreDescription(category: string, score: number): string {

  // Map score ranges to descriptions based on the Critical Review Context document
  if (score >= 90 && score <= 100) {
    switch(category) {
      case 'Implementation Readiness':
        return "Complete implementation package with public repo, clear build commands, detailed environment specs, and working examples";
      case 'Verified Performance Impact':
        return "Comprehensive performance metrics with statistical validation and clear comparisons";
      case 'Debuggability & Maintainability':
        return "Exceptional debugging tools and maintainability improvements with comprehensive examples";
      case 'Audio-Plugin Transfer Potential':
        return "Seamless integration with audio plugin frameworks and proven applicability across multiple audio tasks";
      default:
        return "Excellent";
    }
  } else if (score >= 70 && score <= 89) {
    switch(category) {
      case 'Implementation Readiness':
        return "Good implementation details with most components well-documented";
      case 'Verified Performance Impact':
        return "Strong performance data with good validation across multiple metrics";
      case 'Debuggability & Maintainability':
        return "Strong debugging support and clear maintainability benefits";
      case 'Audio-Plugin Transfer Potential':
        return "Good integration potential with clear resource considerations and demonstrated flexibility";
      default:
        return "Good";
    }
  } else if (score >= 50 && score <= 69) {
    switch(category) {
      case 'Implementation Readiness':
        return "Adequate implementation information with some components documented";
      case 'Verified Performance Impact':
        return "Adequate performance measurements with some validation";
      case 'Debuggability & Maintainability':
        return "Useful debugging features and moderate maintainability improvements";
      case 'Audio-Plugin Transfer Potential':
        return "Reasonable transfer potential with some audio-specific adaptations";
      default:
        return "Adequate";
    }
  } else if (score >= 30 && score <= 49) {
    switch(category) {
      case 'Implementation Readiness':
        return "Basic implementation details with significant gaps";
      case 'Verified Performance Impact':
        return "Limited performance data with minimal validation";
      case 'Debuggability & Maintainability':
        return "Basic debugging capabilities with limited maintainability benefits";
      case 'Audio-Plugin Transfer Potential':
        return "Limited transfer potential requiring significant modifications";
      default:
        return "Limited";
    }
  } else {
    switch(category) {
      case 'Implementation Readiness':
        return "Minimal or missing implementation information";
      case 'Verified Performance Impact':
        return "Unsubstantiated or missing performance claims";
      case 'Debuggability & Maintainability':
        return "Minimal or no attention to debugging and maintainability";
      case 'Audio-Plugin Transfer Potential':
        return "Poor fit for audio plugin development with major obstacles";
      default:
        return "Poor";
    }
  }
}

// Get subscore description based on category and subscore name
function getSubscoreDescription(category: string, subscoreName: string): string {
  switch(category) {
    case 'Implementation Readiness':
      switch(subscoreName) {
        case 'Code Link License':
          return "Paper cites public repo and permissive license";
        case 'Build Snippet':
          return "Shows exact compile/run commands (e.g., cmake .. && make && ./demo)";
        case 'Environment Spec':
          return "CUDA / compiler / JUCE version listed";
        case 'Minimal Example':
          return "At least one full, compile-ready code listing (or pseudocode ≤20 lines)";
        default:
          return "";
      }
    case 'Verified Performance Impact':
      switch(subscoreName) {
        case 'Metric Table':
          return "CPU %, latency, or bug count reduction vs. baseline plugin";
        case 'Benchmarked Code Output':
          return "Diff or graph proves higher accuracy, lower error, or clearer code style";
        case 'Stat Sig Repetition':
          return "Reports seeds or N runs to show consistency";
        default:
          return "";
      }
    case 'Debuggability & Maintainability':
      switch(subscoreName) {
        case 'Error Handling Walkthrough':
          return "Shows how method spots or fixes C++/JUCE or LLM-generated bugs";
        case 'Code Clarity':
          return "Presents refactored snippet or prompt that cuts 'spaghetti' into modular functions";
        case 'Tooling Hooks':
          return "Mentions static analyzers, sanitizers, or agent loop that auto-tests";
        default:
          return "";
      }
    case 'Audio-Plugin Transfer Potential':
      switch(subscoreName) {
        case 'Domain Mapping':
          return "Explicit paragraph on integrating into VST/AU or real-time DSP chain";
        case 'Resource Fit':
          return "Quotes RAM/VRAM and block-size constraints typical of plugins";
        case 'Generalisability':
          return "Claims or demo for second audio task (e.g., same technique boosts both compressor and reverb)";
        default:
          return "";
      }
    default:
      return "";
  }
}

// Define the structure of the scores
interface Scores {
  implementation_readiness: {
    code_link_license: number;
    build_snippet: number;
    environment_spec: number;
    minimal_example: number;
    total: number;
  };
  verified_performance_impact: {
    metric_table: number;
    benchmarked_code_output: number;
    stat_sig_repetition: number;
    total: number;
  };
  debuggability_maintainability: {
    error_handling_walkthrough: number;
    code_clarity: number;
    tooling_hooks: number;
    total: number;
  };
  audio_plugin_transfer: {
    domain_mapping: number;
    resource_fit: number;
    generalisability: number;
    total: number;
  };
  total_weighted_score: number;
}

interface Metadata {
  title: string;
  authors: string;
  year: number;
  doi: string;
  url?: string;
}

interface MethodologicalDeepDive {
  methodName: string;
  simplifiedExplanation: string;
  prerequisites?: string;
  stepByStepGuide?: string;
  practicalExample?: {
    scenarioDescription?: string;
    implementationCode?: string;
    expectedOutcome?: string;
  };
}

interface EvaluationData {
  metadata?: Metadata;
  scores: Scores;
  paper_summary?: string;
  key_strategies?: string[];
  detailed_analysis?: {
    implementation_readiness?: {
      code_link_license?: string;
      build_snippet?: string;
      environment_spec?: string;
      minimal_example?: string;
    } | string;
    verified_performance_impact?: {
      metric_table?: string;
      benchmarked_code_output?: string;
      stat_sig_repetition?: string;
    } | string;
    debuggability_maintainability?: {
      error_handling_walkthrough?: string;
      code_clarity?: string;
      tooling_hooks?: string;
    } | string;
    audio_plugin_transfer?: {
      domain_mapping?: string;
      resource_fit?: string;
      generalisability?: string;
    } | string;
  };
  methodological_deep_dive?: MethodologicalDeepDive[];
  key_takeaways?: string[] | string;
  method_applicability?: string;
  selection_status?: string;
  conclusion?: string;
}

interface PaperEvaluationProps {
  evaluationData: EvaluationData; // Added
  showRawMarkdown?: boolean;
}

// Helper components
interface ScoreSectionProps {
  title: string;
  scores: {
    total: number;
    [key: string]: number;
  };
  details?: string | {
    [key: string]: string | undefined;
  };
  colorClass?: string;
}

function ScoreSection({ title, scores, details, colorClass }: ScoreSectionProps) {
  const [expanded, setExpanded] = useState(false);

  // Determine color classes based on title if not provided
  let barFillColor = colorClass || '';
  let mainCardAccentBorder = '';
  let subCardAccentBorder = '';

  if (!colorClass) {
    switch(title) {
      case 'Implementation Readiness':
        barFillColor = 'bg-blue-500';
        mainCardAccentBorder = 'border-l-4 border-l-blue-500 dark:border-l-blue-400';
        subCardAccentBorder = 'border-t-2 border-t-blue-500 dark:border-t-blue-400';
        break;
      case 'Verified Performance Impact':
        barFillColor = 'bg-green-500';
        mainCardAccentBorder = 'border-l-4 border-l-green-500 dark:border-l-green-400';
        subCardAccentBorder = 'border-t-2 border-t-green-500 dark:border-t-green-400';
        break;
      case 'Debuggability & Maintainability':
        barFillColor = 'bg-purple-500';
        mainCardAccentBorder = 'border-l-4 border-l-purple-500 dark:border-l-purple-400';
        subCardAccentBorder = 'border-t-2 border-t-purple-500 dark:border-t-purple-400';
        break;
      case 'Audio-Plugin Transfer Potential':
        barFillColor = 'bg-amber-500';
        mainCardAccentBorder = 'border-l-4 border-l-amber-500 dark:border-l-amber-400';
        subCardAccentBorder = 'border-t-2 border-t-amber-500 dark:border-t-amber-400';
        break;
      default:
        barFillColor = 'bg-blue-500';
        mainCardAccentBorder = 'border-l-4 border-l-blue-500 dark:border-l-blue-400';
        subCardAccentBorder = 'border-t-2 border-t-blue-500 dark:border-t-blue-400';
    }
  }

  // Format keys for display (e.g., 'latency_throughput' -> 'Latency Throughput')
  const formatKey = (key: string): string => {
    return key
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  // Get subscore keys (excluding 'total')
  const subscoreKeys = Object.keys(scores).filter(key => key !== 'total');

  return (
    <div className={`bg-white dark:bg-zinc-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 ${mainCardAccentBorder} overflow-hidden`}>
      {/* Header - more compact */}
      <div className="p-4">
        <div className="flex-1">
          <div className="flex justify-between items-center mb-1">
            <h3 className="text-lg font-bold text-gray-900 dark:text-gray-100">{title}</h3>
            <div className="flex items-center">
              <span className="text-lg font-bold text-gray-900 dark:text-gray-100 bg-gray-100 dark:bg-gray-800 px-3 py-1 rounded-lg">
                {scores.total}/100
              </span>
            </div>
          </div>
          <div className="h-2 bg-gray-200 dark:bg-gray-700 rounded-full mb-1">
            <div
              className={`h-2 ${barFillColor} rounded-full`}
              style={{ width: `${Math.min(scores.total, 100)}%` }}
            ></div>
          </div>
          <div className="flex justify-between items-center">
            <p className="text-xs text-gray-600 dark:text-gray-400 italic">
              {getScoreDescription(title, scores.total)}
            </p>

            {/* Expand/Collapse Button - Below the score bar */}
            <button
              onClick={() => setExpanded(!expanded)}
              className="text-sm text-gray-600 dark:text-gray-400 flex items-center hover:text-gray-900 dark:hover:text-gray-200 focus:outline-none"
              aria-expanded={expanded}
              aria-controls={`details-${title.toLowerCase().replace(/\s+/g, '-')}`}
            >
              <span className="sr-only">{expanded ? 'Hide Details' : 'Show Details'}</span>
              <span className="mr-1 text-xs">{expanded ? 'Hide Details' : 'Show Details'}</span>
              <svg
                className={`w-4 h-4 transition-transform ${expanded ? 'transform rotate-180' : ''}`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>
          </div>
        </div>
      </div>
      {/* Expanded Details */}
      {expanded && (
        <div
          id={`details-${title.toLowerCase().replace(/\s+/g, '-')}`}
          className="px-4 pb-4 pt-0 border-t border-gray-200 dark:border-gray-700"
        >
          {/* Subscores */}
          <div className={`grid grid-cols-1 ${subscoreKeys.length === 4 ? 'md:grid-cols-4' : 'md:grid-cols-3'} gap-3 mb-4 mt-4`}>
            {subscoreKeys.map(key => (
              <div key={key} className={`bg-white dark:bg-zinc-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 ${subCardAccentBorder} p-3`}>
                <div className="flex justify-between items-center mb-1">
                  <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">{formatKey(key)}</h4>
                  <span className="text-sm font-bold text-gray-900 dark:text-gray-100 bg-gray-100 dark:bg-gray-700 px-2 py-0.5 rounded">
                    {scores[key]}/100
                  </span>
                </div>
                <div className="h-1.5 bg-gray-200 dark:bg-gray-700 rounded-full mb-1">
                  <div
                    className={`h-1.5 ${barFillColor} rounded-full`}
                    style={{ width: `${Math.min(scores[key], 100)}%` }}
                  ></div>
                </div>
                <p className="text-xs text-gray-600 dark:text-gray-400 italic">
                  {getSubscoreDescription(title, formatKey(key))}
                </p>
              </div>
            ))}
          </div>

          {/* Detailed Analysis */}
          {details && (
            <div className="space-y-4">
              {typeof details === 'string' ? (
                <div className="bg-white dark:bg-zinc-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
                  <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-2">Analysis</h4>
                  <div className="prose prose-sm dark:prose-invert max-w-none text-gray-700 dark:text-gray-300 custom-markdown">
                    <InlineGlossaryTooltip>
                      {details || ''}
                    </InlineGlossaryTooltip>
                  </div>
                </div>
              ) : (
                Object.entries(details).map(([key, content]) => (
                  content ? (
                    <div key={key} className="bg-white dark:bg-zinc-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
                      <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-2">{formatKey(key)}</h4>
                      <div className="prose prose-sm dark:prose-invert max-w-none text-gray-700 dark:text-gray-300 custom-markdown">
                        <InlineGlossaryTooltip>
                          {content || ''}
                        </InlineGlossaryTooltip>
                      </div>
                    </div>
                  ) : null
                ))
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
}

interface ContentSectionProps {
  title: React.ReactNode; // Can be string, React element, or other renderable type
  content: string | string[];
  accentColor?: string;
}

function ContentSection({ title, content, accentColor = 'blue' }: ContentSectionProps) {
  // Debug logging
  console.log('ContentSection - Title:', {
    type: typeof title,
    value: title,
    isElement: React.isValidElement(title),
    stringValue: String(title)
  });

  // More detailed debugging of content structure
  console.log('ContentSection - Content:', content);
  if (Array.isArray(content)) {
    content.forEach((item, index) => {
      console.log(`Content item ${index}:`, item);

      return (
        <li key={index} className="text-gray-700 dark:text-gray-300">
          <div className="prose dark:prose-invert max-w-none">
            {renderStrategyItem(item)}
          </div>
        </li>
      );
    });
  }

  // Simple function to safely render the title
  const renderTitle = () => {
    try {
      // If it's a string or number, render it directly
      if (typeof title === 'string' || typeof title === 'number') {
        return title;
      }

      // If it's a React element, render it directly
      if (React.isValidElement(title)) {
        return title;
      }

      // If it's an object with a toString method
      if (title && typeof title === 'object' && 'toString' in title) {
        return title.toString();
      }

      // Fallback to empty string
      return '';
    } catch (e) {
      console.error('Error rendering title:', e);
      return '';
    }
  };

  // Determine border color based on accentColor
  const borderColorClass = accentColor === 'blue'
    ? 'border-t-4 border-t-blue-500 dark:border-t-blue-400'
    : accentColor === 'green'
    ? 'border-t-4 border-t-green-500 dark:border-t-green-400'
    : accentColor === 'yellow'
    ? 'border-t-4 border-t-yellow-500 dark:border-t-yellow-400'
    : 'border-t-4 border-t-blue-500 dark:border-t-blue-400';

  // Helper function to render a strategy item with proper formatting
  const renderStrategyItem = (item: any) => {
    // If it's a simple string, just render it
    if (typeof item === 'string') {
      return (
        <InlineGlossaryTooltip>
          {item}
        </InlineGlossaryTooltip>
      );
    }

    // If it's an object, try to extract the content based on its structure
    if (item && typeof item === 'object') {
      // First, check if it's a React element
      if (React.isValidElement(item)) {
        return item;
      }

      // For key strategies and key takeaways, the structure might be more complex
      // The object itself might be the content we want to display
      // Try to extract the text content from the object

      // Check if the object itself has a toString method that's been overridden
      if (item.toString && item.toString !== Object.prototype.toString) {
        const stringValue = item.toString();
        if (stringValue !== '[object Object]') {
          return <InlineGlossaryTooltip>{stringValue}</InlineGlossaryTooltip>;
        }
      }

      // Handle case where the object might be the actual text content
      // This is common in React when text nodes are converted to objects
      if (Object.keys(item).length === 0) {
        const stringValue = String(item);
        if (stringValue !== '[object Object]') {
          return <InlineGlossaryTooltip>{stringValue}</InlineGlossaryTooltip>;
        }
      }

      // Try to access common properties that might contain the content

      // Handle objects with subject property (common in key strategies)
      if ('subject' in item) {
        const subject = item.subject;
        const description = item.description || '';

        return (
          <>
            <strong className="font-bold">{subject}</strong>
            {description ? <span>{description}</span> : null}
          </>
        );
      }

      // Handle objects with text property
      if ('text' in item) {
        return <>{item.text}</>;
      }

      // Handle objects with content property
      if ('content' in item) {
        return <>{item.content}</>;
      }

      // Handle objects with value property
      if ('value' in item) {
        return <>{item.value}</>;
      }

      // Handle objects with message property
      if ('message' in item) {
        return <>{item.message}</>;
      }

      // Try to extract a meaningful string representation
      try {
        // Check for common properties that might contain the main content
        const possibleProps = ['name', 'title', 'label', 'id', 'key', 'description', 'text', 'value', 'content'];
        for (const prop of possibleProps) {
          if (prop in item && typeof item[prop] === 'string') {
            return <>{item[prop]}</>;
          }
        }

        // If we have a toString method that's not the default Object.toString
        if ('toString' in item && item.toString !== Object.prototype.toString) {
          return <>{item.toString()}</>;
        }

        // If the object has only one property, use that
        const keys = Object.keys(item);
        if (keys.length === 1 && typeof item[keys[0]] === 'string') {
          return <>{item[keys[0]]}</>;
        }

        // Last resort: try to stringify the object in a readable way
        return <span className="text-red-500">[Unable to display content]</span>;
      } catch (e) {
        console.error('Error rendering item:', e);
        return <span className="text-red-500">[Error rendering item]</span>;
      }
    }

    // Fallback for any other type
    return <InlineGlossaryTooltip>{String(item)}</InlineGlossaryTooltip>;
  };

  return (
    <div className={`bg-white dark:bg-zinc-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 ${borderColorClass}`}>
      <h2 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
        {renderTitle()}
      </h2>

      {/* Handle string content */}
      {typeof content === 'string' ? (
        <div className="prose dark:prose-invert max-w-none text-gray-700 dark:text-gray-300 whitespace-pre-line custom-markdown">
          <InlineGlossaryTooltip>
            {content}
          </InlineGlossaryTooltip>
        </div>
      ) : (
        /* Handle array content */
        Array.isArray(content) && (
          <ul className="list-disc pl-5 space-y-2">
            {content.map((item: any, index: number) => (
              <li key={index} className="text-gray-700 dark:text-gray-300">
                <div className="prose dark:prose-invert max-w-none">
                  {renderStrategyItem(item)}
                </div>
              </li>
            ))}
          </ul>
        )
      )}
    </div>
  );
}

const PaperEvaluation = ({ evaluationData, showRawMarkdown = false }: PaperEvaluationProps) => {
  if (showRawMarkdown) {
    return (
      <div className="p-4 bg-gray-800 text-white rounded-md shadow my-4">
        <h2 className="text-xl font-semibold mb-2">Raw Evaluation Data (V1)</h2>
        <pre className="text-sm whitespace-pre-wrap break-all">
          {JSON.stringify(evaluationData, null, 2)}
        </pre>
      </div>
    );
  }

  if (!evaluationData) {
    return <p>Loading evaluation data...</p>; // Or handle as 'No data provided'
  }

  // Destructure metadata safely
  const metadata = evaluationData.metadata || {};

  return (
    <div className="space-y-6">
      {/* Paper Summary */}
      {evaluationData.paper_summary && (
        <div className="bg-white dark:bg-zinc-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 border-t-4 border-t-blue-500 dark:border-t-blue-400">
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
            <DocumentTextIcon className="mr-3 h-6 w-6 text-blue-500 dark:text-blue-400 flex-shrink-0" />
            <span>Paper Summary</span>
            <SparklesIconWithTooltip className="ml-2 h-5 w-5 text-yellow-500 dark:text-yellow-400 flex-shrink-0" />
          </h2>
          <div className="prose dark:prose-invert max-w-none text-gray-700 dark:text-gray-300">
            <InlineGlossaryTooltip>
              {evaluationData.paper_summary || ''}
            </InlineGlossaryTooltip>
          </div>
        </div>
      )}

      {/* Methodological Deep Dive */}
      {evaluationData.methodological_deep_dive && evaluationData.methodological_deep_dive.length > 0 && (
        <div className="bg-white dark:bg-zinc-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 border-t-4 border-t-purple-500 dark:border-t-purple-400">
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-6 flex items-center">
            <BeakerIcon className="mr-3 h-6 w-6 text-purple-500 dark:text-purple-400 flex-shrink-0" />
            <span>Methodological Deep Dive</span>
            {/* No SparklesIcon here as this section is human-written or not explicitly AI for this task */}
          </h2>
          <div className="space-y-8">
            {evaluationData.methodological_deep_dive.map((method, index) => (
              <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-6 bg-gray-50 dark:bg-zinc-800">
                <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">{method.methodName}</h3>

                <div className="mb-6">
                  <h4 className="text-lg font-medium text-gray-800 dark:text-gray-200 mb-2">Simplified Explanation</h4>
                  <div className="prose prose-sm dark:prose-invert max-w-none text-gray-700 dark:text-gray-300">
                    <ReactMarkdown>{method.simplifiedExplanation}</ReactMarkdown>
                  </div>
                </div>

                {method.prerequisites && (
                  <div className="mb-6">
                    <h4 className="text-lg font-medium text-gray-800 dark:text-gray-200 mb-2">Prerequisites</h4>
                    <div className="prose prose-sm dark:prose-invert max-w-none text-gray-700 dark:text-gray-300">
                      <ReactMarkdown>{method.prerequisites}</ReactMarkdown>
                    </div>
                  </div>
                )}

                {method.stepByStepGuide && (
                  <div className="mb-6">
                    <h4 className="text-lg font-medium text-gray-800 dark:text-gray-200 mb-2">Step-by-Step Guide</h4>
                    <div className="prose prose-sm dark:prose-invert max-w-none text-gray-700 dark:text-gray-300">
                      <ReactMarkdown>{method.stepByStepGuide}</ReactMarkdown>
                    </div>
                  </div>
                )}

                {method.practicalExample && (
                  <div className="border-t border-gray-200 dark:border-gray-700 pt-6 mt-6">
                    <h4 className="text-lg font-medium text-gray-800 dark:text-gray-200 mb-4">Practical Example</h4>

                    {method.practicalExample.scenarioDescription && (
                      <div className="mb-4">
                        <h6 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Scenario</h6>
                        <div className="prose prose-sm dark:prose-invert max-w-none text-gray-700 dark:text-gray-300">
                          <ReactMarkdown>{method.practicalExample.scenarioDescription}</ReactMarkdown>
                        </div>
                      </div>
                    )}

                    {method.practicalExample.implementationCode && (
                      <div className="mb-4">
                        <h6 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Implementation</h6>
                        <div className="bg-gray-800 text-gray-200 p-4 rounded-md overflow-x-auto">
                          <pre className="text-sm">
                            <code>{method.practicalExample.implementationCode}</code>
                          </pre>
                        </div>
                      </div>
                    )}

                    {method.practicalExample.expectedOutcome && (
                      <div>
                        <h6 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Expected Outcome</h6>
                        <div className="prose prose-sm dark:prose-invert max-w-none text-gray-700 dark:text-gray-300">
                          <ReactMarkdown>{method.practicalExample.expectedOutcome}</ReactMarkdown>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Evaluation Scores Section */}
      {evaluationData.scores && (
        <div className="mt-6 bg-white dark:bg-zinc-900 rounded-lg shadow-sm dark:shadow-zinc-800/30 p-8 border border-gray-200 dark:border-gray-700 border-t-4 border-t-red-500 dark:border-t-red-400">
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-6 flex items-center">
            <TableCellsIcon className="mr-3 h-6 w-6 text-red-500 dark:text-red-400 flex-shrink-0" />
            <span>Evaluation Scores</span>
            <SparklesIconWithTooltip className="ml-2 h-5 w-5 text-yellow-500 dark:text-yellow-400 flex-shrink-0" />
          </h2>
          <div className="space-y-6">
            {/* Implementation Readiness */}
            {evaluationData.scores.implementation_readiness && (
              <ScoreSection
                title="Implementation Readiness"
                scores={evaluationData.scores.implementation_readiness}
                details={evaluationData.detailed_analysis?.implementation_readiness}
              />
            )}

            {evaluationData.scores.verified_performance_impact && (
              <ScoreSection
                title="Verified Performance Impact"
                scores={evaluationData.scores.verified_performance_impact}
                details={evaluationData.detailed_analysis?.verified_performance_impact}
              />
            )}

            {evaluationData.scores.debuggability_maintainability && (
              <ScoreSection
                title="Debuggability & Maintainability"
                scores={evaluationData.scores.debuggability_maintainability}
                details={evaluationData.detailed_analysis?.debuggability_maintainability}
              />
            )}

            {evaluationData.scores.audio_plugin_transfer && (
              <ScoreSection
                title="Audio-Plugin Transfer Potential"
                scores={evaluationData.scores.audio_plugin_transfer}
                details={evaluationData.detailed_analysis?.audio_plugin_transfer}
              />
            )}

            {/* Total Weighted Score - With distinct background color and styling */}
            <div className="bg-gradient-to-r from-blue-50/70 to-blue-50/30 dark:from-blue-900/30 dark:to-blue-900/10 rounded-lg shadow-md border-2 border-gray-200 dark:border-gray-700 border-l-4 border-l-blue-500 dark:border-l-blue-400 overflow-hidden mt-6">
              <div className="p-4">
                <div className="flex-1">
                  <div className="flex justify-between items-center mb-1">
                    <h3 className="text-lg font-bold text-gray-900 dark:text-gray-100">Total Weighted Score</h3>
                    <span className="text-lg font-bold text-gray-900 dark:text-gray-100 bg-gray-100 dark:bg-gray-800 px-3 py-1 rounded-lg">
                      {evaluationData.scores.total_weighted_score}/100
                    </span>
                  </div>
                  <div className="h-2 bg-gray-200 dark:bg-gray-700 rounded-full mb-1">
                    <div
                      className="h-2 bg-blue-600 dark:bg-blue-500 rounded-full"
                      style={{ width: `${Math.min(evaluationData.scores.total_weighted_score, 100)}%` }}
                    ></div>
                  </div>
                  <div className="flex justify-between items-center">
                    <p className="text-xs text-gray-600 dark:text-gray-400 italic">
                      Weighted calculation: Implementation Readiness (30%), Verified Performance Impact (25%), Debuggability & Maintainability (25%), Audio-Plugin Transfer Potential (20%)
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Key Strategies Section */}
      {evaluationData.key_strategies && evaluationData.key_strategies.length > 0 && (
        <div className="bg-white dark:bg-zinc-900 rounded-lg shadow-sm dark:shadow-zinc-800/30 p-8 border border-gray-200 dark:border-gray-700 border-t-4 border-t-green-500 dark:border-t-green-400">
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
            <LightBulbIcon className="mr-3 h-6 w-6 text-green-500 dark:text-green-400 flex-shrink-0" />
            <span>Key Strategies</span>
            <SparklesIconWithTooltip className="ml-2 h-5 w-5 text-yellow-500 dark:text-yellow-400 flex-shrink-0" />
          </h2>
          <ul className="list-disc pl-5 space-y-2">
            {evaluationData.key_strategies.map((strategy, index) => (
              <li key={index} className="text-gray-700 dark:text-gray-300">
                <div className="prose dark:prose-invert max-w-none">
                  {parseAndRenderStrategyItemWithTooltips(strategy)}
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Additional Section Cards */}
      {(evaluationData.key_takeaways || evaluationData.method_applicability || evaluationData.selection_status) && (
        <div className="space-y-6 mt-6">
          {evaluationData.key_takeaways && (
            <div className="bg-white dark:bg-zinc-900 rounded-lg shadow-sm dark:shadow-zinc-800/30 p-8 border border-gray-200 dark:border-gray-700 border-t-4 border-t-blue-500 dark:border-t-blue-400">
              <h2 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
                <ClipboardDocumentListIcon className="mr-3 h-6 w-6 text-blue-500 dark:text-blue-400 flex-shrink-0" />
                <span>Key Takeaways</span>
                <SparklesIconWithTooltip className="ml-2 h-5 w-5 text-yellow-500 dark:text-yellow-400 flex-shrink-0" />
              </h2>
              <ul className="list-disc pl-5 space-y-2">
                {(typeof evaluationData.key_takeaways === 'string'
                  ? evaluationData.key_takeaways.split('\n\n').filter(item => item.trim() !== '')
                  : Array.isArray(evaluationData.key_takeaways)
                    ? evaluationData.key_takeaways
                    : [String(evaluationData.key_takeaways)] // Ensure it's an array, converting non-array/non-string to string
                ).map((takeaway, index) => (
                  <li key={index} className="text-gray-700 dark:text-gray-300">
                    <div className="prose dark:prose-invert max-w-none">
                      {parseAndRenderStrategyItemWithTooltips(String(takeaway))}
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {evaluationData.method_applicability && (
            <ContentSection
              title={<><PuzzlePieceIcon className="mr-3 h-6 w-6 text-green-500 dark:text-green-400 flex-shrink-0 inline-block" />Method Applicability<SparklesIconWithTooltip className="ml-2 h-5 w-5 text-yellow-500 dark:text-yellow-400 flex-shrink-0 inline-block" /></>}
              content={evaluationData.method_applicability}
              accentColor="green"
            />
          )}

          {(evaluationData.conclusion || evaluationData.selection_status) && (
            <ContentSection
              title={<><FlagIcon className="mr-3 h-6 w-6 text-yellow-500 dark:text-yellow-400 flex-shrink-0 inline-block" />Conclusion<SparklesIconWithTooltip className="ml-2 h-5 w-5 text-yellow-500 dark:text-yellow-400 flex-shrink-0 inline-block" /></>}
              content={evaluationData.conclusion || evaluationData.selection_status || ''}
              accentColor="yellow"
            />
          )}
        </div>
      )}
    </div>
  );
}

export default PaperEvaluation;
