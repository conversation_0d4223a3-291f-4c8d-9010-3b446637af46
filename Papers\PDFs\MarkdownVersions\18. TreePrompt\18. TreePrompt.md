TreePrompt: Learning to Compose Tree Prompts for Explainable Visual Grounding

, Lei Chen2

1Zhejiang University 2Finvolution Group 3Hong Kong University of Science and Technology {chenchiz, junx, jshao}@zju.edu.cn, <EMAIL>, <EMAIL>

Abstract

Prompt tuning has achieved great success in transferring the knowledge from large pretrained vision-language models into downstream tasks, and has dominated the performance on visual grounding (VG). However, almost all existing prompt tuning paradigms suffer from poor interpretability. In this paper, we argue that their poor interpretability is attributed to the holistic prompt generation and inference process. By "holistic", we mean that they usually directly learn a set of vectors as the prompt (*i*.*e*., prompt generation), and use the learned global prompt to augment the textual input for the VG model (*i*.*e*., prompt inference). To this end, we propose a new prompt construction paradigm with explicit explainable ability, named *TreePrompt*. Specifically, we first deconstruct a complex sentence into a tree, that is consistent with human reasoning. Then, following the syntax tree, we compose a structured prompt in a bottom-up manner. Thanks to this step-by-step prompt construction process, each intermediate prompt (*i*.*e*., tree node) permits us to understand the reasoning process. Extensive ablations on various backbones and benchmarks consistently demonstrate the effectiveness and interpretability of our TreePrompt.

Visual grounding (VG), *a*.*k*.*a*., referring expression comprehension, aims to identify and localize the referent specified by the natural language query. It is a crucial technique for many downstream applications, including navigation [4], visual question answering [1], image-text matching [10], and so on. With the appearance of large-scale web data, the *pretrain-finetune* paradigm has achieved significant success in addressing different vision-language (VL) tasks including VG, *i*.*e*., it first pretrains a general model with large-scale data, and then finetunes the model with small-size datastream data. However, fully finetuning the whole large model is always computationally expensive, and it easily suffers from over-fitting issues. Thus, in order to adopt these powerful large models to downstream

Among them, prompt tuning (or prompt learning) [32] is the most popular solution for fast large model adaptation. As shown in Figure 1(a), prompt tuning always fixes the parameters of the pretrained models and uses a prompt to trigger its knowledge. For example, given a well-pretrained VL model, a simple natural language prompt like "*Which region does the text describe*?" can adapt a general VL model to the VG task (*c*.*f*., Figure 1(b)) [37]. However, manually designing suitable prompts usually requires extensive expert experience and labor. To mitigate this problem, some recent prompt tuning works try to learn the optimal prompts automatically [20, 50, 49], *i*.*e*., the natural language prompt is

Although significant progress has been achieved, almost all existing prompt tuning paradigms (for VG) suffer from poor interpretability in both prompt generation and inference stages: 1) In ∗Long Chen is the corresponding author. Work was done when Chenchi Zhang remotely visited HKUST.

tasks at a lower cost, the community recently begins to explore alternative approaches.

substituted with a set of learnable embeddings, which are learned in an end-to-end manner.

, Jian Shao1

, Long Chen3∗

, Jun Xiao1

Chenchi Zhang1

1 Introduction

arXiv:2305.11497v1 [cs.CV] 19 May 2023

Preprint. Under review.

A woman with flowers on her sweater holding a remote.

*Prompts*

Pretrained Model

Discrete prompt

Prompt

Continuous prompt

(d) Our proposed prompt follows the same tree structure as the query sentence.

nodes allow us to observe the reasoning and construction process.

In summary, we made three main contributions in this paper:

*Which region does the text describe?* expression

Referring text Textual input

Visual input

expression

(b)

(c)

Figure 1: Comparison of three types of prompt tuning methods. (a): The general framework of adapting pretrained models with prompt tuning. (b) The prompt is a manually written natural sentence (*i*.*e*., discrete prompt) [37]. (c) The prompt is a set of learned embeddings (*i*.*e*., continuous prompt).

the prompt generation stage, they directly construct the holistic prompt without any explainable intermediate steps. 2) In the prompt inference stage, they usually take the global learned prompt and the query sentence as input, and then predict the corresponding output directly. In contrast, we humans typically decompose a complex reasoning task into multiple explicit subtasks. Take the query "*A woman with flowers on her sweater holding a remote*" in Figure 1 as an example, we humans would solve this task in three steps: 1) identifying the referent is "*a woman*"; 2) finding all women "wearing a sweater with flowers"; and 3) localizing the woman "*holding a remote*". Based on these observations, it is natural to raise a question: *can prompt tuning paradigm realize a similar*

In this paper, we propose a new prompt construction mechanism: TreePrompt, which is the first work to enable prompt-tuning-based VG methods to have explicit interpretability in both prompt generation and inference stages. Generally speaking, TreePrompt tries to compose a structured prompt following the language tree structures of query sentences, which inherently imitates the reasoning process as we humans. Specifically, we first deconstruct the complex query sentence into a tree structure with an off-the-shelf sentence parser (*e*.*g*., dependency parsing trees, DPTs [3]). Then, we compose a structured prompt in a bottom-up manner along the tree structure. Since each word (or tree node) has different functions in localizing the referent, we design three primitive modular networks: Leaf, Rel, and Enti modules (detailed in Sec. 3.2.2). Each module calculates an intermediate prompt, which simulates the human-like reasoning process step by step. For example in Figure 1(d), Rel(*holding*) combines the message carried by the word "holding" and prompts received from Leaf(*remote*). Then, it will generate a new intermediate prompt that conveys the meaning like "*something is holding a remote*". Thanks to the explicit and structured design, the intermediate prompts of different tree

We evaluated our TreePrompt on three challenging VG benchmarks: RefCOCO [45], RefCOCO+ [45], and RefCOCOg [29]. In particular, TreePrompt not only outperforms all existing prompt tuning methods, but also achieves comparable performance to whole-model finetuning methods. Meanwhile, since TreePrompt is model-agnostic, *i*.*e*., generated prompts can be easily incorporated into different pretrained VL models, we validate its generalization ability on different architectures (*e*.*g*., OFA [37] and VLT5 [8]). Extensive qualitative results have shown the strong explainable ability of TreePrompt.

1. We propose a new TreePrompt for constructing prompts that utilize the structure of syntax trees, which effectively addresses the poor interpretability issue present in previous methods.

2. TreePrompt is universal and model-agnostic, which can be attached to any pretrained VL models.

3. Extensive experiments have been conducted to demonstrate the superiority of our TreePrompt.

2

(d)

with flowers on sweater  

expression

woman

= , , , ⋯

Our tree prompt

holding remote

 

 = , , , ⋯ *Learnable vectors*

*Fixed texts*

…

(a)

*human-like explainability*?

…

…

2 Related Work

Visual Grounding. Existing state-of-the-art VG methods can be coarsely grouped into two categories: two-stage and one-stage methods. Specifically, *two-stage methods* divide the VG task into two steps: proposal generation and proposal ranking. Firstly, a pretrained detector [14, 13, 33] is used to generate proposals. Then a multimodal ranking network measures the similarity between the query sentence and proposals and selects the best result [16, 29, 34, 47, 5]. This two-stage pipeline is more similar to the reasoning way of us humans. To achieve more fine-grained reasoning, some studies [46, 25] decompose the query sentence and image into several components (*e*.*g*., categories, spatial relations, attributes) and solve them independently. By breaking down the complex VG task into simpler ones, each component can be analyzed and interpreted individually, which enhances models' interpretability. *One-stage methods* directly predict the position of the referent [23, 28, 40]. Some studies treat VG as a conditional object detection problem [39, 6] and adopt an end-to-end training pipeline [51, 44]. Although these end-to-end trainable methods may achieve better performance, their interpretability is compromised at a cost. In this paper, we propose a model-agnostic prompt generation mechanism,

Interpretability in Visual Grounding. Two-stage VG methods always achieve better interpretability. Earlier works have explored incorporating tree structures into the reasoning process for VG [24, 35, 15]. However, they either are limited in the two-stage paradigm [24], or just consider spatially related information [35], which may not be sufficient. More importantly, existing methods are not compatible with the prevalent prompt tuning paradigm. They rely heavily on pretrained proposal detectors which are difficult to incorporate into existing pretrained models. In contrast, our TreePrompt generates structured prompts under the prompt tuning paradigm, which inherently have strong interpretability. Pretrained VL Models. Recently, VL models [11, 22, 26, 7, 21, 12, 48, 37, 27, 8, 18] have shown powerful comprehensiveness and expressiveness, and have greatly narrowed the gap between two modalities. The success can be attributed to the development of deep networks (*e*.*g*., Transformers [36]) and web-scale training datasets. By employing contrastive learning, methods like CLIP [32] and ALIGN [17], have demonstrated the ability to learn highly effective visual representations. Some methods commit to developing a unified model to work on multiple downstream tasks [26, 18, 37, 8, 7]. They are typically pretrained on web-scale datasets, then finetuned on downstream tasks, resulting in remarkable performances in VG. In this paper, we apply some pretrained models (*e*.*g*., OFA [37] and

Prompt Tuning for VL Tasks. Prompt engineering involves designing appropriate instructions for guiding pretrained models in generating desired outputs. Pretrained language models such as BERT [9] and GPT3 [2] have demonstrated impressive generalization abilities when given sophisticatedly handcrafted prompt templates. When extended to more complex multi-modal scenes, CPT [42] has also shown great success for zero-shot prompt learning. However, handcrafted prompts require extensive expert knowledge and experience, which limits their flexibility. In more recent works [20, 19, 50, 38], researchers have proposed methods for automating prompt engineering (*e*.*g*., continuous prompts). Zhou et al. [49] point out that this globally unified prompt may result in overfitting to the base class, and thus propose conditional prompt learning. However, prompt tuning suffers from severely poor interpretability. In this study, we explore a structured approach to generate

Given an image I and a textual query T, visual grounding (VG) aims to output the bounding box of the referent. Typically, the image is represented by a set of visual (patch/region) features, *i*.*e*., I = {v1, · · · , vK}, where K is the number of regions or patches. The query is represented by a sequence of word embeddings, *i*.*e*., T = {w1, · · · , wM}, where M is the length of word tokens and

3

prompts can be classified into two types: input-layer prompt and multi-layer prompt.

dt denotes the embedding of i-th word. Currently, learning continuous prompts [49] have improved VG performance and efficiency for their end-to-end trainability. Generally, continuous

which can be incorporated into both one-stage and two-stage methods.

VLT5 [8]) as backbones and validate the effectiveness of our method on them.

prompts to enhance prompt-tuning-based VG models' interpretability.

3.1 Preliminary: Prompt Tuning in Visual Grounding

3 Method

wi ∈ R

Pretrained Model

{p1, · · · , pN }, to augment the input text query, where pi ∈ R

[ _; Ti+1; Ii+1] = Fi([Pi

(a learned prompt that is the same as the input-layer prompt).

node, we concatenate its word embedding wi ∈ R

dl

generate the prompt of the current node, which will be detailed in Sec. 3.2.2.

augmented textual input Tˆ for the VL models becomes:

decoder. Specifically, the concatenation of prompt Pi

is input to i-th layer of Transformer (Fi):

Textual embeddings … Visual

Ii

3.2 TreePrompt

3.2.1 Tree Structure Generation

dependency relation label li ∈ R

Empirically we set ti and li

of ni

embeddings

Textual embeddings

… … … … … …

Tˆ = {v1, · · · , vN , w1, · · · , wM}. (1)

… …

(a) Input-layer prompt (b) Multi-layer prompt

Figure 2: Two types of prompts. (a) Input-layer prompt: It adds the prompt tokens to the beginning of the text. (b) Multi-layer prompt: It adds the prompt tokens to each layer of the Transformer.

Input-Layer Prompt. For continuous prompt, they usually introduce N learnable vectors, P =

textual embeddings dt. P is initialized randomly and prepended to the textual embeddings, then the

Multi-Layer Prompt. Since most pretrained VL models apply Transformer structure as their backbones, several prompt tuning methods attempt to add prompts to each layer of the encoder and

> ; Ti ; Ii

where [; ] is a concatenation operation and L is the total number of Transformer layers. _ will be replaced by Pi+1 in the next layer. While multi-layer prompts improve performance, their parameters expand to L times. Therefore multi-layer prompts P can be represented by P = {P1, P2, · · · , PL}. Discussion. During prompt-based training, the pretrained VL model is frozen, but the prompt can be updated through the network via backpropagation. In general, the current paradigm generates prompts in a holistic, indivisible, and unstructured manner, which we believe is the primary reason for poor interpretability. Therefore, it is essential to break down the prompt construction process into smaller, more manageable components and assemble them in a more structured, systematic manner.

As shown in Figure 3, TreePrompt consists of three main steps: 1) The given sentence is parsed into a tree, where each node represents a word in the sentence. The prompt will be constructed by following the path to the tree to more effectively capture the structural relationships between the words. 2) We develop three modules (Leaf, Rel, and Enti) to obtain intermediate prompts for each tree node (or word). 3) The prompt generated by TreePrompt will be integrated into a global prompt

Syntax tree is the essential component in TreePrompt and provides a hierarchical representation of the sentence structure. The tree form enables us to simply identify the syntactic relationships between words, such as subject-verb-object or adjective-noun pairs. By utilizing the off-the-shelf tools of SpaCy, we can easily and quickly parse a given query sentence into a syntax tree (*c*.*f*., Figure 3 (a)). The syntax tree is composed of the individual words in the sentence and each word corresponds to a node. Additionally, the DPT provides valuable information such as the part-of-speech (POS) tag t and the dependency relations label l between words. To fully leverage all the information, for i-th

ni = [wi

; ti ; li

to the same dimension and dn = dw + 2 × dl

4

to a suitable modular network (Leaf, Rel, or Enti) based on its dependency relation label to

! " #

Visual embeddings … … … … …

dp usually has the same dimension as

, textual embeddings Ti and visual embeddings

dw with the corresponding POS tag ti ∈ R

]. (3)

, to represent the embedding of i-th node as ni ∈ R

dl and

dn , *i*.*e*.,

. We pass the embedding

]) i = 1, 2, · · · , L, (2)

…

×

…

#

*women with flowers*… *remote*

…

0 i

concat

Positional embedding

Cross-Attn

Node representation

child-node prompt mean

ℎ! "

"

…

), (4)

dp . For the current node i,

MLP

…

MLP

Node prompt

Tree Prompt

Word embedding

POS tag embedding Dep. label embedding concat

FC

**horse**

**dobj**

Figure 3: An overview of TreePrompt model. (a) Dependency Parsing Tree. we first parse the input sentence into a tree structure via an off-the-shelf sentence parser. (b) Modular Network. we utilize it to build an intermediate prompt at each node along the tree path. (c) Composing Prompt. The prompts from tree are arranged in the order of traversal and combined with a global learned prompt to produce the final output. By prepending the intermediate prompt to the text and guiding the model's

The syntax tree provides a systematic and structured pathway for constructing prompts. However, to achieve a more sophisticated reasoning approach, we design three modules (Leaf, Rel, and Enti) that enable the VG model to perform human-like reasoning. Each module has a distinct role and is responsible for proceeding words based on their specific characteristics and functions within the sentence. To simplify the process of module design, we intentionally use *identical* network structures for all modules, while adjusting the parameters to fit the specific requirements of each module:

i = L2Norm(ni), ri = FC(n

dp , and ri can be served as the representation of the node. Note ri does not yet constitute the prompt of the current node. We construct the prompt in a bottom-up manner, following the path to the tree. Therefore, in order to obtain the prompt hi of i-th node, we first obtain the representation of its child nodes. Specifically, we concatenate the representation ri with the mean prompt embedding

], hi = MLP(fi),

dp+dp to hi ∈ R

is the number of its child nodes. This bottom-up strategy

where L2Norm is the L2 normalization, and FC is a fully connected layer mapping ni ∈ R

; ri

Module Functions. Lastly, we introduce the specific functions of the three modules in detail: 1) Leaf Module: It is particularly designed for leaf nodes which differ from intermediate nodes as they have no child nodes. This module generates the initial prompt, which serves as the most fundamental and basic clue of the sentence. Eventually, these clues are aggregated along the tree path to provide a complete understanding of the sentence. 2) Rel Module: It is employed for computing the nodes whose dependency relation label is an adjectival clause ("acl") or prepositional modifier ("prep"). These words are typically verbs or prepositions, so they tend to carry important relationships between objects in the sentence. By utilizing this module, we can obtain more precise information on the connections between various components. 3) Enti Module: It is created for the remaining nodes. These nodes usually contain information related to the entity in the sentence, such as attributes and classes. With the usage of Enti, we can extract more detailed information about entities and other

5

(remote) (holding) (women) …

prediction, the reasoning process depicted in the bottom left corner can be obtained.

(flowers) (with)

n 0

of its child nodes and feed it to a MLP to get the prompt of i-th node:

PNi j=1 h i j

allows us to gather relevant contextual information from surrounding words.

Ni

fi = [

where MLP represents a two-layer MLP mapping fi ∈ R

} is the prompt of its child nodes and Ni

information that may have been missed.

*Dependency Parsing Tree Modular Network*

**prep pobj prep pobj dobj NN**

A woman with flowers on her sweater holding a remote Prune Parse

**(***a***) (***b***)**

**acl**

woman with flowers on sweater holding remote **NN ADP NN ADP NN VB NN**

(sweater) (on)

3.2.2 Modular Network

*Composing Prompt*

**(***c***)**

ri ∈ R

{h i j Global prompt

…

dn to

(5)

Discussions. Notably, our modular network is intentionally kept simple and relies solely on textual features for two reasons: 1) Even though the parsing tools are already highly effective, they may still contain parsing errors. Adopting simple and uniform network structures can mitigate the negative impacts of these errors. 2) Pretrained models always process visual features differently, requiring extra preprocessing steps, which can significantly restrict the generalization. By solely utilizing textual inputs, we overcome this limitation and make our method applicable to any pretrained model.

The tree structure and modular networks enable the generation of prompts with sentence-specific information. To make optimal use of the tree prompt, we integrate it with a global prompt that contains general knowledge about data distribution. We first arrange the prompts generated by the tree in the reverse order of traversing (similar to pre-order traversal), with the root node being the first, denoted by H ∈ RM×dp . A learnable position embedding is then attached to the tree prompt.

> H = {h1, h2, · · · , hM}, G = {g1, g2, · · · , gN }, [ _; P] = CrossAttn([H; G]),

where CrossAttn represents a cross attention network. The first vector h1 is the intermediate prompt of the root node. To apply the input-layer prompt method, P is concatenated to the textual embeddings and then fed as textual input to the pretrained model. On the other hand, to implement

Datasets. We validated the effectiveness of our method on three challenging VG benchmarks: 1) RefCOCO [45], which contains 142,210 referring expressions for 50,000 objects in 19,994 images obtained through an interactive game interface. All expression-referent are split into train, val, testA, and testB sets. testA includes images with multiple people, while testB includes images with multiple objects. 2) RefCOCO+ [45], which contains 141,564 referring expressions for 49,856 objects in 19,992 images, also obtained from the same game interface and partitioned into train, val, testA, and testB splits. 3) RefCOCOg, which contains 104,560 referring expressions for 54,822 objects in 26,711 images obtained through a non-interactive method. We employ the same partitioning as [30]. Implementation Details and Metrics. We constructed a vocabulary for each dataset, containing words, POS tags and dependency relation labels that appeared more than once in the datasets. The embedding sizes for words, POS tags, and dependency labels are set to 300, 50, and 50, respectively. We initialized the word vectors using pretrained GloVe [31], while POS tags and dependency labels were initialized randomly. To obtain precise parsing results, we removed the punctuation and did not limit the expression lengths during parsing. The size of the prompt was the same as textual embeddings (*e*.*g*., 768 for OFAbase, VLT5 and 1024 for OFAlarge) and the length was set to 64. We conducted experiments on 2 NVIDIA 2080Ti GPUs for VLT5 and OFAbase, and on a single NVIDIA A100 GPU for OFAlarge. The weights of the pretrained model were fixed during training, while TreePrompt was trained with a batch size of 8 and a learning rate of 5e-5 via the AdamW optimizer. For OFA, we trained multi-layer prompts for 100 epochs in advance with batch size 16 and a learning rate of 0.03 following [38]. TreePrompt was trained with the same training objectives as the corresponding backbone models. We adopted the top-1 accuracy metric. A prediction is considered correct when the IoU between the predicted bounding box and ground truth is greater than 0.5.

Settings. TreePrompt is agnostic to the model architecture and can be easily integrated with any pretrained models. To evaluate the generalization, we incorporated TreePrompt into two powerful and representative pretrained methods: OFA [37] and VLT5 [8]. OFA utilizes multi-layer prompts and generates object coordinates directly, while VLT5 adds prompts before the text and requires a pretrained detector to supply proposals. For OFA, we first trained the global multi-layer prompt

6

N×dp through cross attention:

N×dp to Pt ∈ R

(6)

L×N×dp , shown in

L×N×dp .

3.2.3 Integrated with Global Prompt

4.1 Experimental Settings and Details

4.2 Model Agnostic Generalization

4 Experiments

Then, the tree prompt will be fused with the global prompt G ∈ R

the multi-layer prompt approach, we use an MLP to map P ∈ R

Figure 3 (c). Pt will be directly added to the global multi-layer prompt Pg ∈ R

Table 1: Performance of different models. †denotes the results from our implementation. Models Prompt RefCOCO RefCOCO+ RefCOCOg

VL-T5 +Continuous 71.33 76.91 65.71 60.58 67.05 50.50 66.18 66.18

Table 2: Comparison to state-of-the-art VG methods. The metric is top-1 accuracy(%). †denotes the

Models Finetune RefCOCO RefCOCO+ RefCOCOg

UNITER [7] 81.41 87.04 74.17 75.90 81.45 66.70 74.86 75.77 VILLA [12] 82.39 87.48 74.84 76.17 81.54 66.84 76.18 76.71

following [38], then they were fixed during training TreePrompt. We use these continuous prompts (Continuous) to build a strong baseline for comparison. All results are reported in Table 1.

Results. TreePrompt consistently improves the performance of the multiple baselines across three benchmarks. The most significant enhancement is achieved by integrating TreePrompt with VLT5 (*e*.*g*., 2.63%, 1.83%, and 2.00% absolute performance gains on three benchmarks). We believe that the difference in improvement may be caused by the multi-layer prompt used in OFA. The multi-layer prompt contains a large number of prompt parameters (*e*.*g*., with OFAbase being 12 times larger and OFAlarge being 24 times larger than the input-layer prompt), which is sufficient for prompt tuning. Nevertheless, TreePrompt is still able to improve performance due to the benefits brought by tree

Settings. To the best of our knowledge, most existing prompt tuning methods in VG adopt the setting of zero-shot or few-shot, which is different from our methods. Therefore, we compared our prompt tuning method with other complete model finetuning methods. As a matter of fact, such a comparison is also unfair. Finetuning the whole model involves much larger parameters than prompt tuning methods. To provide a comprehensive comparison, we present the following state-of-the-art, web-scale data pretrained VL models for comparison: UNITER [7], VILLA [12],

Results. Although such a comparison is not fair and presents a significant challenge for TreePrompt, it still outperforms most of them, demonstrating the superiority of our TreePrompt. One may concern the improvement was solely brought by the strong backbone. We argue that TreePrompt achieves comparable performance to the powerful backbone OFA, with a much more challenging setting of only finetuning prompts. As the result shown in Table 2, the margin is narrowed to 0.13%, 0.7% and -0.18%. In general, the experimental results demonstrate the effectiveness of our TreePrompt.

7

[8] 79.46 85.61 72.91 69.17 76.95 59.62 72.39 72.14 MDETR [18] 87.51 90.40 82.67 81.13 85.52 72.96 83.35 83.31 UNICORN [41] 88.29 90.42 83.06 80.30 85.05 71.88 83.44 83.93 PEVL [43] 89.60 92.50 85.00 83.00 88.40 74.50 87.10 86.30 OFALarge [37] 90.05 92.93 85.26 84.49 90.10 77.77 84.54 85.20 +TreePrompt 89.92 92.03 84.85 83.79 89.05 76.05 84.72 84.89

Multi-Layer Prompt† 82.28 86.16 77.02 75.17 80.61 65.94 74.88 75.81 +Continuous 82.50 86.80 77.17 74.99 80.82 66.48 74.43 75.14 +TreePrompt 83.36 87.11 78.19 75.29 81.05 66.52 75.12 76.16

Multi-Layer Prompt† 89.38 92.45 84.20 83.24 89.03 75.76 84.15 84.81 +Continuous 89.25 92.10 83.91 83.22 89.12 75.60 84.66 84.70 +TreePrompt 89.92 92.03 84.85 83.79 89.05 76.05 84.72 84.89

+TreePrompt 73.96 79.37 67.81 62.40 68.88 51.32 68.10 68.18

entire model val testA testB val testA testB val test

OFAbase

OFAlarge

VLT5†

results from our implementations.

structure and modular network.

4.3 Comparison with Fully Finetuning Models

VLT5 [8], MDETR [18], UNICORN [41], PEVL [43], and OFA [37].

val testA testB val testA testB val test

(d) A white and blue bag on top of a black suitcase

**bag**

**chair**

**table near**

0 10 20 30

Timesteps

RefCOCOg Epoch 0 10 20 30

**white**

**suitcase of top on**

**pillow with sofa**

**black**

**white and pink stripes with**

(b) A sofa with pink and white stripes with a white/off-white pillow

**white**

(f) First sandwich on the left just beneath the fork (g) a white chair near table

3.8

3.8

Loading [MathJax]/extensions/MathMenu.js

3.9

3.9

4.0

4.0

Loss

Loss

4.1

4.1

**blue**

**left on first**

Figure 4: Qualitative results of TreePrompt on RefCOCOg. Enti, Rel, and Leaf modules are distinguished by their respective red, green, and blue colors of the words. GT and predictions for the original image are displayed on the right side. We present four successful cases shown in (a), (b), (c), and (d), along with three additional instances of failure for comparison, shown in (e), (f), and (g).

Continuous Prompt TreePrompt

0 10 20 30

Timesteps

Figure 5: Comparison of convergence rate between TreePrompt and continuous prompt on three

Effectiveness of Tree Structure and Modular Networks. We conducted extensive ablation studies to expose the effectiveness of TreePrompt. We decomposed our method and validated each part to illustrate their functions. In Table 5, the term "Tree" indicates that the model utilizes tree structures to construct prompts, while the term "Module" represents the use of modular networks. We mark the use of each component (*i*.*e*., Tree and Module) with a check mark to indicate its activation and a

*Results*. As shown in Table 5, we can observe that the complete TreePrompt outperforms the incomplete version. It demonstrates the necessity of both components. The difference in the performance of the two components reveals their different roles. Compared with TreePrompt w/o Module, TreePrompt w/o Tree shows better performance in RefCOCO, while it is the opposite in RefCOCOg. We attribute it to the difference in datasets. RefCOCO has shorter and simpler expressions as compared to RefCOCOg, which has longer and more complex expressions (3.5 *v*.*s*. 8.4 words per sentence on average). Modular networks are effective for precise and fine-grained reasoning, while tree structure assists in organizing sentence structure and capturing semantic information. Convergence Rate. We compared the convergence rate of our TreePrompt and the baseline, *i*.*e*., continuous prompt. We kept the learning rate, prompt length, and other factors the same, and

8

RefCOCO+ Epoch 0 10 20 30

3.7

3.7

Loading [MathJax]/extensions/MathMenu.js

3.8

3.8

3.9

3.9

Loss

Loss

cross mark to indicate its deactivation in the corresponding experiment.

4

4.0

**beneath sandwich**

(c) an old man is sitting near a woman holding an umbrella

**fork**

**just**

**man**

**is sitting old**

0 10 20 30

Timesteps

RefCOCO Epoch 0 10 20 30

**laptop**

(a) The bigger laptop on the left

**umbrella**

**foremost**

(e) The foremost donut

3.7

3.7

Loading [MathJax]/extensions/MathMenu.js

4.4 Ablation Studies

3.8

3.8

3.9

3.9

Loss

Loss

benchmarks.

4

4.0

**donut**

**holding woman near**

**bigger**

**left on**

**and**

**white off white**

Table 3: Top-1 accuracy(%) of ablation models

Tree Module RefCOCO RefCOCO+ RefCOCOg

% % 76.9 65.7 67.1 50.5 66.2 % ! 78.4 67.1 68.3 51.2 67.5 ! % 78.3 66.7 68.5 50.5 67.8 ! ! 79.4 67.8 68.9 51.3 68.2

testA testB testA testB test

Table 4: Top-1 accuracy(%) of TreePrompt with

Prompt RefCOCO RefCOCO+ RefCOCOg length testA testB testA testB test 32 78.7 67.5 67.3 49.8 65.5 64 79.4 67.8 68.9 51.3 68.2 100 79.9 68.6 67.5 49.4 65.8 128 79.5 68.2 67.8 49.3 65.2

different prompt lengths.

compared the convergence trend of the loss between the two methods. In order to comprehensively demonstrate the advantages of our method in terms of convergence rate, we conducted extensive experiments on three benchmarks (*i*.*e*., RefCOCO, RefCOCO+, and RefCOCOg). The results have

*Results*. The proposed TreePrompt decomposes the VG task into simpler components and solves them separately. This "divide and conquer" strategy not only leads to improved performance and fine-grained reasoning, but also speeds up convergence. As is shown in Figure 5, compared with the baseline, we observe that: 1) our method requires less time to reach the same loss, and 2) we can achieve a lower loss within the same time frame. These findings suggest that the model can learn more efficiently when dealing with multiple simple problems instead of a single complex one.

Impact of Prompt Length. Generally, prompt tuning is sensitive to the length of the prompt. Consequently, we experimented with prompts of different lengths to reveal the impact of prompt lengths on performance. Empirically, we tried 4 different lengths, *i*.*e*., 32, 64, 100, 128. For a fair comparison, all the prompt tokens were initialized randomly. The result is shown in Table 6.

*Results*. In Table 6, a prompt length of 64 generally shows the best performance compared to other lengths. It is intuitive that a longer prompt can carry more information and thus boost performance. However, when the prompt length exceeds 100, the inclusion of more parameters has a negative impact on performance. This may be because having too many parameters will cause the model to

We illustrate the qualitative results of TreePrompt in Figure 4. By outputting the intermediate prompts of nodes, we are able to gain insight into the reasoning process and mechanism of TreePrompt. Through the qualitative results, we can observe that: 1) For leaf nodes that contain rich visual information, Leaf is able to acquire the most basic information, which provides significant assistance for reasoning, *e*.*g*., "*umbrella*" in (c). Even for nodes containing abstract visual information, like "*bigger*" in (a), our model can identify the biggest computer on the right. 2) Rel is responsible for resolving words that may involve multiple objects. It helps to identify the relationship between the objects and the action or attribute described by the word *e*.*g*., "*holding*" and "*sitting*" in (c). For words with ambiguous meanings like "*of* ", Rel may struggle to identify the object. 3) Enti usually has multiple subtrees, so it is capable of aggregating relevant information. 4) Along the tree path, the information will be assembled. For example, in Figure 4 (d), it is more likely to output the black bag because of the words "black" and "top". However, by combining the prompts generated from subtrees, the root node is able to provide more comprehensive information about the entity, leading to correct grounding results. 5) Even for the failure cases, we can still find the reason behind it by

analyzing its reasoning process, which is impossible for previous unstructured prompts.

Conclusion. In this paper, we investigated an alternative way to construct prompts for explainable visual grounding, which has been overlooked by previous prompt tuning methods. Particularly, we drew attention to the shortcomings of the holistic and general prompt generation and inference process, which may lead to poor interpretability. To address this issue, we proposed a novel prompt construction paradigm dubbed TreePrompt, which adopts a structured manner to compose prompts. By leveraging the language syntax tree, TreePrompt constructs prompt step-by-step, and thus achieves

9

overfit the training data, leading to a poor generalization of testing data.

on the three benchmarks.

shown in Figure 5.

4.5 Qualitative Analysis

5 Conclusion and Limitation

fine-grained reasoning process and explainable results. Meanwhile, TreePrompt is compatible with various VL pretrained backbones, and can be integrated into any state-of-the-art pretrained multimodal methods. Moving forward, we aim to extend applications of TreePrompt to other multimodal tasks which confront similar interpretability challenges in prompt tuning, *e*.*g*., image captioning and VQA. Limitations. Our method relies on pretrained VL models for visual grounding and thus may inherit some of the limitations and biases of those pretrained models. Another challenge is that our method requires an off-the-shelf sentence parser to generate the tree structure, which may not always be accurate or efficient. While we developed some approaches to mitigate parsing errors (detailed in

TreePrompt. Our method has no apparent ethical risks in terms of social negative impact and privacy violation, as all three benchmarks (*i*.*e*., RefCOCO [45], RefCOCO+ [45] and RefCOCOg [29]) used

Pretrained backbone. Since our method relies on pretrained multimodal models trained on webscale data, which may unintentionally include private information, inappropriate image-text pairs, or potential biases. Given these considerations, further investigation is recommended before deploying

The detailed results of ablation studies related to modular network and prompt length are shown in Table 5 and Table 6, respectively. In order to provide a comprehensive analysis, we include the performance on the validation set, which was not presented in the main paper due to space limitations. Effectiveness of Tree Structure and Modular Networks. The results are consistent with our observation in the main paper. TreePrompt w/o Tree shows superior performance in RefCOCO compared to TreePrompt w/o Module, whereas the opposite trend is observed in RefCOCOg. This suggests that modular networks contribute to precise and fine-grained reasoning, while the tree

Prompt Length. We conducted experiments using a prompt length of 10 for comparison purposes,

In this section, we provide additional qualitative results to better understand the internal prompt construction process employed by TreePrompt. Figure 7 shows successful cases, illustrating the tree structures, the prompt generation process at each intermediate node, and the corresponding final results. Conversely, Figure 6 showcases examples of failure cases for comparative analysis. Some failures arise from misunderstandings of the sentence (*e*.*g*., Figure 6(a), (b), and (c)), while others are

10

structure assists in organizing sentence structure and capturing semantic information.

and generally, a prompt length of 64 yielded the best performance.

Section 3.2.2), it still could impact the accuracy of results.

1. Broader impacts are discussed in Section A.

B Detailed Results of Ablation Studies

3. More qualitative results are shown in Section C.

In the appendix, we provide more details in the following sections:

2. More detailed ablation study experiments are reported in Section B.

Appendix

A Broader Impacts

our method in practice.

are publicly available and transparent.

C More Qualitative Results

attributed to parsing errors (*e*.*g*., Figure 6(d)).

**sheep**

**large**

**girl helping boy**

**ride**

**skateboard**

(a) A horned sheep to the left of two other horned sheep

(c) Giraffe looking to the left side (d) A boy helping a girl ride a skateboard

Figure 6: More incorrect qualitative results of TreePrompt on RefCOCOg. Enti, Rel, and Leaf modules are distinguished by their respective red, green, and blue colors of the words. GT and

Table 5: Top-1 accuracy(%) of ablation models on the three benchmarks.

Tree Module RefCOCO RefCOCO+ RefCOCOg

% % 71.3 76.9 65.7 60.6 67.1 50.5 66.2 66.2 % ! 73.4 78.4 67.1 61.6 68.3 51.2 67.5 67.5 ! % 72.7 78.3 66.7 61.3 68.5 50.5 68.3 67.8 ! ! 74.0 79.4 67.8 62.4 68.9 51.3 68.1 68.2

Table 6: Top-1 accuracy(%) of TreePrompt with different prompt lengths.

Prompt RefCOCO RefCOCO+ RefCOCOg length val testA testB val testA testB val test 10 70.8 77.0 64.7 57.7 65.3 47.7 63.4 63.7 32 73.0 78.7 59.3 67.5 67.3 49.8 64.9 65.5 64 74.0 79.4 67.8 62.4 68.9 51.3 68.1 68.2 100 75.0 79.9 68.6 59.0 67.5 49.4 65.5 65.8 128 74.3 79.5 68.2 59.0 67.8 49.3 65.4 65.2

11

val testA testB val testA testB val test

**sheep**

**two**

**other**

**horned**

**left side**

**table of**

**left**

**side to looking giraffe**

predictions for the original image are displayed.

**of left to**

**horned**

**on stand on sitting pizza**

(b) Large pizza sitting on stand on the left side of the table

**chair**

(b) A person sitting in a chair

**black**

**white stripe with**

**splattered on**

**its back**

**procession**

**of**

(d) A black cow with a white splattered stripe on its back

**rear in elephant**

**sitting person**

**cow**

**imac computer of front**

**desk on white**

**outer jets between**

**background in**

**force in 2**

**dark in**

**umbrella**

**of**

**brown**

(e) A person in dark clothing holding an umbrella in the background

(c) 2 air force jets in between the outer jets

(a) White keyboard sitting in front of an imac computer on a desk.

**air**

(f) Elephant in rear of procession (f) Brown chair on far right of picture.

Figure 7: More correct qualitative results of TreePrompt on RefCOCOg. Enti, Rel, and Leaf modules are distinguished by their respective red, green, and blue colors of the words. To facilitate

12

better observation, both the original image and the ground truth (GT) are displayed.

**picture**

**far**

**in sitting keyboard**

**jets**

**holding**

**right on chair**

**clothing person**

**in**

References

*NeurIPS*, pages 1877–1901, 2020.

*EMNLP*, pages 740–750, 2014.

In *ICML*, pages 1931–1942. PMLR, 2021.

infoloob outperform clip. *NeurIPS*, pages 20450–20468, 2022.

object detection and semantic segmentation. In *CVPR*, pages 580–587, 2014.

[13] Ross Girshick. Fast r-cnn. In *ICCV*, pages 1440–1448, 2015.

language object retrieval. In *CVPR*, pages 4555–4564, 2016.

supervision. In *ICML*, pages 4904–4916. PMLR, 2021.

*arXiv preprint arXiv:2104.08691*, 2021.

*ECCV*, pages 121–137. Springer, 2020.

*arXiv preprint arXiv:2110.05208*, 2021.

*preprint arXiv:2101.00190*, 2021.

*ACM MM*, pages 7–16, 2014.

[1] Stanislaw Antol, Aishwarya Agrawal, Jiasen Lu, Margaret Mitchell, Dhruv Batra, C Lawrence Zitnick,

[2] Tom Brown, Benjamin Mann, Nick Ryder, Melanie Subbiah, Jared D Kaplan, Prafulla Dhariwal, Arvind Neelakantan, Pranav Shyam, Girish Sastry, Amanda Askell, et al. Language models are few-shot learners.

[3] Danqi Chen and Christopher D Manning. A fast and accurate dependency parser using neural networks. In

[4] Howard Chen, Alane Suhr, Dipendra Misra, Noah Snavely, and Yoav Artzi. Touchdown: Natural language navigation and spatial reasoning in visual street environments. In *CVPR*, pages 12538–12547, 2019. [5] Long Chen, Wenbo Ma, Jun Xiao, Hanwang Zhang, and Shih-Fu Chang. Ref-nms: Breaking proposal bottlenecks in two-stage referring expression grounding. In *AAAI*, number 2, pages 1036–1044, 2021. [6] Xinpeng Chen, Lin Ma, Jingyuan Chen, Zequn Jie, Wei Liu, and Jiebo Luo. Real-time referring expression comprehension by single-stage grounding network. In *arXiv preprint arXiv:1812.03426*, 2018.

[7] Yen-Chun Chen, Linjie Li, Licheng Yu, Ahmed El Kholy, Faisal Ahmed, Zhe Gan, Yu Cheng, and Jingjing Liu. Uniter: Universal image-text representation learning. In *ECCV*, pages 104–120. Springer, 2020. [8] Jaemin Cho, Jie Lei, Hao Tan, and Mohit Bansal. Unifying vision-and-language tasks via text generation.

[9] Jacob Devlin, Ming-Wei Chang, Kenton Lee, and Kristina Toutanova. Bert: Pre-training of deep bidirec-

[10] Fangxiang Feng, Xiaojie Wang, and Ruifan Li. Cross-modal retrieval with correspondence autoencoder. In

[11] Andreas Fürst, Elisabeth Rumetshofer, Johannes Lehner, Viet T Tran, Fei Tang, Hubert Ramsauer, David Kreil, Michael Kopp, Günter Klambauer, Angela Bitto, et al. Cloob: Modern hopfield networks with

[12] Zhe Gan, Yen-Chun Chen, Linjie Li, Chen Zhu, Yu Cheng, and Jingjing Liu. Large-scale adversarial training for vision-and-language representation learning. *NeurIPS*, pages 6616–6628, 2020.

[14] Ross Girshick, Jeff Donahue, Trevor Darrell, and Jitendra Malik. Rich feature hierarchies for accurate

[15] Richang Hong, Daqing Liu, Xiaoyu Mo, Xiangnan He, and Hanwang Zhang. Learning to compose and reason with language tree structures for visual grounding. *IEEE TPAMI*, pages 684–696, 2022. [16] Ronghang Hu, Huazhe Xu, Marcus Rohrbach, Jiashi Feng, Kate Saenko, and Trevor Darrell. Natural

[17] Chao Jia, Yinfei Yang, Ye Xia, Yi-Ting Chen, Zarana Parekh, Hieu Pham, Quoc Le, Yun-Hsuan Sung, Zhen Li, and Tom Duerig. Scaling up visual and vision-language representation learning with noisy text

[18] Aishwarya Kamath, Mannat Singh, Yann LeCun, Gabriel Synnaeve, Ishan Misra, and Nicolas Carion. Mdetr-modulated detection for end-to-end multi-modal understanding. In *ICCV*, pages 1780–1790, 2021.

[19] Brian Lester, Rami Al-Rfou, and Noah Constant. The power of scale for parameter-efficient prompt tuning.

[20] Xiang Lisa Li and Percy Liang. Prefix-tuning: Optimizing continuous prompts for generation. *arXiv*

[21] Xiujun Li, Xi Yin, Chunyuan Li, Pengchuan Zhang, Xiaowei Hu, Lei Zhang, Lijuan Wang, Houdong Hu, Li Dong, Furu Wei, et al. Oscar: Object-semantics aligned pre-training for vision-language tasks. In

[22] Yangguang Li, Feng Liang, Lichen Zhao, Yufeng Cui, Wanli Ouyang, Jing Shao, Fengwei Yu, and Junjie Yan. Supervision exists everywhere: A data efficient contrastive language-image pre-training paradigm.

13

tional transformers for language understanding. *arXiv preprint arXiv:1810.04805*, 2018.

and Devi Parikh. Vqa: Visual question answering. In *ICCV*, pages 2425–2433, 2015.

[23] Yue Liao, Si Liu, Guanbin Li, Fei Wang, Yanjie Chen, Chen Qian, and Bo Li. A real-time cross-modality correlation filtering method for referring expression comprehension. In *CVPR*, pages 10880–10889, 2020.

[24] Daqing Liu, Hanwang Zhang, Feng Wu, and Zheng-Jun Zha. Learning to assemble neural module tree

[25] Xihui Liu, Zihao Wang, Jing Shao, Xiaogang Wang, and Hongsheng Li. Improving referring expression

[26] Jiasen Lu, Dhruv Batra, Devi Parikh, and Stefan Lee. Vilbert: Pretraining task-agnostic visiolinguistic

[27] Jiasen Lu, Christopher Clark, Rowan Zellers, Roozbeh Mottaghi, and Aniruddha Kembhavi. Unified-io: A unified model for vision, language, and multi-modal tasks. *arXiv preprint arXiv:2206.08916*, 2022. [28] Gen Luo, Yiyi Zhou, Xiaoshuai Sun, Liujuan Cao, Chenglin Wu, Cheng Deng, and Rongrong Ji. Multi-task collaborative network for joint referring expression comprehension and segmentation. In *CVPR*, 2020. [29] Junhua Mao, Jonathan Huang, Alexander Toshev, Oana Camburu, Alan L Yuille, and Kevin Murphy. Generation and comprehension of unambiguous object descriptions. In *CVPR*, pages 11–20, 2016. [30] Varun K Nagaraja, Vlad I Morariu, and Larry S Davis. Modeling context between objects for referring

[31] Jeffrey Pennington, Richard Socher, and Christopher D Manning. Glove: Global vectors for word

[32] Alec Radford, Jong Wook Kim, Chris Hallacy, Aditya Ramesh, Gabriel Goh, Sandhini Agarwal, Girish Sastry, Amanda Askell, Pamela Mishkin, Jack Clark, Gretchen Krueger, and Ilya Sutskever. Learning

[33] Shaoqing Ren, Kaiming He, Ross Girshick, and Jian Sun. Faster r-cnn: Towards real-time object detection

[34] Anna Rohrbach, Marcus Rohrbach, Ronghang Hu, Trevor Darrell, and Bernt Schiele. Grounding of textual

[35] Sanjay Subramanian, Will Merrill, Trevor Darrell, Matt Gardner, Sameer Singh, and Anna Rohrbach. Reclip: A strong zero-shot baseline for referring expression comprehension. *arXiv preprint arXiv:2204.05991*,

[36] Ashish Vaswani, Noam Shazeer, Niki Parmar, Jakob Uszkoreit, Llion Jones, Aidan N Gomez, Łukasz

[37] Peng Wang, An Yang, Rui Men, Junyang Lin, Shuai Bai, Zhikang Li, Jianxin Ma, Chang Zhou, Jingren Zhou, and Hongxia Yang. Ofa: Unifying architectures, tasks, and modalities through a simple sequence-to-

[38] Hao Yang, Junyang Lin, An Yang, Peng Wang, Chang Zhou, and Hongxia Yang. Prompt tuning for

[39] Sibei Yang, Guanbin Li, and Yizhou Yu. Dynamic graph attention for referring expression comprehension.

[40] Zhengyuan Yang, Boqing Gong, Liwei Wang, Wenbing Huang, Dong Yu, and Jiebo Luo. A fast and

[41] Zhengyuan Yang, Zhe Gan, Jianfeng Wang, Xiaowei Hu, Faisal Ahmed, Zicheng Liu, Yumao Lu, and Lijuan Wang. Crossing the format boundary of text and boxes: Towards unified vision-language modeling.

[42] Yuan Yao, Ao Zhang, Zhengyan Zhang, Zhiyuan Liu, Tat-Seng Chua, and Maosong Sun. Cpt: Colorful prompt tuning for pre-trained vision-language models. *arXiv preprint arXiv:2109.11797*, 2021.

[43] Yuan Yao, Qianyu Chen, Ao Zhang, Wei Ji, Zhiyuan Liu, Tat-Seng Chua, and Maosong Sun. Pevl: Positionenhanced pre-training and prompt tuning for vision-language models. *arXiv preprint arXiv:2205.11169*,

14

networks for visual grounding. In *ICCV*, pages 4673–4682, 2019.

representations for vision-and-language tasks. *NeurIPS*, 2019.

grounding with cross-modal attention-guided erasing. In *CVPR*, 2019.

expression understanding. In *ECCV*, pages 792–807. Springer, 2016.

transferable visual models from natural language supervision, 2021.

phrases in images by reconstruction. In *ECCV*, pages 817–834. Springer, 2016.

Kaiser, and Illia Polosukhin. Attention is all you need. *NeurIPS*, 2017.

accurate one-stage approach to visual grounding. In *ICCV*, 2019.

*arXiv preprint arXiv:2111.12085*, 2021.

sequence learning framework. In *ICML*, pages 23318–23340. PMLR, 2022.

generative multimodal pretrained models. *arXiv preprint arXiv:2208.02532*, 2022.

representation. In *EMNLP*, pages 1532–1543, 2014.

with region proposal networks. *NeurIPS*, 28, 2015.

2022.

In *ICCV*, 2019.

2022.

[44] Jiabo Ye, Junfeng Tian, Ming Yan, Xiaoshan Yang, Xuwu Wang, Ji Zhang, Liang He, and Xin Lin. Shifting more attention to visual backbone: Query-modulated refinement networks for end-to-end visual grounding.

[45] Licheng Yu, Patrick Poirson, Shan Yang, Alexander C Berg, and Tamara L Berg. Modeling context in

[46] Licheng Yu, Zhe Lin, Xiaohui Shen, Jimei Yang, Xin Lu, Mohit Bansal, and Tamara L Berg. Mattnet: Modular attention network for referring expression comprehension. In *CVPR*, pages 1307–1315, 2018. [47] Chenchi Zhang, Wenbo Ma, Jun Xiao, Hanwang Zhang, Jian Shao, Yueting Zhuang, and Long Chen. Vl-nms: Breaking proposal bottlenecks in two-stage visual-language matching. *ACM MM*, 2021. [48] Pengchuan Zhang, Xiujun Li, Xiaowei Hu, Jianwei Yang, Lei Zhang, Lijuan Wang, Yejin Choi, and Jianfeng Gao. Vinvl: Revisiting visual representations in vision-language models. In *CVPR*, pages

[49] Kaiyang Zhou, Jingkang Yang, Chen Change Loy, and Ziwei Liu. Conditional prompt learning for

[50] Kaiyang Zhou, Jingkang Yang, Chen Change Loy, and Ziwei Liu. Learning to prompt for vision-language

[51] Chaoyang Zhu, Yiyi Zhou, Yunhang Shen, Gen Luo, Xingjia Pan, Mingbao Lin, Chao Chen, Liujuan Cao, Xiaoshuai Sun, and Rongrong Ji. Seqtr: A simple yet universal network for visual grounding. In *ECCV*,

15

In *CVPR*, pages 15502–15512, 2022.

5579–5588, 2021.

vision-language models, 2022.

pages 598–615. Springer, 2022.

models. *IJCV*, pages 2337–2348, 2022.

referring expressions. In *ECCV*, pages 69–85. Springer, 2016.

