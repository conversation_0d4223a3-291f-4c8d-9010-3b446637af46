'use client';

import { useEffect, useState } from 'react';
import { useModelContext } from '@/context/ModelContext';

interface SubScore {
  score: number;
  maxScore: number;
  explanation: string;
}

interface CategoryScore {
  total: number;
  subcriteria: {
    [key: string]: SubScore;
  };
}

interface EvaluationData {
  metadata: {
    title: string;
    authors: string[];
    year: number;
    doi: string;
  };
  scores: {
    relevance: CategoryScore;
    applicability: CategoryScore;
    scientific: CategoryScore;
    innovation: CategoryScore;
    alignment: CategoryScore;
    total: number;
  };
  summary: string;
  executableStrategies: Array<{
    name: string;
    implementation: string[];
    resources: string;
    results: string;
  }>;
  codeExamples: string;
  implementationNotes: {
    requirements: string[];
    challenges: string[];
    optimization: string[];
  };
  conclusion: string;
  // Keeping these for backward compatibility
  selected?: boolean;
  selectionMotivation?: string;
}

interface EvaluationDetailsProps {
  filename: string;
}

export default function EvaluationDetails({ filename }: EvaluationDetailsProps) {
  const [evaluationData, setEvaluationData] = useState<EvaluationData | null>(null);
  const [rawMarkdown, setRawMarkdown] = useState<string>('');
  const [showRawMarkdown, setShowRawMarkdown] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Get the selected model from context
  const { selectedModel } = useModelContext();

  useEffect(() => {
    const fetchEvaluation = async () => {
      try {
        console.log('Input filename:', filename);
        console.log('Selected model:', selectedModel);
        
        // Map the URL parameter to the actual filename if needed
        let actualFilename = filename;
        if (filename === 'adaptive_temperature_sampling') {
          actualFilename = 'Adaptive_Temperature_Sampling';
        }
        
        // Use the new folder structure and model-specific files
        // Use the selected model directly without any mapping
        const fullPath = `/papers/evaluations/${actualFilename}/Evaluation_${actualFilename}_${selectedModel}.md`;
        console.log('Attempting to fetch from:', fullPath);
        
        const response = await fetch(fullPath);
        console.log('Response status:', response.status);
        
        if (!response.ok) {
          // Try JSON file as an alternative
          const jsonPath = `/papers/evaluations/${actualFilename}/Evaluation_${actualFilename}_${selectedModel}.json`;
          console.log('MD file not found, trying JSON file:', jsonPath);
          
          const jsonResponse = await fetch(jsonPath);
          if (!jsonResponse.ok) {
            throw new Error(`Failed to load evaluation: ${response.status}`);
          }
          
          const jsonContent = await jsonResponse.json();
          // Convert JSON to markdown format for backward compatibility
          const content = `# ${jsonContent.metadata.title}\n\n${jsonContent.paper_summary || jsonContent.paperSummary || ''}`;
          console.log('Successfully loaded JSON content');
          setEvaluationData(jsonContent);
          setRawMarkdown(content);
          return;
        }
        
        const content = await response.text();
        console.log('Successfully loaded content length:', content.length);
        
        // Store the raw markdown
        setRawMarkdown(content);
        
        // Parse the markdown content into structured data
        const data = parseEvaluationMarkdown(content);
        setEvaluationData(data);
      } catch (err) {
        console.error('Detailed error information:', {
          filename,
          error: err,
          errorMessage: err instanceof Error ? err.message : 'Unknown error',
          errorStack: err instanceof Error ? err.stack : undefined
        });
        setError(err instanceof Error ? err.message : 'Failed to load evaluation');
      }
    };

    fetchEvaluation();
  }, [filename, selectedModel]);

  if (error) {
    return <div className="text-red-500">Error: {error}</div>;
  }

  if (!evaluationData) {
    return <div className="animate-pulse">Loading evaluation...</div>;
  }

  const renderScoreBar = (score: number, maxScore: number) => (
    <div className="w-full bg-gray-100 rounded-full h-3">
      <div
        className={`h-3 rounded-full transition-all duration-300 ${
          (score/maxScore) >= 0.8 ? 'bg-green-500' :
          (score/maxScore) >= 0.6 ? 'bg-blue-500' :
          (score/maxScore) >= 0.4 ? 'bg-yellow-500' :
          'bg-red-500'
        }`}
        style={{ width: `${(score/maxScore) * 100}%` }}
      />
    </div>
  );

  const renderSubcriteria = (category: CategoryScore, title: string) => (
    <div className="space-y-4">
      <h3 className="text-xl font-semibold text-gray-900">{title}</h3>
      <div className="space-y-6">
        {Object.entries(category.subcriteria).map(([name, data]) => (
          <div key={name} className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-base font-medium text-gray-900 capitalize">
                {name.replace(/_/g, ' ')}
              </span>
              <span className="text-base font-medium text-gray-900">
                {data.score}/{data.maxScore}
              </span>
            </div>
            {renderScoreBar(data.score, data.maxScore)}
            <p className="text-sm text-gray-600 mt-1">{data.explanation}</p>
          </div>
        ))}
        <div className="pt-2 border-t border-gray-200">
          <div className="flex justify-between items-center font-semibold">
            <span>Total</span>
            <span>{category.total}/100</span>
          </div>
          {renderScoreBar(category.total, 100)}
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-8">
      {/* View Toggle Button */}
      <div className="flex justify-end">
        <button
          onClick={() => setShowRawMarkdown(!showRawMarkdown)}
          className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          {showRawMarkdown ? (
            <>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.707l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z" clipRule="evenodd" />
              </svg>
              Show Formatted View
            </>
          ) : (
            <>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.707l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z" clipRule="evenodd" />
              </svg>
              Show Raw Markdown
            </>
          )}
        </button>
      </div>

      {showRawMarkdown ? (
        // Raw Markdown View
        <div className="bg-gray-50 rounded-lg p-6 font-mono text-sm">
          <pre className="whitespace-pre-wrap break-words text-gray-900">
            {rawMarkdown}
          </pre>
        </div>
      ) : (
        // Formatted View
        <>
          {/* Metadata Section */}
          <div className="bg-white rounded-lg p-6 border border-gray-200">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">{evaluationData.metadata.title}</h2>
            <dl className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <dt className="text-sm font-medium text-gray-700 mb-1">Authors</dt>
                <dd className="text-base text-gray-900">{evaluationData.metadata.authors.join(', ')}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-700 mb-1">Year</dt>
                <dd className="text-base text-gray-900">{evaluationData.metadata.year}</dd>
              </div>
              <div className="md:col-span-2">
                <dt className="text-sm font-medium text-gray-700 mb-1">DOI</dt>
                <dd className="text-base">
                  <a href={`https://doi.org/${evaluationData.metadata.doi}`} target="_blank" rel="noopener noreferrer" 
                     className="text-blue-600 hover:text-blue-800">
                    {evaluationData.metadata.doi}
                  </a>
                </dd>
              </div>
            </dl>
          </div>

          {/* Scores Section */}
          <div className="space-y-8">
            <div className="bg-white rounded-lg p-6 border border-gray-200">
              {renderSubcriteria(evaluationData.scores.relevance, "Relevance for AI Use in Software Development")}
            </div>
            <div className="bg-white rounded-lg p-6 border border-gray-200">
              {renderSubcriteria(evaluationData.scores.applicability, "Practical Applicability")}
            </div>
            <div className="bg-white rounded-lg p-6 border border-gray-200">
              {renderSubcriteria(evaluationData.scores.scientific, "Scientific Foundation")}
            </div>
            <div className="bg-white rounded-lg p-6 border border-gray-200">
              {renderSubcriteria(evaluationData.scores.innovation, "Innovation & Currency")}
            </div>
            <div className="bg-white rounded-lg p-6 border border-gray-200">
              {renderSubcriteria(evaluationData.scores.alignment, "Alignment with Maker-Researcher Profile")}
            </div>
            
            {/* Total Score */}
            <div className="bg-white rounded-lg p-6 border border-gray-200">
              <div className="flex justify-between items-center text-xl font-semibold">
                <span>Total Score</span>
                <span>{evaluationData.scores.total}/500</span>
              </div>
              {renderScoreBar(evaluationData.scores.total, 500)}
            </div>
          </div>

          {/* Summary */}
          <div className="bg-white rounded-lg p-6 border border-gray-200">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">Summary</h3>
            <p className="text-gray-900">{evaluationData.summary}</p>
          </div>

          {/* Executable Strategies */}
          <div className="bg-white rounded-lg p-6 border border-gray-200">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">Executable Strategies</h3>
            <div className="space-y-6">
              {evaluationData.executableStrategies.map((strategy, index) => (
                <div key={index} className="space-y-3">
                  <h4 className="text-lg font-medium text-gray-900">{index + 1}. {strategy.name}</h4>
                  <div>
                    <h5 className="text-sm font-medium text-gray-700">Implementation steps:</h5>
                    <ul className="list-disc list-inside mt-2 space-y-1">
                      {strategy.implementation.map((step, stepIndex) => (
                        <li key={stepIndex} className="text-gray-900">{step}</li>
                      ))}
                    </ul>
                  </div>
                  <div>
                    <h5 className="text-sm font-medium text-gray-700">Required resources:</h5>
                    <p className="mt-1 text-gray-900">{strategy.resources}</p>
                  </div>
                  <div>
                    <h5 className="text-sm font-medium text-gray-700">Expected results:</h5>
                    <p className="mt-1 text-gray-900">{strategy.results}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Code Examples */}
          {evaluationData.codeExamples && (
            <div className="bg-white rounded-lg p-6 border border-gray-200">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Code Examples</h3>
              <pre className="bg-gray-50 p-4 rounded-lg overflow-x-auto text-sm">
                <code className="text-gray-900">{evaluationData.codeExamples}</code>
              </pre>
            </div>
          )}

          {/* Implementation Notes */}
          <div className="bg-white rounded-lg p-6 border border-gray-200">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">Implementation Notes</h3>
            <div className="space-y-4">
              <div>
                <h4 className="text-base font-medium text-gray-900">Resource Requirements</h4>
                <ul className="mt-2 list-disc list-inside space-y-1">
                  {evaluationData.implementationNotes.requirements.map((req, index) => (
                    <li key={index} className="text-gray-900">{req}</li>
                  ))}
                </ul>
              </div>
              <div>
                <h4 className="text-base font-medium text-gray-900">Potential Challenges</h4>
                <ul className="mt-2 list-disc list-inside space-y-1">
                  {evaluationData.implementationNotes.challenges.map((challenge, index) => (
                    <li key={index} className="text-gray-900">{challenge}</li>
                  ))}
                </ul>
              </div>
              <div>
                <h4 className="text-base font-medium text-gray-900">Optimization Possibilities</h4>
                <ul className="mt-2 list-disc list-inside space-y-1">
                  {evaluationData.implementationNotes.optimization.map((opt, index) => (
                    <li key={index} className="text-gray-900">{opt}</li>
                  ))}
                </ul>
              </div>
            </div>
          </div>

          {/* Conclusion */}
          <div className="bg-white rounded-lg p-6 border border-gray-200">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">Conclusion</h3>
            {evaluationData.conclusion ? (
              <div className="prose max-w-none">
                <p className="text-gray-900">{evaluationData.conclusion}</p>
              </div>
            ) : (
              /* Fallback to old selection status format */
              <>
                <div className="flex items-center space-x-3">
                  <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
                    evaluationData.selected ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  }`}>
                    {evaluationData.selected ? '✓' : '×'}
                  </div>
                  <span className="text-lg font-medium text-gray-900">
                    {evaluationData.selected ? 'Selected (≥ 360 points)' : 'Not selected (< 360 points)'}
                  </span>
                </div>
                <p className="mt-3 text-gray-900">{evaluationData.selectionMotivation}</p>
              </>
            )}
          </div>
        </>
      )}
    </div>
  );
}

function parseEvaluationMarkdown(content: string): EvaluationData {
  try {
    // Find the JSON block
    const jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/);
    
    // Try to extract JSON data from the markdown content
    interface JsonMetadata {
      title?: string;
      authors?: string;
      year?: number;
      doi?: string;
    }

    interface JsonScores {
      total_score?: number;
      [key: string]: unknown;
    }

    interface JsonData {
      metadata?: JsonMetadata;
      scores?: JsonScores;
      conclusion?: string;
      selection_status?: string;
    }
    
    let jsonData: JsonData = {};
    
    if (jsonMatch) {
      try {
        jsonData = JSON.parse(jsonMatch[1]);
      } catch (e) {
        console.error('Error parsing JSON from markdown:', e);
      }
    }

    // Extract scores and explanations
    const scoreCategories = ['Relevance for AI Use in Software Development', 'Practical Applicability', 'Scientific Foundation', 'Innovation & Currency', 'Alignment with Maker-Researcher Profile'];
    interface ScoreCategory {
      total: number;
      subcriteria: Record<string, SubScore>;
    }
    
    const scores: Record<string, ScoreCategory> = {};

    for (const category of scoreCategories) {
      const categoryMatch = content.match(new RegExp(`${category}.*?\\[(\\d+)/100\\]([\\s\\S]*?)(?=\\*\\*Total\\*\\*:)`, 's'));
      if (categoryMatch) {
        const explanationsText = categoryMatch[2];
        const explanations: { [key: string]: string } = {};
        
        // Extract individual explanations
        const explanationMatches = explanationsText.matchAll(/- ([^:]+): \[(.*?)\]/g);
        for (const match of explanationMatches) {
          explanations[match[1].trim()] = match[2].trim();
        }

        // Map to the correct category key
        const categoryKey = category.toLowerCase().replace(/ & /g, '_').replace(/ /g, '_');
        const scoreData = jsonData?.scores?.[categoryKey.replace('relevance_for_ai_use_in_software_development', 'relevance')] || {};
        
        scores[categoryKey] = {
          total: parseInt(categoryMatch[1]) || 0,
          subcriteria: Object.entries(scoreData)
            .filter(([key]) => key !== 'total')
            .reduce<Record<string, SubScore>>((acc, [key, value]) => {
              acc[key] = {
                score: typeof value === 'number' ? value : 0,
                maxScore: key.includes('date') ? 30 : key.includes('aspects') ? 40 : 30,
                explanation: explanations[key.replace(/_/g, ' ')] || ''
              };
              return acc;
            }, {})
        };
      }
    }

    // Extract summary
    const summaryMatch = content.match(/### Summary\n([\s\S]*?)(?=\n###)/);
    const summary = summaryMatch ? summaryMatch[1].trim() : '';

    // Extract executable strategies
    const strategiesMatch = content.match(/### Executable Strategies\n([\s\S]*?)(?=\n###|$)/);
    const strategies = [];
    if (strategiesMatch) {
      const strategyBlocks = strategiesMatch[1].split(/\d+\.\s+\[/).filter(Boolean);
      for (const block of strategyBlocks) {
        const nameMatch = block.match(/^(.*?)\]/);
        if (nameMatch) {
          const name = nameMatch[1];
          const implementationMatch = block.match(/Implementation steps:\s*\[([\s\S]*?)\]/);
          const resourcesMatch = block.match(/Required resources:\s*\[([\s\S]*?)\]/);
          const resultsMatch = block.match(/Expected results:\s*\[([\s\S]*?)\]/);

          strategies.push({
            name,
            implementation: implementationMatch ? 
              implementationMatch[1].split(',').map(step => step.trim()) : [],
            resources: resourcesMatch ? resourcesMatch[1].trim() : '',
            results: resultsMatch ? resultsMatch[1].trim() : ''
          });
        }
      }
    }

    // Extract code examples
    const codeMatch = content.match(/### Code Examples\n```.*?\n([\s\S]*?)```/);
    const codeExamples = codeMatch ? codeMatch[1].trim() : '';

    // Extract implementation notes
    const notesMatch = content.match(/### Implementation Notes\n([\s\S]*?)(?=\n###|$)/);
    const implementationNotes: {
      requirements: string[];
      challenges: string[];
      optimization: string[];
    } = {
      requirements: [],
      challenges: [],
      optimization: []
    };
    
    if (notesMatch) {
      const notesText = notesMatch[1];
      // Use [\s\S] instead of the 's' flag for multi-line matching
      const requirementsMatch = notesText.match(/Resource requirements:\s*\[([\s\S]*?)\]/);
      const challengesMatch = notesText.match(/Potential challenges:\s*\[([\s\S]*?)\]/);
      const optimizationMatch = notesText.match(/Optimization possibilities:\s*\[([\s\S]*?)\]/);

      if (requirementsMatch) implementationNotes.requirements = requirementsMatch[1].split(',').map(r => r.trim());
      if (challengesMatch) implementationNotes.challenges = challengesMatch[1].split(',').map(c => c.trim());
      if (optimizationMatch) implementationNotes.optimization = optimizationMatch[1].split(',').map(o => o.trim());
    }

    // Extract conclusion or selection status
    let conclusion = '';
    let selected = false;
    let selectionMotivation = '';
    
    // First try to get conclusion from JSON
    if (jsonData && (jsonData.conclusion || jsonData.selection_status)) {
      conclusion = (jsonData.conclusion as string) || (jsonData.selection_status as string);
    }
    
    // Fallback to markdown parsing for backward compatibility
    if (!conclusion) {
      const selectionMatch = content.match(/### Selection Status\n(\[x\]|\[ \]) Selected[\s\S]*?\n\nMotivation: \[([\s\S]*?)\]/);
      selected = selectionMatch ? selectionMatch[1] === '[x]' : false;
      selectionMotivation = selectionMatch ? selectionMatch[2].trim() : '';
    }

    return {
      metadata: {
        title: jsonData.metadata?.title || '',
        authors: (jsonData.metadata?.authors || '').split(', '),
        year: jsonData.metadata?.year || 0,
        doi: jsonData.metadata?.doi || ''
      },
      scores: {
        relevance: scores.relevance_for_ai_use_in_software_development || { total: 0, subcriteria: {} },
        applicability: scores.practical_applicability || { total: 0, subcriteria: {} },
        scientific: scores.scientific_foundation || { total: 0, subcriteria: {} },
        innovation: scores.innovation_currency || { total: 0, subcriteria: {} },
        alignment: scores.alignment_with_maker_researcher_profile || { total: 0, subcriteria: {} },
        total: jsonData.scores?.total_score || 0
      },
      summary,
      executableStrategies: strategies,
      codeExamples,
      implementationNotes,
      conclusion,
      selected,
      selectionMotivation
    };
  } catch (error) {
    console.error('Error parsing markdown:', error);
    throw new Error('Failed to parse evaluation markdown');
  }
}
