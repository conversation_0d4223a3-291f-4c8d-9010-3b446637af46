'use client';

import React, { useState, useRef, useEffect } from 'react';
import { useTheme } from '@/context/ThemeContext';
import { useModelContext, ModelType } from '@/context/ModelContext';
import { Cog6ToothIcon, SunIcon, MoonIcon } from '@heroicons/react/24/outline';

// Model display names and descriptions
const MODEL_INFO: Record<ModelType, { name: string; description: string }> = {
  'o3': { 
    name: 'OpenAI o3', 
    description: 'OpenAI\'s o3 model' 
  },
  'Gemini': { 
    name: 'Gemini Pro 2.5', 
    description: 'Google\'s Gemini Pro 2.5 model' 
  },
  'Sonnet': { 
    name: 'Sonnet 3.7 Thinking', 
    description: 'Anthropic\'s Sonnet 3.7 Thinking model' 
  },
  'legacy': { 
    name: 'Legacy', 
    description: 'Original evaluations' 
  }
};

export default function SettingsMenu() {
  const [isOpen, setIsOpen] = useState(false);
  const { theme, toggleTheme } = useTheme();
  const { selectedModel, setSelectedModel, availableModels, isModelSelectorReady } = useModelContext();
  const menuRef = useRef<HTMLDivElement>(null);
  
  // Close the menu when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    }
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [menuRef]);

  return (
    <div className="relative" ref={menuRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="p-1 rounded-full text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-zinc-700 hover:text-gray-700 dark:hover:text-gray-200 focus:outline-none"
        aria-expanded={isOpen}
        aria-haspopup="true"
        aria-controls="settings-menu"
      >
        <Cog6ToothIcon className="h-6 w-6" />
      </button>
      
      {isOpen && (
        <div className="absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white dark:bg-zinc-800 ring-1 ring-black ring-opacity-5 z-50">
          <div className="py-1" role="menu" aria-orientation="vertical" aria-labelledby="options-menu">
            {/* Theme Toggle */}
            <div className="px-4 py-2 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-700 dark:text-gray-300">Theme</span>
                <button
                  onClick={toggleTheme}
                  className="p-1 rounded-md focus:outline-none"
                  aria-label={`Switch to ${theme === 'dark' ? 'light' : 'dark'} mode`}
                >
                  {theme === 'dark' ? (
                    <SunIcon className="h-5 w-5 text-yellow-400" />
                  ) : (
                    <MoonIcon className="h-5 w-5 text-gray-500" />
                  )}
                </button>
              </div>
            </div>
            
            {/* Model Selector */}
            {isModelSelectorReady && availableModels.length > 1 && (
              <div className="px-4 py-2">
                <label htmlFor="model-selector" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  AI Model
                </label>
                <select
                  id="model-selector"
                  value={selectedModel}
                  onChange={(e) => setSelectedModel(e.target.value as ModelType)}
                  className="block w-full pl-3 pr-10 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-zinc-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-1 focus:ring-blue-500"
                >
                  {availableModels.map((model) => (
                    <option key={model} value={model}>
                      {MODEL_INFO[model]?.name || model}
                    </option>
                  ))}
                </select>
                {/* Model description removed as it's redundant */}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
