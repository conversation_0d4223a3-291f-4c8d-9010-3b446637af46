import { NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';
import { NextRequest } from 'next/server';

// Define available models
const AVAILABLE_MODELS = ['o3', 'Gemini', 'Sonnet'];
const DEFAULT_MODEL = 'o3';

export async function GET(request: NextRequest) {
  try {
    // Get the model from query parameters or use default
    const searchParams = request.nextUrl.searchParams;
    const requestedModel = searchParams.get('model') || DEFAULT_MODEL;
    
    // Validate the model parameter
    const model = AVAILABLE_MODELS.includes(requestedModel) ? requestedModel : DEFAULT_MODEL;
    
    // Construct the absolute path to the evaluations directory
    const evaluationsDir = path.join(process.cwd(), 'public', 'papers', 'evaluations');
    
    // Check if we're using the new directory structure
    const dirContents = await fs.readdir(evaluationsDir);
    
    // Check if we have paper folders or direct JSON files
    const hasSubdirectories = await checkForSubdirectories(evaluationsDir, dirContents);
    
    if (hasSubdirectories) {
      // New structure: Paper folders with model-specific files
      
      // Get evaluation files for the specified model from each paper folder
      const evaluationFiles = [];
      
      for (const folder of dirContents) {
        const folderPath = path.join(evaluationsDir, folder);
        try {
          const stats = await fs.stat(folderPath);
          
          if (stats.isDirectory()) {
            const folderFiles = await fs.readdir(folderPath);
            // Look for files matching the requested model
            const modelFile = folderFiles.find((file: string) => 
              file.endsWith(`_${model}.json`) && file.startsWith('Evaluation_')
            );
            
            if (modelFile) {
              // Return the path relative to the evaluations directory
              evaluationFiles.push(`${folder}/${modelFile}`);
            }
          }
        } catch (err) {
          // Skip folders that can't be read
          console.warn(`Could not read folder ${folder}:`, err);
        }
      }
      
      return NextResponse.json({
        model,
        files: evaluationFiles,
        availableModels: AVAILABLE_MODELS
      });
    } else {
      // Legacy structure: Direct JSON files (for backward compatibility)
      const jsonFiles = dirContents.filter(file => 
        file.startsWith('Evaluation_') && file.endsWith('.json')
      );
      
      return NextResponse.json({
        model: 'legacy',
        files: jsonFiles,
        availableModels: ['legacy']
      });
    }
  } catch (error: unknown) {
    console.error('Error listing evaluation files:', error);
    // Provide a more specific error message if possible
    let errorMessage = 'Failed to list evaluation files.';
    
    // Type guard for NodeJS.ErrnoException
    if (error && typeof error === 'object' && 'code' in error && error.code === 'ENOENT') {
      errorMessage = 'Evaluations directory not found.';
    }
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}

// Helper function to check if the directory contains subdirectories
async function checkForSubdirectories(dirPath: string, contents: string[]): Promise<boolean> {
  for (const item of contents) {
    const itemPath = path.join(dirPath, item);
    try {
      const stats = await fs.stat(itemPath);
      if (stats.isDirectory()) {
        return true;
      }
    } catch {
      // Skip items that can't be stat'd
    }
  }
  return false;
}
