import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

// Define available models
const AVAILABLE_MODELS = ['o3', 'Gemini', 'Sonnet'] as const;
type ModelType = typeof AVAILABLE_MODELS[number];

const MODEL_FILE_SUFFIX: Record<ModelType, string> = {
  'o3': 'o3',
  'Gemini': 'Gemini',
  'Sonnet': 'Sonnet'
};

// Define the score categories and their subscores
const SCORE_CATEGORIES = [
  {
    id: 'ai_techniques',
    label: 'AI Techniques',
    subscores: ['methods', 'ai_integration', 'collaboration']
  },
  {
    id: 'process_enhancement',
    label: 'Process Enhancement',
    subscores: ['task_improvements', 'lifecycle_integration', 'challenge_solutions']
  },
  {
    id: 'implementation',
    label: 'Implementation',
    subscores: ['steps', 'examples', 'requirements']
  },
  {
    id: 'measurable_impact',
    label: 'Measurable Impact',
    subscores: ['efficiency', 'metrics', 'benefits']
  },
  {
    id: 'developer_experience',
    label: 'Developer Experience',
    subscores: ['adoption', 'workflow', 'practicality']
  }
];

interface SubScore {
  [key: string]: number;
}

interface CategoryScore {
  total: number;
  subscores: SubScore;
}

interface ModelCategoryScores {
  [category: string]: CategoryScore;
}

interface DetailedModelScore {
  total: number;
  categories: ModelCategoryScores;
}

interface PaperScore {
  id: string;
  title: string;
  modelScores: {
    [key: string]: number;
  };
  detailedModelScores: {
    [key: string]: DetailedModelScore;
  };
  averageScore: number;
  averageDetailedScores: {
    categories: {
      [category: string]: {
        total: number;
        subscores: SubScore;
      };
    };
  };
}

export async function GET() {
  try {
    // Path to the evaluations directory
    const evaluationsDir = path.join(process.cwd(), 'public', 'papers', 'evaluations');
    
    // Check if we're using the new directory structure
    const dirContents = fs.readdirSync(evaluationsDir);
    const hasSubdirectories = dirContents.some(item => {
      const itemPath = path.join(evaluationsDir, item);
      return fs.existsSync(itemPath) && fs.statSync(itemPath).isDirectory();
    });
    
    if (!hasSubdirectories) {
      return NextResponse.json(
        { error: 'Legacy directory structure not supported for score averaging' },
        { status: 400 }
      );
    }
    
    // Get all paper folders
    const paperFolders = dirContents.filter(item => {
      const itemPath = path.join(evaluationsDir, item);
      return fs.existsSync(itemPath) && fs.statSync(itemPath).isDirectory();
    });
    
    // Process each paper folder to extract scores for all models
    const paperScores: PaperScore[] = [];
    
    for (const folder of paperFolders) {
      const folderPath = path.join(evaluationsDir, folder);
      const folderFiles = fs.readdirSync(folderPath);
      
      // Initialize paper data
      const paperData: PaperScore = {
        id: folder.toLowerCase().replace(/\s+/g, '_'),
        title: '', // Will be populated from the first valid file
        modelScores: {},
        detailedModelScores: {},
        averageScore: 0,
        averageDetailedScores: {
          categories: {}
        }
      };
      
      let validScoresCount = 0;
      let totalScore = 0;
      
      // Initialize average detailed scores structure
      SCORE_CATEGORIES.forEach(category => {
        paperData.averageDetailedScores.categories[category.id] = {
          total: 0,
          subscores: {}
        };
        
        category.subscores.forEach(subscore => {
          paperData.averageDetailedScores.categories[category.id].subscores[subscore] = 0;
        });
      });
      
      // Track counts for averaging
      const categoryScoreCounts: Record<string, number> = {};
      const subscoreScoreCounts: Record<string, Record<string, number>> = {};
      
      SCORE_CATEGORIES.forEach(category => {
        categoryScoreCounts[category.id] = 0;
        subscoreScoreCounts[category.id] = {};
        
        category.subscores.forEach(subscore => {
          subscoreScoreCounts[category.id][subscore] = 0;
        });
      });
      
      // Process each model's evaluation file
      for (const model of AVAILABLE_MODELS) {
        const modelSuffix = MODEL_FILE_SUFFIX[model];
        const modelFile = folderFiles.find(file => 
          file.endsWith(`_${modelSuffix}.json`) && file.startsWith('Evaluation_')
        );
        
        if (modelFile) {
          try {
            const filePath = path.join(folderPath, modelFile);
            const fileContent = fs.readFileSync(filePath, 'utf8');
            
            // Skip empty files
            if (!fileContent.trim()) {
              continue;
            }
            
            // Parse JSON
            const jsonData = JSON.parse(fileContent);
            
            // Set paper title if not already set
            if (!paperData.title && jsonData.metadata && jsonData.metadata.title) {
              paperData.title = jsonData.metadata.title;
            }
            
            // Initialize detailed model score
            paperData.detailedModelScores[model] = {
              total: 0,
              categories: {}
            };
            
            // Extract score
            let score = 0;
            
            // First check if there's a total_weighted_score at the top level of scores
            if (jsonData.scores && jsonData.scores.total_weighted_score !== undefined) {
              score = jsonData.scores.total_weighted_score;
              paperData.detailedModelScores[model].total = score;
            }
            
            // Extract detailed category scores and subscores
            if (jsonData.scores) {
              const scores = jsonData.scores;
              
              // Process each category
              SCORE_CATEGORIES.forEach(category => {
                const categoryId = category.id;
                const categoryData = scores[categoryId];
                
                if (categoryData && typeof categoryData === 'object') {
                  // Initialize category in detailed model scores
                  paperData.detailedModelScores[model].categories[categoryId] = {
                    total: 0,
                    subscores: {}
                  };
                  
                  // Extract category total
                  if (categoryData.total !== undefined) {
                    paperData.detailedModelScores[model].categories[categoryId].total = categoryData.total;
                    
                    // Add to average category total
                    paperData.averageDetailedScores.categories[categoryId].total += categoryData.total;
                    categoryScoreCounts[categoryId]++;
                  }
                  
                  // Extract subscores
                  category.subscores.forEach(subscoreId => {
                    if (categoryData[subscoreId] !== undefined) {
                      // Add to model's detailed scores
                      paperData.detailedModelScores[model].categories[categoryId].subscores[subscoreId] = categoryData[subscoreId];
                      
                      // Add to average subscores
                      paperData.averageDetailedScores.categories[categoryId].subscores[subscoreId] += categoryData[subscoreId];
                      subscoreScoreCounts[categoryId][subscoreId]++;
                    }
                  });
                }
              });
              
              // If no total_weighted_score was found, calculate from category totals
              if (score === 0) {
                const categoryTotals = [];
                
                // Look for total scores in each category
                for (const category in scores) {
                  if (typeof scores[category] === 'object' && scores[category].total !== undefined) {
                    categoryTotals.push(scores[category].total);
                  }
                }
                
                // Calculate average if we found any category totals
                if (categoryTotals.length > 0) {
                  score = Math.round(categoryTotals.reduce((sum, score) => sum + score, 0) / categoryTotals.length);
                  paperData.detailedModelScores[model].total = score;
                }
              }
            }
            
            // Add score to paper data
            paperData.modelScores[model] = score;
            totalScore += score;
            validScoresCount++;
          } catch (error) {
            console.error(`Error processing file ${modelFile} for paper ${folder}:`, error);
          }
        }
      }
      
      // Calculate average scores
      if (validScoresCount > 0) {
        // Overall average score
        paperData.averageScore = Math.round(totalScore / validScoresCount);
        
        // Average category scores
        SCORE_CATEGORIES.forEach(category => {
          const categoryId = category.id;
          if (categoryScoreCounts[categoryId] > 0) {
            paperData.averageDetailedScores.categories[categoryId].total = 
              Math.round(paperData.averageDetailedScores.categories[categoryId].total / categoryScoreCounts[categoryId]);
            
            // Average subscores
            category.subscores.forEach(subscoreId => {
              if (subscoreScoreCounts[categoryId][subscoreId] > 0) {
                paperData.averageDetailedScores.categories[categoryId].subscores[subscoreId] = 
                  Math.round(paperData.averageDetailedScores.categories[categoryId].subscores[subscoreId] / 
                  subscoreScoreCounts[categoryId][subscoreId]);
              }
            });
          }
        });
        
        paperScores.push(paperData);
      }
    }
    
    return NextResponse.json(paperScores);
  } catch (error) {
    console.error('Error in API route:', error);
    return NextResponse.json(
      { error: 'Failed to load paper scores' },
      { status: 500 }
    );
  }
}
