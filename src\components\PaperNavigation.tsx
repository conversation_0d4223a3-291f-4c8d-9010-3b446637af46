'use client';

import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import Link from 'next/link';
import InlineGlossaryText from '@/components/InlineGlossaryText';
import { titleToId } from '@/utils/titleToId';
import {
  DocumentTextIcon,
  ClipboardDocumentListIcon,
  ScaleIcon,
  CubeTransparentIcon,
  AcademicCapIcon,
  KeyIcon,
  ExclamationTriangleIcon,
  ChatBubbleLeftRightIcon,
  CheckCircleIcon,
  ArrowUpIcon
} from '@heroicons/react/24/outline';

interface PaperNavigationProps {
  paperSlug: string;
  paper: any;
}

interface NavLinkItem {
  name: string;
  href: string;
  icon: React.ElementType;
  isExternal?: boolean;
  customHandler?: (e: React.MouseEvent) => void;
  active?: boolean;
  visibility?: number; // 0-1 representing how visible the section is
}



const PaperNavigation: React.FC<PaperNavigationProps> = ({ paperSlug, paper }) => {
  const displayTitle = "Table Of Contents";

  // State to track section visibility and active section
  const [activeSection, setActiveSection] = useState<string>('');
  const [sectionVisibility, setSectionVisibility] = useState<Map<string, number>>(new Map());
  const observerRef = useRef<IntersectionObserver | null>(null);
  const isManualNavigation = useRef<boolean>(false);

  // Memoize tocSections to prevent re-creating it on every render
  const tocSections = useMemo(() => [
    { name: "Executive Summary", fullTitle: "Executive Summary for Audio Developers", icon: ClipboardDocumentListIcon },
    { name: "Scores", fullTitle: "Quantitative Scores", icon: ScaleIcon },
    { name: "Pillar Analysis", fullTitle: "Implementation Readiness", icon: CubeTransparentIcon },
    { name: "Explanations", fullTitle: "Multi-Level Explanations", icon: AcademicCapIcon },
    { name: "Key Learnings", fullTitle: "Key Learnings for Audio Developers", icon: KeyIcon },
    { name: "Critical Assessment", fullTitle: "Critical Assessment and Limitations", icon: ExclamationTriangleIcon },
    { name: "Methodology", fullTitle: "Methodological Deep Dive & Adaptation", icon: CubeTransparentIcon },
    { name: "Research Impact", fullTitle: "Impact on My Research & Development", icon: ChatBubbleLeftRightIcon },
    { name: "Final Verdict", fullTitle: "Final Verdict for Audio Developers", icon: CheckCircleIcon },
  ], []);

  // Calculate which section should be active
  const updateActiveSection = useCallback(() => {
    if (isManualNavigation.current) {
      // If a programmatic scroll is in progress, activeSection is already set by the click handler.
      // Don't let observer/scroll events override mid-programmatic-scroll.
      return;
    }

    // Priority 1: If at the very top of the page, 'Overview' must be active.
    if (window.pageYOffset < 10) { // Stricter threshold for being "at the top"
      if (activeSection !== 'page-top') {
        setActiveSection('page-top');
      }
      return; // 'page-top' has priority, no further checks needed
    }

    // Priority 2: Determine active section by visibility from IntersectionObserver
    let maxVisibility = 0;
    let mostVisibleSectionId = '';
    sectionVisibility.forEach((visibility, sectionId) => {
      if (visibility > maxVisibility) {
        maxVisibility = visibility;
        mostVisibleSectionId = sectionId;
      }
    });

    // If a section is sufficiently visible and it's not already active, set it.
    if (mostVisibleSectionId && maxVisibility > 0.2) {
      if (activeSection !== mostVisibleSectionId) {
        setActiveSection(mostVisibleSectionId);
      }
    } else if (activeSection && activeSection !== 'page-top' && maxVisibility <= 0.2) {
      // If something was active (but not 'page-top'), and now nothing is prominent,
      // it means we scrolled away from it. For now, keep it active to prevent flickering.
      // The 'page-top' case is handled by the pageYOffset check above.
      // If we are not at page top, and no section is >0.2 visible, the last active section remains.
    }
  }, [activeSection, sectionVisibility]); // Dependencies for useCallback

  // Recalculate active section based on new visibility or if activeSection itself changed
  // This ensures that if activeSection was set manually (e.g. by click), we still re-evaluate if needed
  // once isManualNavigation is false.
  useEffect(() => {
    updateActiveSection();
  }, [activeSection, sectionVisibility, updateActiveSection]); // updateActiveSection is memoized by useCallback

  // Set up Intersection Observer
  useEffect(() => {
    const observerCallback = (entries: IntersectionObserverEntry[]) => {
      const newVisibility = new Map(sectionVisibility);
      let changed = false;
      entries.forEach(entry => {
        const sectionId = entry.target.id;
        // Check if visibility actually changed to avoid unnecessary state updates
        const currentVis = newVisibility.get(sectionId) || 0;
        const newVisRatio = entry.intersectionRatio;
        if (Math.abs(currentVis - newVisRatio) > 0.01) { // Threshold for change
          newVisibility.set(sectionId, newVisRatio);
          changed = true;
        }
      });

      if (changed) {
        setSectionVisibility(newVisibility); // This will trigger the useEffect above to call updateActiveSection
      }
    };

    observerRef.current = new IntersectionObserver(observerCallback, {
      // Use negative top margin to account for navbar
      rootMargin: `-${80}px 0px -20% 0px`,
      // Multiple thresholds for smooth transitions
      threshold: [0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]
    });

    // Observe all section elements
    tocSections.forEach(section => {
      const sectionId = titleToId(section.fullTitle);
      const element = document.getElementById(sectionId);
      if (element && observerRef.current) {
        observerRef.current.observe(element);
      }
    });

    // Cleanup observer on unmount
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [updateActiveSection]);

  const handleScrollToTop = (e: React.MouseEvent) => {
    e.preventDefault();
    isManualNavigation.current = true;
    setActiveSection('page-top'); // Set active state immediately
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });

    setTimeout(() => {
      isManualNavigation.current = false;
      updateActiveSection(); // Re-evaluate active section after scroll completes
    }, 1000); // Adjust timeout to match smooth scroll duration
  };

  const handleTocNavigation = (e: React.MouseEvent, sectionId: string) => {
    e.preventDefault();
    const element = document.getElementById(sectionId);
    if (element) {
      isManualNavigation.current = true;
      setActiveSection(sectionId); // Set target section active immediately

      const navbarHeight = 80;
      const elementPosition = element.getBoundingClientRect().top + window.pageYOffset;
      const offsetPosition = elementPosition - navbarHeight;

      window.scrollTo({ top: offsetPosition, behavior: 'smooth' });

      setTimeout(() => {
        isManualNavigation.current = false;
        updateActiveSection(); // Re-evaluate active section after scroll completes
      }, 1000);
    }
  };

  const navLinks: NavLinkItem[] = [
    {
      name: 'Overview',
      href: '#page-top', // Semantic href, actual scroll handled by customHandler
      icon: ArrowUpIcon,
      customHandler: handleScrollToTop,
      active: activeSection === 'page-top',
      // visibility is not applicable here or can be considered 0 for styling if not active
    },
    ...tocSections.map(section => {
      const sectionId = titleToId(section.fullTitle);
      const visibility = sectionVisibility.get(sectionId) || 0;
      return {
        name: section.name,
        href: `#${sectionId}`,
        icon: section.icon,
        customHandler: (e: React.MouseEvent) => handleTocNavigation(e, sectionId),
        active: activeSection === sectionId,
        visibility: visibility,
      };
    })
  ];

  return (
    <div className="fixed bottom-8 right-8 z-50">
      {/* Outer div for gradient border */}
      <div className="bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg p-0.5 shadow-lg">
        {/* Inner content div */}
        <div className="bg-white dark:bg-zinc-900 rounded-md overflow-hidden max-h-[70vh] flex flex-col shadow-sm dark:shadow-zinc-800/30">
          <div className="px-4 py-3 bg-gray-50 dark:bg-zinc-900 border-b border-gray-200 dark:border-gray-700 sticky top-0 z-10">
            <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate max-w-[200px]">
              <InlineGlossaryText
                text={displayTitle}
                className="text-sm font-medium text-gray-900 dark:text-gray-100"
              />
            </h3>
          </div>
          <nav className="flex flex-col overflow-y-auto flex-grow">
            {navLinks.map((link) => {
              // Calculate dynamic styles based on visibility and active state
              const visibility = link.visibility || 0;
              const isActive = link.active;

              // Create smooth transition styles with dark mode support
              const getTransitionStyles = () => {
                if (link.isExternal) {
                  // External links (like "View Paper") use standard styling
                  return {
                    backgroundColor: 'transparent',
                    color: '',
                    opacity: 1,
                    borderLeft: 'none'
                  };
                }

                if (isActive) {
                  // Fully active section - different styles for light/dark mode
                  return {
                    backgroundColor: 'light', // Will be handled by CSS classes
                    color: 'light', // Will be handled by CSS classes
                    opacity: 1,
                    borderLeft: '3px solid rgb(59 130 246)' // blue-500 border
                  };
                } else if (visibility > 0.1) {
                  // Partially visible section - gradual feedback
                  const intensity = Math.min(visibility * 2, 1); // Scale up visibility for better effect
                  return {
                    backgroundColor: `rgba(59, 130, 246, ${intensity * 0.1})`, // Universal blue with alpha
                    color: `rgba(59, 130, 246, ${0.6 + intensity * 0.4})`, // Gradually stronger blue text
                    opacity: 0.8 + intensity * 0.2,
                    borderLeft: `2px solid rgba(59, 130, 246, ${intensity * 0.6})` // Faded blue border
                  };
                } else {
                  // Not visible section
                  return {
                    backgroundColor: 'transparent',
                    color: '',
                    opacity: 1,
                    borderLeft: 'none'
                  };
                }
              };

              const dynamicStyles = getTransitionStyles();

              return (
                <Link
                  key={link.name}
                  href={link.href}
                  onClick={link.customHandler}
                  target={link.isExternal ? "_blank" : "_self"}
                  rel={link.isExternal ? "noopener noreferrer" : undefined}
                  className={`flex items-center px-4 py-2 text-sm transition-all duration-300 ease-out relative ${
                    link.isExternal
                      ? 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-zinc-700/50'
                      : isActive
                        ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-400'
                        : visibility > 0.1
                          ? '' // Dynamic styles will handle this
                          : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-zinc-700/50'
                  }`}
                  style={{
                    backgroundColor: isActive ? undefined : dynamicStyles.backgroundColor,
                    color: isActive ? undefined : (dynamicStyles.color || undefined),
                    opacity: dynamicStyles.opacity,
                    borderLeft: dynamicStyles.borderLeft,
                  }}
                >
                  <link.icon
                    className={`mr-3 h-5 w-5 flex-shrink-0 transition-all duration-300 ease-out ${
                      isActive
                        ? 'text-blue-600 dark:text-blue-400'
                        : visibility > 0.1
                          ? 'text-blue-500'
                          : 'text-gray-500 dark:text-gray-400'
                    }`}
                    aria-hidden="true"
                    style={{
                      opacity: link.isExternal ? 1 : (isActive ? 1 : 0.6 + (visibility * 0.4))
                    }}
                  />
                  <InlineGlossaryText
                    text={link.name}
                    className="text-sm transition-all duration-300 ease-out"
                  />

                  {/* Progress indicator for partially visible sections */}
                  {!link.isExternal && visibility > 0.1 && !isActive && (
                    <div
                      className="absolute right-2 w-1 h-4 bg-blue-400 rounded-full transition-all duration-300 ease-out"
                      style={{
                        opacity: visibility * 0.8,
                        transform: `scaleY(${visibility})`
                      }}
                    />
                  )}
                </Link>
              );
            })}
          </nav>
        </div>
      </div>
    </div>
  );
};

export default PaperNavigation;
