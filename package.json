{"name": "thesis-narrative-site", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.2", "@heroicons/react": "^2.2.0", "@types/react-syntax-highlighter": "^15.5.13", "framer-motion": "^12.12.1", "lucide-react": "^0.508.0", "marked": "^15.0.6", "next": "15.1.5", "react": "^19.0.0", "react-code-blocks": "^0.1.6", "react-dom": "^19.0.0", "react-markdown": "^9.0.3", "react-syntax-highlighter": "^15.6.1", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.5", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}