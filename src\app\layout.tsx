import type { Metadata } from 'next'
import { Inter, Merriweather, <PERSON><PERSON> } from 'next/font/google'

import { ThemeProvider } from '@/context/ThemeContext'
import { ModelProvider } from '@/context/ModelContext'
import { GlossaryProvider } from '@/context/GlossaryContext'
import Navbar from '@/components/Navbar';
import ContentOriginModal from '@/components/ContentOriginModal'
import DirectGlossaryScript from '@/components/DirectGlossaryScript'

import './globals.css'

const inter = Inter({ subsets: ['latin'], variable: '--font-inter' })
const merriweather = Merriweather({ 
  subsets: ['latin'], 
  weight: ['400', '700'], 
  variable: '--font-merriweather' 
})
const roboto = Roboto({ 
  subsets: ['latin'], 
  weight: ['400', '500', '700'], 
  variable: '--font-roboto' 
})

export const metadata: Metadata = {
  title: 'Supportive Narrative',
  description: 'Supportive Narrative for Music & Technology',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className={`${inter.variable} ${merriweather.variable} ${roboto.variable}`} suppressHydrationWarning>
      <body className="bg-gray-50 dark:bg-black text-gray-900 dark:text-gray-100">
        <ThemeProvider>
          <ModelProvider>
            <GlossaryProvider>
              <ContentOriginModal />
              <Navbar />
              <main className="min-h-screen font-merriweather pt-16">  
                {children}
              </main>
              <footer className="bg-white dark:bg-zinc-900 font-sans relative"> 
                {/* Added Gradient Bar */}
                <div className="absolute top-0 left-0 right-0 h-1 w-full bg-gradient-to-l from-blue-400 to-purple-400"></div>
                <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                  <p className="text-center text-gray-800 dark:text-gray-200">{new Date().getFullYear()} Nino van Orsouw - Music & Technology</p>
                </div>
              </footer>
              <DirectGlossaryScript />
            </GlossaryProvider>
          </ModelProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}
