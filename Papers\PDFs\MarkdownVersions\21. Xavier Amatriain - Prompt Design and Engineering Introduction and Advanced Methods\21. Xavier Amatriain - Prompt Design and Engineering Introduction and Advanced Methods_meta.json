{"table_of_contents": [], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 78], ["Line", 33], ["Text", 23]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 54], ["Line", 23], ["Text", 13]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 49], ["Line", 22], ["Text", 13]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 74], ["Line", 28], ["Text", 18]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 68], ["Line", 28], ["Text", 24]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 38], ["Line", 16], ["Text", 14]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 60], ["Line", 27], ["Text", 16]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 12], ["Text", 3], ["Line", 3]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 14], ["Text", 4], ["Line", 4]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 104], ["Line", 49], ["Text", 25]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 36], ["Line", 15], ["Text", 10]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 12], ["Text", 3], ["Line", 3]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 56], ["Line", 22], ["Text", 15]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 58], ["Line", 26], ["Text", 15]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 43], ["Line", 15], ["Text", 10]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 107], ["Line", 51], ["Text", 27]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 48], ["Line", 21], ["Text", 12]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 71], ["Line", 33], ["Text", 18]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 87], ["Line", 35], ["Text", 25]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 54], ["Line", 21], ["Text", 14]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 51], ["Line", 23], ["Text", 16]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 60], ["Line", 27], ["Text", 19]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 42], ["Line", 18], ["Text", 14]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 14], ["Text", 4], ["Line", 4]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 81], ["Line", 27], ["Text", 18]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 158], ["Line", 51], ["Text", 40]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}], "debug_data_path": "debug_data\\21. <PERSON> - Prompt Design and Engineering Introduction and Advanced Methods"}