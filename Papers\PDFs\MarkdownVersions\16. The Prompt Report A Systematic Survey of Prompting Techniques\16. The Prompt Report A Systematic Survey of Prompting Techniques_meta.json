{"table_of_contents": [], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 173], ["Line", 24], ["Text", 13]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 433], ["Line", 80], ["Text", 33]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 150], ["Line", 28], ["Text", 9]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 217], ["Line", 93], ["Text", 31]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 206], ["Line", 82], ["Text", 37]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 275], ["Line", 97], ["Text", 51]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 189], ["Line", 68], ["Text", 32]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 230], ["Line", 85], ["Text", 42]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 245], ["Line", 68], ["Text", 33]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 361], ["Line", 172], ["Text", 73]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 278], ["Line", 102], ["Text", 43]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 233], ["Line", 92], ["Text", 32]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 225], ["Line", 96], ["Text", 25]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 217], ["Line", 96], ["Text", 25]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 217], ["Line", 96], ["Text", 22]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 148], ["Line", 122], ["Text", 91]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 209], ["Line", 95], ["Text", 33]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 149], ["Line", 69], ["Text", 35]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 220], ["Line", 86], ["Text", 29]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 247], ["Line", 84], ["Text", 30]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 265], ["Line", 89], ["Text", 34]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 240], ["Line", 70], ["Text", 22]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 233], ["Line", 88], ["Text", 31]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 234], ["Line", 89], ["Text", 37]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 223], ["Line", 86], ["Text", 30]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 280], ["Line", 92], ["Text", 35]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 27], ["Line", 10], ["Text", 3]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 190], ["Line", 82], ["Text", 36]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 258], ["Line", 91], ["Text", 35]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 236], ["Line", 84], ["Text", 27]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 194], ["Line", 81], ["Text", 23]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Span", 202], ["Line", 90], ["Text", 33]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 32, "text_extraction_method": "pdftext", "block_counts": [["Span", 181], ["Line", 88], ["Text", 29]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 33, "text_extraction_method": "pdftext", "block_counts": [["Span", 248], ["Line", 104], ["Text", 32]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 34, "text_extraction_method": "pdftext", "block_counts": [["Span", 170], ["Line", 83], ["Text", 52]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 35, "text_extraction_method": "pdftext", "block_counts": [["Span", 57], ["Line", 44], ["Text", 42]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 36, "text_extraction_method": "pdftext", "block_counts": [["Span", 228], ["Line", 90], ["Text", 46]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 37, "text_extraction_method": "pdftext", "block_counts": [["Span", 312], ["Line", 96], ["Text", 35]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 38, "text_extraction_method": "pdftext", "block_counts": [["Span", 269], ["Line", 83], ["Text", 37]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 39, "text_extraction_method": "pdftext", "block_counts": [["Span", 334], ["Line", 86], ["Text", 32]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 40, "text_extraction_method": "pdftext", "block_counts": [["Span", 262], ["Line", 105], ["Text", 28]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 41, "text_extraction_method": "pdftext", "block_counts": [["Span", 279], ["Line", 96], ["Text", 12]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 42, "text_extraction_method": "pdftext", "block_counts": [["Span", 120], ["Line", 67], ["Text", 12]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 43, "text_extraction_method": "pdftext", "block_counts": [["Span", 250], ["Line", 111], ["Text", 31]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 44, "text_extraction_method": "pdftext", "block_counts": [["Span", 265], ["Line", 110], ["Text", 42]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 45, "text_extraction_method": "pdftext", "block_counts": [["Span", 281], ["Line", 110], ["Text", 44]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 46, "text_extraction_method": "pdftext", "block_counts": [["Span", 263], ["Line", 108], ["Text", 44]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 47, "text_extraction_method": "pdftext", "block_counts": [["Span", 257], ["Line", 109], ["Text", 40]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 48, "text_extraction_method": "pdftext", "block_counts": [["Span", 253], ["Line", 105], ["Text", 45]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 49, "text_extraction_method": "pdftext", "block_counts": [["Span", 257], ["Line", 108], ["Text", 49]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 50, "text_extraction_method": "pdftext", "block_counts": [["Span", 251], ["Line", 107], ["Text", 48]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 51, "text_extraction_method": "pdftext", "block_counts": [["Span", 247], ["Line", 108], ["Text", 41]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 52, "text_extraction_method": "pdftext", "block_counts": [["Span", 261], ["Line", 109], ["Text", 38]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 53, "text_extraction_method": "pdftext", "block_counts": [["Span", 272], ["Line", 109], ["Text", 40]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 54, "text_extraction_method": "pdftext", "block_counts": [["Span", 282], ["Line", 113], ["Text", 35]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 55, "text_extraction_method": "pdftext", "block_counts": [["Span", 253], ["Line", 110], ["Text", 46]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 56, "text_extraction_method": "pdftext", "block_counts": [["Span", 267], ["Line", 108], ["Text", 51]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 57, "text_extraction_method": "pdftext", "block_counts": [["Span", 211], ["Line", 90], ["Text", 39]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 58, "text_extraction_method": "pdftext", "block_counts": [["Span", 155], ["Line", 72], ["Text", 43]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 59, "text_extraction_method": "pdftext", "block_counts": [["Span", 48], ["Line", 27], ["Text", 15]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 60, "text_extraction_method": "pdftext", "block_counts": [["Span", 140], ["Line", 43], ["Text", 34]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 61, "text_extraction_method": "pdftext", "block_counts": [["Span", 11], ["Text", 3], ["Line", 3]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 62, "text_extraction_method": "pdftext", "block_counts": [["Span", 94], ["Line", 47], ["Text", 42]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 63, "text_extraction_method": "pdftext", "block_counts": [["Span", 87], ["Line", 44], ["Text", 42]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 64, "text_extraction_method": "pdftext", "block_counts": [["Span", 125], ["Text", 33], ["Line", 33]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 65, "text_extraction_method": "pdftext", "block_counts": [["Span", 57], ["Text", 15], ["Line", 15]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 66, "text_extraction_method": "pdftext", "block_counts": [["Span", 330], ["Line", 37], ["Text", 7]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 67, "text_extraction_method": "pdftext", "block_counts": [["Span", 101], ["Line", 36], ["Text", 30]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 68, "text_extraction_method": "pdftext", "block_counts": [["Span", 104], ["Line", 37], ["Text", 29]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 69, "text_extraction_method": "pdftext", "block_counts": [["Span", 93], ["Line", 31], ["Text", 27]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 70, "text_extraction_method": "pdftext", "block_counts": [["Span", 420], ["Line", 72], ["Text", 53]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 71, "text_extraction_method": "pdftext", "block_counts": [["Span", 68], ["Line", 11], ["Text", 9]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 72, "text_extraction_method": "pdftext", "block_counts": [["Span", 47], ["Line", 17], ["Text", 11]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 73, "text_extraction_method": "pdftext", "block_counts": [["Span", 36], ["Line", 12], ["Text", 4]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 74, "text_extraction_method": "pdftext", "block_counts": [["Span", 145], ["Line", 38], ["Text", 37]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 75, "text_extraction_method": "pdftext", "block_counts": [["Span", 73], ["Text", 17], ["Line", 17]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}], "debug_data_path": "debug_data\\16. The Prompt Report A Systematic Survey of Prompting Techniques"}