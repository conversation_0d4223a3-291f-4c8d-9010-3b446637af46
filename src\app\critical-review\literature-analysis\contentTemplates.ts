// c:\Nino\School\2025\Jaar 4\Supportive Narrative\thesis-narrative-site\src\app\critical-review\literature-analysis\contentTemplates.ts

export const snContextTemplate = `# Supportive Narrative Context Document

## Background

I am a fourth-year student in the Music & Technology program. Throughout my studies, I have shifted my focus towards developing audio plugins. I use JUCE in C++ and integrate neural networks for various applications.

During my projects, I collaborate with two other software developers on both commissioned and autonomous plugins. I strive to maximize the use of AI in my development process, including:

- Code Writing: Using AI for code generation and optimization.
- Bug Fixing: AI assistance in identifying and resolving bugs.
- Conceptual Explanation: Using AI to understand and explain complex concepts.
- Implementation Planning: Employing AI for structuring and planning development steps.
- Knowledge Acquisition: Efficiently gaining new knowledge through dialogue with AI.

Additionally, I have a Dual Profile as a Maker-Researcher, which means I maintain an investigative attitude and approach in both my projects and my Supportive Narrative (SN). This profile encourages the integration of theory and practice and critical reflection on one's own creation process.

## Supportive Narrative Objective

The goal of my Supportive Narrative is to develop a structured and well-founded methodology for efficiently implementing AI within my workflow as an audio plugin developer. This methodology must:

1. Analysis of Current AI Usage
- Thoroughly analyze my current, primarily intuitive use of AI.
- Identify which actions work to my advantage or disadvantage.

2. Formalizing Work Processes
- Translate intuitive approaches into concrete, repeatable steps.
- Concretize how to move from "it works" to "it works better."

3. Theoretical Foundation
- Understand why certain AI strategies are effective in software development.
- Support with academic literature and existing research.

4. Optimization of AI Interactions
- Provide practical tools for improving AI interactions.
- Develop step-by-step plans and best practices for using AI in my specific context.

5. Accessibility for Others
- Create a guide that is also useful for other developers in the creative technology sector.
- Help those who come after me get started in the right direction and avoid pitfalls.

## Research Question

How can I most efficiently apply AI within my workflow as an audio plugin developer, and how can I formalize and optimize this methodology for both myself and others in the sector?

## Methodology

To answer this question, I will:

1. Conduct Literature Research

- Identify and evaluate a collection of relevant papers and sources.
- Use a double diamond strategy to refine the selection.
- Evaluate papers based on evaluation criteria specifically aligned with my goals and context (see CR_Context.md).

2. Analyze Personal Practical Experience

- Document and critically assess my current use of AI.
- Identify successes and challenges.

3. Integration of Theory and Practice

- Connect findings from literature to my own experiences.
- Apply and test new insights within my workflow.

## Relevance

In an era where AI is increasingly integrated into various fields, it is essential to understand how we can optimally utilize this technology. By combining my experiences and research, I hope to improve my own workflow.`;

export const crContextTemplate = `# Critical Review Context Document

> Note: For the complete project context and objectives, see SN_Context.md

## Paper Evaluation Criteria

1. **Relevance for AI Use in Software Development** (0-100)
   - Addresses the use of AI for code generation, debugging, conceptual explanation, implementation planning, or knowledge acquisition
   - Specific focus on C++, JUCE, audio plugin development, or similar domains
   - Describes how AI can integrate into the development process

   Scoring breakdown:
   - 90-100: Directly applicable to audio plugin development with C++ and JUCE
   - 70-89: Relevant for C++ development with clear connection to audio
   - 50-69: General software development with AI, translatable to audio context
   - 30-49: Limited relevance, requires significant adaptation
   - 0-29: Minimal to no relevance for the domain

2. **Practical Applicability** (0-100)
   - Offers concrete methods or techniques that are directly implementable
   - Contains examples, case studies, or tools that align with my workflow
   - Provides step-by-step instructions or guidelines

   Scoring breakdown:
   - 90-100: Directly implementable with concrete code examples
   - 70-89: Implementable with minimal adjustments
   - 50-69: Implementable with significant adjustments
   - 30-49: Theoretically implementable but practically challenging
   - 0-29: Not practically implementable

3. **Scientific Foundation** (0-100)
   - Conclusions are based on solid research or empirical data
   - Article contains a solid methodology and analysis
   - Findings are peer-reviewed and generally recognized within the field

   Scoring breakdown:
   - 90-100: Excellent methodology with extensive empirical validation
   - 70-89: Solid research with good validation
   - 50-69: Acceptable methodology with some limitations
   - 30-49: Weak or unclear methodology
   - 0-29: No or inadequate scientific foundation

4. **Innovation & Currency** (0-100)
   - Publication date within the last 3 years
   - Introduces new insights, technologies, or trends
   - Relevant within the rapidly evolving field of AI and software development

   Scoring breakdown:
   - 90-100: Cutting-edge (≤1 year) with groundbreaking innovations
   - 70-89: Recent (≤2 years) with significant innovations
   - 50-69: Relatively recent (≤3 years) with incremental improvements
   - 30-49: Older or limited innovation
   - 0-29: Outdated or not innovative

5. **Alignment with Maker-Researcher Profile** (0-100)
   - Supports an investigative and reflective attitude in the making process
   - Stimulates integration of theory and practice
   - Promotes critical thinking and self-reflection on own workflow

   Scoring breakdown:
   - 90-100: Perfect balance between making and researching
   - 70-89: Strong in both aspects with slight imbalance
   - 50-69: Adequate balance but room for improvement
   - 30-49: Unbalanced focus
   - 0-29: No clear alignment with profile

### Scoring Guidelines
- Total score: Maximum 500 points
- Selection threshold: ≥ 360 points (72%)
- Minimum 80/100 on 'Relevance for AI Use in Software Development'
- Minimum 80/100 on 'Practical Applicability'

### Score Interpretation
- **450-500**: Exceptionally valuable - Direct impact on workflow
- **400-449**: Very valuable - Significant contribution to methodology
- **360-399**: Valuable - Useful insights with some limitations
- **300-359**: Limited value - Potentially relevant for specific aspects
- **0-299**: Not selected - Insufficient alignment with objectives`;

export const workflowTemplate = `# Critical Review Guidelines and Workflow

> Note: For project context see SN_Context.md
> For evaluation criteria see CR_Context.md
> For evaluation template see CR_Evaluation_Template.md

## Overview

This document describes the workflow and guidelines for the current phase of the project. The goal is to maintain a clear structure and approach for evaluating academic papers and effectively using the LLM in the analysis process.

## Workflow Steps

### Step 1: Analyze All Contextual Files

Action:
Read and understand the following files:
- SN_Context.md: Project context, background, goals, and research question
- CR_Context.md: Evaluation criteria and scoring guidelines
- CR_Evaluation_Template.md: Template for systematic paper assessment

Goal:
Ensure a good understanding of the project context and evaluation criteria before starting to analyze the papers.

### Step 2: Apply The Context To The PDF

Action:
- Open a PDF from the /Papers/PDFs/ folder.
- Analyze the paper within the context of the information from the contextual files.

Specifications:
- Identify how the paper aligns with the objectives and criteria.
- Pay attention to specific details relevant to your workflow as an audio plugin developer and the efficient use of AI.

### Step 3: Record The Result And Save It In The Right Place

Action:
- Fill out the evaluation template from CR_Evaluation_Template.md for the analyzed paper.
- Save the completed evaluation in /Papers/Evaluations/ with the name Evaluation_[PaperTitle].md.
- Add the summary and scores to Results.md in the /Results/ folder.

Goal:
Ensure systematic and organized documentation of all analyses and results.

### Step 4: Reflect On The Actions

Action:
Evaluate whether the analysis was successful:
- Have you covered all relevant aspects of the paper?
- Are the evaluation criteria correctly applied?
- Note your reflections in Reflection.md in the /Reflections/ folder.

Goal:
- Identify potential improvements in the analysis process.
- Determine if it's necessary to revise the analysis or adjust the process.

## Instructions For The LLM

When analyzing a PDF, the LLM should follow these steps:

### Understanding Context

- Read SN_Context.md, CR_Context.md, and CR_Evaluation_Template.md thoroughly.
- Understand the objectives, evaluation criteria, and scoring guidelines.

### Analyzing Paper

- Read the selected PDF document carefully.
- Identify the main points, methodology, results, and conclusions.
- Pay specific attention to how the paper relates to:
  * AI use in software development.
  * Practical applicability in audio plugin development.
  * Innovation and currentness within the field.

### Filling Out Evaluation Template

- Use the template from CR_Evaluation_Template.md to evaluate the paper.
- Give a score of 1-5 for each criterion.
- Write a brief summary (2-3 sentences) of the paper.
- Note relevant insights (minimum of 3).
- Make a decision about progression to Phase 2 and motivate this.

### Saving Result

- Save the evaluation as Evaluation_[PaperTitle].md in /Papers/Evaluations/.
- Add the evaluation to Results.md in /Results/.

### Performing Reflection

- Assess whether the analysis is complete and accurate.
- Note any difficulties or notable points in Reflection.md.
- Advise if adjustments are needed in the process or criteria.

## Guidelines For Analyzing Papers

### Objectivity:
- Be as objective as possible when scoring the criteria.
- Base your scores on information actually present in the paper.

### Relevance:
- Focus on information directly related to your research question and goals.
- Ignore superfluous details that don't contribute to the evaluation.

### Clarity:
- Write clear and concise summaries and insights.
- Avoid jargon unless necessary, and explain it when used.

### Consistency:
- Use the same terminology and structure in all evaluations.
- Ensure scores are consistent between different papers.

## Tips For Successful Analysis

### Preparation:
- Take time to truly understand the contextual files before starting.
- Note any questions and seek clarification if needed.

### Efficiency:
- Keep track of time to move efficiently through the papers.
- However, prioritize thoroughness over speed.

### Integrating Feedback:
- Use your reflections to continuously improve the process.
- Adjust the approach for subsequent analyses where needed.

## Next Steps

### After Analyzing All Papers:
- Check Results.md for completeness and consistency.
- Discuss the results and determine which papers proceed to Phase 2.
- Plan the approach for Phase 2, including any adjustments based on reflections.

### Updating Documentation:
- Update CR_Guidelines.md if there are changes in the workflow.
- Ensure all reflections and feedback are incorporated into the next phase.`;

export const evaluationTemplate = `# Critical Review Phase 1 - Paper Evaluation

> Note: For detailed scoring criteria and guidelines, see CR_Context.md

## [Paper Title]
**Author(s):** [Author names]
**Year:** [Publication year]
**DOI/Link:** [DOI or URL]

### Machine Readable Scores
json
{
    "metadata": {
        "title": "[Paper Title]",
        "authors": "[Author names]",
        "year": 0000,
        "doi": "[DOI/Link]"
    },
    "scores": {
        "relevance": {
            "development_tasks": 0,
            "domain_focus": 0,
            "integration": 0,
            "total": 0
        },
        "applicability": {
            "implementability": 0,
            "examples": 0,
            "resources": 0,
            "total": 0
        },
        "scientific": {
            "methodology": 0,
            "validation": 0,
            "peer_review": 0,
            "total": 0
        },
        "innovation": {
            "publication_date": 0,
            "innovative_aspects": 0,
            "field_relevance": 0,
            "total": 0
        },
        "alignment": {
            "theory_practice": 0,
            "reflection": 0,
            "workflow": 0,
            "total": 0
        },
        "total_score": 0,
        "selected": false
    }
}


### Scores

1. **Relevance for AI Use in Software Development** [0-100]
   - Subcriteria evaluation:
     - AI use for development tasks: [0/40]
     - C++/JUCE/Audio focus: [0/40]
     - Integration possibilities: [0/20]
   **Total**: [0/100]

   *Explanation:*
   - Development tasks: [brief explanation of score]
   - Focus: [brief explanation of score]
   - Integration: [brief explanation of score]

2. **Practical Applicability** [0-100]
   - Subcriteria evaluation:
     - Implementability: [0/40]
     - Concrete examples: [0/30]
     - Resource requirements: [0/30]
   **Total**: [0/100]

   *Explanation:*
   - Implementation: [brief explanation of score]
   - Examples: [brief explanation of score]
   - Resources: [brief explanation of score]

3. **Scientific Foundation** [0-100]
   - Subcriteria evaluation:
     - Methodology: [0/40]
     - Empirical validation: [0/30]
     - Peer review status: [0/30]
   **Total**: [0/100]

   *Explanation:*
   - Methodology: [brief explanation of score]
   - Validation: [brief explanation of score]
   - Review: [brief explanation of score]

4. **Innovation & Currency** [0-100]
   - Subcriteria evaluation:
     - Publication date: [0/30]
     - Innovative aspects: [0/40]
     - Relevance for field: [0/30]
   **Total**: [0/100]

   *Explanation:*
   - Date: [brief explanation of score]
   - Innovation: [brief explanation of score]
   - Relevance: [brief explanation of score]

5. **Alignment with Maker-Researcher Profile** [0-100]
   - Subcriteria evaluation:
     - Theory-practice balance: [0/40]
     - Reflective aspects: [0/30]
     - Workflow integration: [0/30]
   **Total**: [0/100]

   *Explanation:*
   - Balance: [brief explanation of score]
   - Reflection: [brief explanation of score]
   - Workflow: [brief explanation of score]

**Total Score**: [0/500]

### Summary
[2-3 sentences about key points of the paper]

### Executable Strategies
1. [Strategy 1]
   - Implementation steps: [steps]
   - Required resources: [resources]
   - Expected results: [results]

2. [Strategy 2]
   - Implementation steps: [steps]
   - Required resources: [resources]
   - Expected results: [results]

3. [Strategy 3]
   - Implementation steps: [steps]
   - Required resources: [resources]
   - Expected results: [results]

### Code Examples
cpp
// Relevant code snippets with comments


### Implementation Notes
- Resource requirements: [specifications]
- Potential challenges: [list]
- Optimization possibilities: [options]

### Selection Status
[ ] Selected (≥ 360 points)
[ ] Not selected (< 360 points)

Motivation: [brief explanation of selection status]
`;

export const promptTemplate = `# AI Paper Analysis Instructions

> Before starting: All evaluation criteria and scoring guidelines are in:
> C:/Nino/School/2025/Jaar 4/Supportive Narrative/Critical Review/Context/CR_Context.md

## Analysis Steps

1. **Load Context**
   
   Read the following files:
   a. C:/Nino/School/2025/Jaar 4/Supportive Narrative/Critical Review/Context/SN_Context.md
   b. C:/Nino/School/2025/Jaar 4/Supportive Narrative/Critical Review/Context/CR_Context.md
   c. C:/Nino/School/2025/Jaar 4/Supportive Narrative/Critical Review/Context/CR_Evaluation_Template.md
   d. C:/Nino/School/2025/Jaar 4/Supportive Narrative/Critical Review/Context/CR_Workflow.md
   

2. **Locate Paper**
   
   a. Select paper to analyze from: [Paper Path]
   b. Verify markdown file is accessible and readable
   c. Document selected paper title in evaluation metadata
   

3. **Create Evaluation File**
   
   a. Create new file in: C:/Nino/School/2025/Jaar 4/Supportive Narrative/Critical Review/Papers/Evaluations/
   b. Name format: Evaluation_[PaperTitle].md
   c. Copy template from: C:/Nino/School/2025/Jaar 4/Supportive Narrative/Critical Review/Context/CR_Evaluation_Template.md
   

4. **Document Metadata**
   
   In the new evaluation file:
   a. Paper title
   b. Author(s)
   c. Publication year
   d. DOI/Link
   e. Update JSON block with metadata
   

5. **Analyze Content**
   
   In the evaluation file:
   a. Read paper thoroughly
   b. Apply criteria from CR_Context.md
   c. Score each criterion with justification
   d. Update JSON scores block with all values
   e. Calculate and update total_score in JSON
   

6. **Extract Implementation Details**
   
   In the evaluation file:
   a. Document executable strategies
   b. Add code examples
   c. List implementation requirements
   

7. **Quality Check**
   
   In the evaluation file:
   a. Verify all scores match between text and JSON
   b. Validate template sections are complete
   c. Confirm selection status (≥360 points)
   d. Update "selected" field in JSON
   

8. **Update Results**
   
   In: C:/Nino/School/2025/Jaar 4/Supportive Narrative/Critical Review/Results/Results.md
   a. Add paper summary
   b. Include final scores
   c. List key strategies
   

9. **Document Reflection**
   
   In: C:/Nino/School/2025/Jaar 4/Supportive Narrative/Critical Review/Reflections/Reflection.md
   a. Add entry about analysis process
   b. Note any insights or challenges
   c. Suggest process improvements
   

10. **Generate Visualizations**
    
    a. Run: C:/Nino/School/2025/Jaar 4/Supportive Narrative/Critical Review/Results/generate_visualizations.py
    b. Verify outputs in: C:/Nino/School/2025/Jaar 4/Supportive Narrative/Critical Review/Results/Visualizations/
       - overview.png
       - radar_[PaperTitle].png
    

> Note: Refer to context files for detailed specifications at each step
`;

// Placeholder constants for the Second Iteration
export const snContextTemplate2 = `# Supportive Narrative Context Document

## Background

I am a fourth-year student in the Music & Technology program. Throughout my studies, I have shifted my focus towards developing audio plugins. I use JUCE in C++ and integrate neural networks for various applications.

During my projects, I collaborate with two other software developers on both commissioned and autonomous plugins. I strive to maximize the use of AI in my development process, including:

- Code Writing: Using AI for code generation and optimization.
- Bug Fixing: AI assistance in identifying and resolving bugs.
- Conceptual Explanation: Using AI to understand and explain complex concepts.
- Implementation Planning: Employing AI for structuring and planning development steps.
- Knowledge Acquisition: Efficiently gaining new knowledge through dialogue with AI.

Additionally, I have a Dual Profile as a Maker-Researcher, which means I maintain an investigative attitude and approach in both my projects and my Supportive Narrative (SN). This profile encourages the integration of theory and practice and critical reflection on one's own creation process.

## Supportive Narrative Objective

The goal of my Supportive Narrative is to develop a structured and well-founded methodology for efficiently implementing AI within my workflow as an audio plugin developer. This methodology must:

1. Analysis of Current AI Usage
- Thoroughly analyze my current, primarily intuitive use of AI.
- Identify which actions work to my advantage or disadvantage.

2. Formalizing Work Processes
- Translate intuitive approaches into concrete, repeatable steps.
- Concretize how to move from "it works" to "it works better."

3. Theoretical Foundation
- Understand why certain AI strategies are effective in software development.
- Support with academic literature and existing research.

4. Optimization of AI Interactions
- Provide practical tools for improving AI interactions.
- Develop step-by-step plans and best practices for using AI in my specific context.

5. Accessibility for Others
- Create a guide that is also useful for other developers in the creative technology sector.
- Help those who come after me get started in the right direction and avoid pitfalls.

## Research Question

How can I most efficiently apply AI within my workflow as an audio plugin developer, and how can I formalize and optimize this methodology for both myself and others in the sector?

## Methodology

To answer this question, I will:

1. Conduct Literature Research

- Identify and evaluate a collection of relevant papers and sources.
- Use a double diamond strategy to refine the selection.
- Evaluate papers based on evaluation criteria specifically aligned with my goals and context (see CR_Context.md).

2. Analyze Personal Practical Experience

- Document and critically assess my current use of AI.
- Identify successes and challenges.

3. Integration of Theory and Practice

- Connect findings from literature to my own experiences.
- Apply and test new insights within my workflow.

## Relevance

In an era where AI is increasingly integrated into various fields, it is essential to understand how we can optimally utilize this technology. By combining my experiences and research, I hope to improve my own workflow.`;

export const crContextTemplate2 = `# Critical Review Context Document

> Note: For the complete project context and objectives, see SN_Context.md

## Paper Evaluation Criteria: Lean 4-Pillar Rubric

This rubric is purpose-built for audio-plugin AI papers, with emphasis on "can I run it, does it measurably help, does it make debugging easier, and can I plug it into JUCE / C++?"

1. **Implementation Readiness** (0-100, 30% of total score)
   
   Sub-aspects:
   - **Code Link & License**: Paper cites public repo and permissive license.
   - **Build Snippet**: Shows exact compile/run commands (e.g., cmake .. && make && ./demo).
   - **Environment Spec**: CUDA / compiler / JUCE version listed.
   - **Minimal Example**: At least one full, compile-ready code listing (or pseudocode ≤20 lines) that reproduces a stated result.
   
   Scoring breakdown:
   - 90-100: Complete implementation package with public repo, clear build commands, detailed environment specs, and working examples
   - 70-89: Good implementation details with most components well-documented
   - 50-69: Adequate implementation information with some components documented
   - 30-49: Basic implementation details with significant gaps
   - 0-29: Minimal or missing implementation information
   
   Why this matters: You need complete implementation details to actually use the method in your projects.

2. **Verified Performance Impact** (0-100, 25% of total score)
   
   Sub-aspects:
   - **Metric Table**: CPU %, latency, or bug count reduction vs. baseline plugin.
   - **Benchmarked Code Output**: Diff or graph proves higher accuracy, lower error, or clearer code style.
   - **Stat Sig / Repetition**: Reports seeds or N runs to show consistency.
   
   Scoring breakdown:
   - 90-100: Comprehensive performance metrics with statistical validation and clear comparisons
   - 70-89: Strong performance data with good validation across multiple metrics
   - 50-69: Adequate performance measurements with some validation
   - 30-49: Limited performance data with minimal validation
   - 0-29: Unsubstantiated or missing performance claims
   
   Why this matters: Performance improvements must be measurable and consistent to justify adoption.

3. **Debuggability & Maintainability Gains** (0-100, 25% of total score)
   
   Sub-aspects:
   - **Error-Handling Walk-through**: Shows how method spots or fixes C++/JUCE or LLM-generated bugs (stack trace, unit test, log excerpt).
   - **Code Clarity**: Presents refactored snippet or prompt that cuts "spaghetti" into modular functions.
   - **Tooling Hooks**: Mentions static analyzers, sanitizers, or agent loop that auto-tests.
   
   Scoring breakdown:
   - 90-100: Exceptional debugging tools and maintainability improvements with comprehensive examples
   - 70-89: Strong debugging support and clear maintainability benefits
   - 50-69: Useful debugging features and moderate maintainability improvements
   - 30-49: Basic debugging capabilities with limited maintainability benefits
   - 0-29: Minimal or no attention to debugging and maintainability
   
   Why this matters: Easier debugging and maintenance directly translates to development time savings.

4. **Audio-Plugin Transfer Potential** (0-100, 20% of total score)
   
   Sub-aspects:
   - **Domain Mapping**: Explicit paragraph on integrating into VST/AU or real-time DSP chain.
   - **Resource Fit**: Quotes RAM/VRAM and block-size constraints typical of plugins.
   - **Generalisability**: Claims or demo for second audio task (e.g., same technique boosts both compressor and reverb).
   
   Scoring breakdown:
   - 90-100: Seamless integration with audio plugin frameworks and proven applicability across multiple audio tasks
   - 70-89: Good integration potential with clear resource considerations and demonstrated flexibility
   - 50-69: Reasonable transfer potential with some audio-specific adaptations
   - 30-49: Limited transfer potential requiring significant modifications
   - 0-29: Poor fit for audio plugin development with major obstacles
   
   Why this matters: Methods must be adaptable to audio plugin constraints and workflows to be valuable.

## Score Weighting
For analysis purposes, the following weights are applied to calculate the total score:
- Implementation Readiness: 30%
- Verified Performance Impact: 25%
- Debuggability & Maintainability Gains: 25%
- Audio-Plugin Transfer Potential: 20%

Note: Every score can be assigned from the PDF alone—no outside Googling required. These scores are used to analyze and compare papers' strengths in different areas, focusing on practical value for audio plugin development processes.`;

export const evaluationTemplate2Json = `
{
  "metadata": {
    "title": "[Paper Title]",
    "authors": "[Author names]",
    "year": 0,
    "doi": "[DOI/Link]"
  },
  "paper_summary": "[Provide a comprehensive 1-2 paragraph summary of the paper, including its core innovation, methodology, and key findings. This should be detailed enough to give a complete understanding of the paper's contribution.]",
  "scores": {
    "implementation_readiness": {
      "code_link_license": 0,
      "build_snippet": 0,
      "environment_spec": 0,
      "minimal_example": 0,
      "total": 0
    },
    "verified_performance_impact": {
      "metric_table": 0,
      "benchmarked_code_output": 0,
      "stat_sig_repetition": 0,
      "total": 0
    },
    "debuggability_maintainability": {
      "error_handling_walkthrough": 0,
      "code_clarity": 0,
      "tooling_hooks": 0,
      "total": 0
    },
    "audio_plugin_transfer": {
      "domain_mapping": 0,
      "resource_fit": 0,
      "generalisability": 0,
      "total": 0
    },
    "total_weighted_score": 0
  },
  "detailed_analysis": {
    "implementation_readiness": {
      "code_link_license": "[Provide a detailed analysis (1-2 paragraphs) of the code availability and licensing. Include: Public repository URL, License type (MIT/BSD/GPL), Access restrictions if any, Documentation of code assets]",
      "build_snippet": "[Provide a detailed analysis (1-2 paragraphs) of the build instructions. Include: Exact compile/run commands, Build system used (cmake, make, etc.), Clarity and completeness of instructions, Any missing steps]",
      "environment_spec": "[Provide a detailed analysis (1-2 paragraphs) of the environment specifications. Include: CUDA/compiler/JUCE version requirements, Dependencies and their versions, Hardware requirements, OS compatibility]",
      "minimal_example": "[Provide a detailed analysis (1-2 paragraphs) of code examples. Include: Completeness of provided examples, Whether they compile and run as stated, How well they demonstrate the paper's results, Length and clarity of code/pseudocode]"
    },
    "verified_performance_impact": {
      "metric_table": "[Provide a detailed analysis (1-2 paragraphs) of performance metrics. Include: CPU usage percentages, Latency measurements, Memory usage statistics, Comparison to baseline plugin performance]",
      "benchmarked_code_output": "[Provide a detailed analysis (1-2 paragraphs) of code output quality. Include: Accuracy improvements, Error rate reductions, Code style enhancements, Visual diffs or graphs showing improvements]",
      "stat_sig_repetition": "[Provide a detailed analysis (1-2 paragraphs) of statistical significance. Include: Number of experiment runs, Seeds used for reproducibility, Consistency of results across runs, Statistical methods applied]"
    },
    "debuggability_maintainability": {
      "error_handling_walkthrough": "[Provide a detailed analysis (1-2 paragraphs) of error handling. Include: How the method identifies bugs, Examples of stack traces or error logs, Handling of C++/JUCE specific issues, LLM-generated bug detection]",
      "code_clarity": "[Provide a detailed analysis (1-2 paragraphs) of code organization. Include: Before/after examples of refactored code, Modularity improvements, Function naming and documentation, Reduction in 'spaghetti code']",
      "tooling_hooks": "[Provide a detailed analysis (1-2 paragraphs) of development tools integration. Include: Static analyzers used, Sanitizers implemented, Automated testing frameworks, Agent loops for continuous testing]"
    },
    "audio_plugin_transfer": {
      "domain_mapping": "[Provide a detailed analysis (1-2 paragraphs) of audio plugin integration. Include: Explicit VST/AU integration guidance, Real-time DSP chain compatibility, Audio-specific adaptations, Implementation challenges]",
      "resource_fit": "[Provide a detailed analysis (1-2 paragraphs) of resource requirements. Include: RAM/VRAM usage figures, Block-size constraints and handling, Latency considerations for real-time audio, Resource optimization techniques]",
      "generalisability": "[Provide a detailed analysis (1-2 paragraphs) of applicability to multiple audio tasks. Include: Additional audio applications demonstrated, Adaptation requirements for different effects, Examples of technique working across different plugin types, Retraining or modification needs]"
    }
  },
  "key_strategies": [
    "1. **[Strategy Title]:** [Detailed description of the first key strategy with specific implementation details]",
    "2. **[Strategy Title]:** [Detailed description of the second key strategy with specific implementation details]",
    "3. **[Strategy Title]:** [Detailed description of the third key strategy with specific implementation details]",
    "4. **[Strategy Title]:** [Detailed description of the fourth key strategy with specific implementation details]",
    "5. **[Strategy Title]:** [Detailed description of the fifth key strategy with specific implementation details]",
    "6. **[Strategy Title]:** [Detailed description of the sixth key strategy with specific implementation details]",
    "7. **[Strategy Title]:** [Detailed description of the seventh key strategy with specific implementation details]"
  ],
  "key_takeaways": [
    "1. **AI Technique:** [Detailed description of the AI technique with specific insights]",
    "2. **Process Impact:** [Detailed description of the process impact with specific insights]",
    "3. **Implementation:** [Detailed description of the implementation considerations with specific insights]",
    "4. **Results:** [Detailed description of the results and outcomes with specific insights]",
    "5. **Experience:** [Detailed description of the experience aspects with specific insights]"
  ],
  "method_applicability": "[Provide a detailed analysis (2-3 paragraphs) of practical application to audio plugin development. Include: Direct implementation possibilities, Required adaptations, Expected outcomes, Specific use cases in audio plugin development, Integration with existing tools and frameworks.]",
  "summary": "[Provide a comprehensive summary (3-5 sentences) covering: Core innovation/contribution, Practical value for software development, Implementation feasibility, Key differentiators, Potential impact.]",
  "implementation_guide": {
    "setup": [
      "1. **[Technical Requirement]:** [Detailed description of specific technical requirement]",
      "2. **[Tool Dependency]:** [Detailed description of required tool or dependency]",
      "3. **[Environment Need]:** [Detailed description of environment configuration]",
      "4. **[Configuration Detail]:** [Detailed description of configuration requirement]",
      "5. **[Compatibility Consideration]:** [Detailed description of compatibility issue to be aware of]"
    ],
    "steps": [
      "1. **Initial Setup:** [Detailed description of the initial setup process]",
      "2. **Configuration:** [Detailed description of the configuration process]",
      "3. **Integration:** [Detailed description of the integration process]",
      "4. **Testing:** [Detailed description of the testing process]",
      "5. **Validation:** [Detailed description of the validation process]",
      "6. **Optimization:** [Detailed description of the optimization process]",
      "7. **Deployment:** [Detailed description of the deployment process]"
    ],
    "validation": [
      "1. **Success Metrics:** [Detailed description of how to measure success]",
      "2. **Expected Outcomes:** [Detailed description of expected outcomes]",
      "3. **Validation Process:** [Detailed description of the validation process]",
      "4. **Testing Methodology:** [Detailed description of testing methodology]",
      "5. **Quality Assurance:** [Detailed description of quality assurance steps]"
    ]
  },
  "methodologicalDeepDive": [
    {
      "methodName": "[Clearly state the name of the specific method or technique discussed in the paper, e.g., 'Zero-shot Chain-of-Thought Prompting', 'Knowledge Distillation for Model Compression']",
      "simplifiedExplanation": "[Explain the core concept of this method in simple terms, using analogies or metaphors if helpful. Aim for clarity for someone not deeply familiar with the specific jargon. E.g., 'Imagine teaching a student by showing them how to work through a problem step-by-step, rather than just giving them the answer.']",
      "prerequisites": [
        "[Prerequisite 1: Specific software, hardware, or knowledge requirement]",
        "[Prerequisite 2: Another specific requirement]",
        "[Prerequisite 3: Another specific requirement]",
        "[Prerequisite 4: Another specific requirement]"
      ],
      "stepByStepGuide": [
        "1. [First step with clear, actionable instruction]",
        "2. [Second step with clear, actionable instruction]",
        "3. [Third step with clear, actionable instruction]",
        "4. [Fourth step with clear, actionable instruction]",
        "5. [Fifth step with clear, actionable instruction]",
        "6. [Sixth step with clear, actionable instruction]",
        "7. [Seventh step with clear, actionable instruction]"
      ],
      "practicalExample": {
        "scenarioDescription": "[Describe a consistent, practical scenario relevant to your thesis (e.g., 'Improving the factual accuracy of an LLM responding to user queries about audio synthesis techniques' or 'Debugging a common Python script that fails to correctly process MIDI data'). This scenario should be the *same* for each method across different papers to allow comparison. The prompt will later specify this standard scenario.]",
        "implementationCode": "[language_extension]\n// Provide a concrete, runnable (or near-runnable) code snippet demonstrating the method applied to the scenario described above.\n// Ensure the code is well-commented and clearly illustrates the method's application.\n// Example for a prompting technique:\n// const prompt = User query: {query}. Let's think step by step to ensure accuracy. First, identify keywords...;\n// const result = await llm.generate(prompt.replace('{query}', 'How does FM synthesis work?'));\n",
        "expectedOutcome": "[Describe the expected result or observation when the method is correctly applied to the practical example. E.g., 'The LLM's response should now include a step-by-step breakdown of FM synthesis principles, leading to a more accurate and comprehensive answer compared to a simple direct query.' or 'The Python script should now correctly parse all MIDI note events and print their pitch and velocity.']"
      }
    }
  ],
  "resultsInsights": {
    "claimedOutcomes": "[Summarize the primary outcomes and performance improvements as claimed by the paper. Include key metrics and how they were measured, as reported by the authors.]",
    "contextualizedBenefits": {
      "audioPluginApplications": "[Describe specific applications and benefits of the paper's findings/methods within the context of audio plugin development. How could it improve audio processing, UI, workflow, etc.?]",
      "problemSolvingPotential": "[Identify specific problems in audio plugin development that these results could help solve or alleviate.]"
    },
    "contextualizedDrawbacks": {
      "limitationsForAudio": "[Discuss potential limitations, downsides, or challenges when applying these methods/results to audio plugin development (e.g., latency, CPU, data needs, real-time constraints).]",
      "implementationHurdles": "[What are the practical hurdles to implementing or adopting these findings in an audio plugin project (e.g., complexity, toolchain compatibility, expertise required)?]"
    },
    "feasibilityAssessment": "[Provide an overall assessment of the practicality and feasibility of leveraging the paper's findings in real-world audio plugin projects. Consider resource requirements and potential ROI.]",
    "keyTakeawaysForAudioDev": [
      "1. [First key takeaway: Concise, actionable insight specifically relevant to audio plugin development]",
      "2. [Second key takeaway: Another specific, actionable insight]",
      "3. [Third key takeaway: Another specific, actionable insight]",
      "4. [Fourth key takeaway: Another specific, actionable insight]",
      "5. [Fifth key takeaway: Another specific, actionable insight]"
    ]
  },
  "conclusion": "[Provide a detailed conclusion (1-2 paragraphs) summarizing the paper's contribution and value. Include: Score analysis, Key strengths, Limitations, Comparative advantages, Implementation feasibility, Expected impact on audio plugin development, and final assessment of the paper's significance.]"
}
`;

export const promptTemplate2 = `# AI Paper Analysis Instructions

> IMPORTANT: This analysis creates a SINGLE comprehensive JSON file containing ALL evaluation details.
> The JSON file must include detailed analysis, key strategies, and implementation guidance that was previously split between evaluation and results files.
> All evaluation criteria and scoring guidelines are in:
> C:/Nino/School/2025/Jaar 4/Supportive Narrative/Critical Review/Context/CR_Context.md

## Analysis Steps

1. **Load Context**
   
   Read the following files:
   a. C:/Nino/School/2025/Jaar 4/Supportive Narrative/Critical Review/Context/CR_Context.md (Scoring criteria)
   b. C:/Nino/School/2025/Jaar 4/Supportive Narrative/Critical Review/Context/SN_Context.md (Scoring criteria)

   c. C:/Nino/School/2025/Jaar 4/Supportive Narrative/Critical Review/Context/evaluation_template.json (Template structure)
   

2. **Locate Paper**
   
   a. Select paper to analyze from: [Paper Path]
   b. Verify markdown file is accessible and readable
   c. Document selected paper title for use in the evaluation
   

3. **Create Evaluation File**
   
   a. Create new file in: C:/Nino/School/2025/Jaar 4/Supportive Narrative/Critical Review/Papers/Evaluations/
   b. Name format: Evaluation_[PaperTitle].json
   c. Copy template from: C:/Nino/School/2025/Jaar 4/Supportive Narrative/Critical Review/Context/evaluation_template.json
   

4. **Document Metadata**
   
   In the JSON structure within the evaluation file:
   a. Update "metadata.title" with the paper title
   b. Update "metadata.authors" with the author names
   c. Update "metadata.year" with the publication year (as a number)
   d. Update "metadata.doi" with the DOI or arXiv ID
   

5. **Score Each Category**
     
     In the JSON structure, update the following score values (0-100):
     a. scores.implementation_readiness.code_link_license - Score for public repo and permissive license
     b. scores.implementation_readiness.build_snippet - Score for exact compile/run commands
     c. scores.implementation_readiness.environment_spec - Score for CUDA/compiler/JUCE version specifications
     d. scores.implementation_readiness.minimal_example - Score for compile-ready code listing/pseudocode
     e. scores.implementation_readiness.total - Calculate average of the above four scores
     
     f. scores.verified_performance_impact.metric_table - Score for CPU/latency/bug count metrics
     g. scores.verified_performance_impact.benchmarked_code_output - Score for accuracy/error/code style improvements
     h. scores.verified_performance_impact.stat_sig_repetition - Score for statistical validation and consistency
     i. scores.verified_performance_impact.total - Calculate average of the above three scores
     
     j. scores.debuggability_maintainability.error_handling_walkthrough - Score for bug detection/fixing capabilities
     k. scores.debuggability_maintainability.code_clarity - Score for code organization and modularity
     l. scores.debuggability_maintainability.tooling_hooks - Score for development tools integration
     m. scores.debuggability_maintainability.total - Calculate average of the above three scores
     
     n. scores.audio_plugin_transfer.domain_mapping - Score for VST/AU integration guidance
     o. scores.audio_plugin_transfer.resource_fit - Score for RAM/VRAM and block-size constraints
     p. scores.audio_plugin_transfer.generalisability - Score for application to multiple audio tasks
     q. scores.audio_plugin_transfer.total - Calculate average of the above three scores
     
     r. scores.total_weighted_score - Calculate weighted average using these weights:
        - Implementation Readiness: 30%
        - Verified Performance Impact: 25%
        - Debuggability & Maintainability: 25%
        - Audio-Plugin Transfer Potential: 20%
    

6. **Provide Detailed Analysis**
   
   In the JSON structure, fill in the detailed_analysis section:
   
   a. detailed_analysis.implementation_readiness.code_link_license - Analysis of code availability and licensing
   b. detailed_analysis.implementation_readiness.build_snippet - Analysis of build instructions and commands
   c. detailed_analysis.implementation_readiness.environment_spec - Analysis of environment specifications
   d. detailed_analysis.implementation_readiness.minimal_example - Analysis of code examples and their completeness
   
   e. detailed_analysis.verified_performance_impact.metric_table - Analysis of performance metrics
   f. detailed_analysis.verified_performance_impact.benchmarked_code_output - Analysis of code output quality
   g. detailed_analysis.verified_performance_impact.stat_sig_repetition - Analysis of statistical significance
   
   h. detailed_analysis.debuggability_maintainability.error_handling_walkthrough - Analysis of error handling
   i. detailed_analysis.debuggability_maintainability.code_clarity - Analysis of code organization
   j. detailed_analysis.debuggability_maintainability.tooling_hooks - Analysis of development tools integration
   
   k. detailed_analysis.audio_plugin_transfer.domain_mapping - Analysis of audio plugin integration
   l. detailed_analysis.audio_plugin_transfer.resource_fit - Analysis of resource requirements
   m. detailed_analysis.audio_plugin_transfer.generalisability - Analysis of applicability to multiple audio tasks
   

7. **Extract Key Strategies**
   
   a. Identify 5-7 key strategies or techniques from the paper
   b. **IMPORTANT**: This must be formatted as an ARRAY of strings, not a single string
   c. Format each strategy as a numbered item with a bold title
   d. Focus on actionable, implementable strategies
   e. Add these to the key_strategies array in the JSON structure
   f. **JSON Format Example**:
      json
      "key_strategies": [
        "1. **Dynamic Temperature Sampling:** Apply higher temperatures for 'challenging' tokens (often at code block beginnings) and lower temperatures for 'confident' tokens.",
        "2. **Heuristic Development:** Create a simple rule-based system to identify when the next token to be generated is 'challenging'.",
        "3. **Temperature Parameter Tuning:** Empirically determine optimal values for high temperature (a) and low temperature (b).",
        "4. **Token-by-Token Processing:** Implement a system that evaluates each token position before generation.",
        "5. **Integration with Top-p Sampling:** Combine adaptive temperature with nucleus sampling for better results."
      ],
      
   e. Ensure strategies are relevant to software development
   

8. **Add Summary Sections**
   
   a. Complete key_takeaways section with 5 detailed points (1-2 paragraphs each)
      - **IMPORTANT**: This must be formatted as an ARRAY of strings, not a single string
      - Format each takeaway as a numbered item with a bold title
      - Cover: AI Technique, Process Impact, Implementation, Results, Experience
      - **JSON Format Example**:
        json
        "key_takeaways": [
          "1. **AI Technique:** AdapT introduces a novel dynamic temperature sampling approach that varies the randomness of token selection based on the token's position and importance.",
          "2. **Process Impact:** The method significantly improves code generation quality across multiple LLMs without requiring model retraining or fine-tuning.",
          "3. **Implementation:** The technique can be implemented as a lightweight wrapper around existing LLM APIs that support temperature control.",
          "4. **Results:** Consistent improvements in pass@k metrics across multiple benchmarks and reduction in specific error types.",
          "5. **Experience:** Developers can expect more reliable code generation, particularly for structurally complex code segments."
        ],
        

   b. Analyze method applicability to audio plugin development

   c. Write comprehensive summary (3-5 sentences)

   d. Complete implementation_guide with detailed setup, steps, and validation
      - **IMPORTANT**: Each field (setup, steps, validation) must be formatted as an ARRAY of strings, not a single string
      - Format each item as a numbered item with a bold title
      - **JSON Format Example for setup**:
        json
        "setup": [
          "1. **LLM API Access:** Access to an LLM API that allows per-token temperature control.",
          "2. **Programming Environment:** Python 3.8+ with requests and json libraries.",
          "3. **Token Processing Library:** A library for tokenizing and processing C++ code.",
          "4. **Development IDE:** Visual Studio Code or similar with C++ extensions.",
          "5. **JUCE Framework:** Latest version of JUCE installed and configured."
        ],
        
      - **JSON Format Example for steps**:
        json
        "steps": [
          "1. **Initial Setup:** Install required libraries and configure API access.",
          "2. **Heuristic Development:** Create a function to identify 'challenging' tokens in C++ code.",
          "3. **Temperature Control Implementation:** Develop the core AdapT algorithm for token-by-token temperature adjustment.",
          "4. **Integration with LLM API:** Connect your implementation to the LLM API.",
          "5. **Testing:** Validate the implementation with simple C++ generation tasks.",
          "6. **Parameter Tuning:** Adjust temperature values for optimal C++ generation.",
          "7. **JUCE Integration:** Adapt the system for JUCE-specific code generation."
        ],
        
      - **JSON Format Example for validation**:
        json
        "validation": [
          "1. **Compilation Success Rate:** Measure the percentage of generated code snippets that compile without errors.",
          "2. **Functional Correctness:** Test if the generated code performs its intended function.",
          "3. **Error Reduction:** Compare error types and frequencies with and without AdapT.",
          "4. **Code Quality Metrics:** Evaluate generated code using static analysis tools.",
          "5. **Developer Feedback:** Gather qualitative feedback from developers using the system."
        ],
        

   e. Write a detailed conclusion summarizing the paper's contribution and value (1-2 paragraphs)
   

9. **Populate Methodological Deep Dive**
    
{{ ... }}

    **IMPORTANT JSON COMPATIBILITY NOTE:** All text and code you enter into these fields MUST be JSON-compatible. This means:
    - **Escape Special Characters:** Backslashes (\), double quotes ("), and other special characters within your strings must be properly escaped (e.g., \\ for a backslash, \" for a double quote, \n for a newline within a string).
    - **Multi-line Strings:** For fields like simplifiedExplanation, stepByStepGuide, scenarioDescription, and implementationCode, ensure newlines are represented as \n if the string is on a single line in the JSON. Alternatively, you can use an array of strings if the JSON structure supports it (though our current template uses single strings with \n).
    - **Code Blocks:** When including code in implementationCode, ensure the entire code block is a single JSON string. This often means converting actual newlines into \n and escaping any quotes or special characters within the code itself.
    - **Validate:** It is highly recommended to paste the entire JSON into a validator after filling it out to catch syntax errors before saving.

    a. methodologicalDeepDive[n].methodName:
       - Clearly state the name of the specific method or technique (e.g., "Zero-shot Chain-of-Thought Prompting", "Knowledge Distillation for Model Compression").
       - Use the terminology from the paper.
       - **JSON Reminder**: Ensure any special characters in your explanation are escaped.

    b. methodologicalDeepDive[n].simplifiedExplanation:
       - Explain the core concept of this method in simple terms, using analogies or metaphors if helpful.
       - Aim for clarity for someone not deeply familiar with the specific jargon.
       - Example: "Chain-of-Thought prompting guides an LLM to 'think step by step' by having it articulate its reasoning process before arriving at an answer, much like a student showing their work."
       - **JSON Reminder**: Ensure any special characters in your explanation are escaped.

    c. methodologicalDeepDive[n].prerequisites:
       - **IMPORTANT**: This must be formatted as an ARRAY of strings, not a single string.
       - List all necessary prerequisites for applying this method, as indicated or implied by the paper.
       - Be specific. Include:
         - Software requirements (e.g., Python 3.8+, PyTorch)
         - Hardware requirements (e.g., GPU with >16GB VRAM)
         - Datasets or existing models needed
         - Conceptual understanding required (e.g., familiarity with transformer architectures)
       - **JSON Format Example**:
         json
         "prerequisites": [
           "Access to an LLM that supports per-token temperature adjustment during generation.",
           "Ability to interface with the LLM to provide context and receive generated tokens one by one.",
           "A heuristic to identify 'challenging tokens' in the target programming language.",
           "Chosen values for hyperparameters: a (high temperature) and b (low temperature)."
         ],
         

    d. methodologicalDeepDive[n].stepByStepGuide:
       - **IMPORTANT**: This must be formatted as an ARRAY of strings, not a single string.
       - Provide a numbered, actionable step-by-step guide to implement or execute this method.
       - Each step should be clear, concise, and practical.
       - Format each step as a separate string in the array, with the number included in the string.
       - **JSON Format Example**:
         json
         "stepByStepGuide": [
           "1. **Define Challenging Token Heuristic:** Determine how to identify when the next token qualifies as a 'challenging token'.",
           "2. **Start Autoregressive Generation:** Begin generating code token by token.",
           "3. **Assess Next Token Type:** Apply the heuristic to predict if the upcoming token is 'challenging'.",
           "4. **Select Temperature:** Set sampling temperature based on token type assessment.",
           "5. **Apply Temperature and Sample:** Rescale the LLM's output logits using the selected temperature.",
           "6. **Select and Append Token:** Sample the next token and add it to the sequence.",
           "7. **Repeat:** Continue until the desired length or end token is generated."
         ],
         

    e. methodologicalDeepDive[n].practicalExample:
       - This object should detail a practical application or example of the method, ideally drawn directly from the paper.

       i. practicalExample.scenarioDescription:
          - Describe the scenario or problem context where this method is applied, as presented in the paper.
          - **JSON Reminder**: Ensure this description is a valid JSON string, escaping newlines (\n) and special characters.

       ii. practicalExample.implementationCode:
           - Provide a concise code snippet demonstrating the method's application in the described scenario. This should be directly from the paper or a very close adaptation.
           - **IMPORTANT**: The code block MUST be a valid JSON string. This means all newlines within the code must be converted to \n, and all double quotes (") within the code must be escaped as \". Other special characters might also need escaping.
           - Format as a single string, e.g., "python\nprint('Hello, world!')\n# This is a comment\n"

       iii. practicalExample.expectedOutcome:
            - Describe the expected result or outcome when the method is applied to this scenario, according to the paper.
            - **JSON Reminder**: Ensure this description is a valid JSON string, escaping newlines (\n) and special characters.

10. **Analyze Results and Insights**
    
    In the JSON structure, fill in the resultsInsights section. Focus on critically analyzing the paper's findings and their implications, especially for audio plugin development.

    a. resultsInsights.claimedOutcomes:
       - Summarize the primary outcomes, key findings, and performance improvements as *claimed by the paper's authors*.
       - Include specific metrics reported and how they were measured.
       - This should be an objective summary of what the paper presents as its results.

    b. resultsInsights.contextualizedBenefits.audioPluginApplications:
       - Critically think about how the paper's findings or methods could *specifically benefit audio plugin development*.
       - Consider improvements to sound quality, CPU efficiency, user interface design, creative workflows, development speed, etc.
       - Provide concrete, imaginative examples if possible (e.g., "This technique could enable real-time spectral processing with lower latency in a VST effect.").

    c. resultsInsights.contextualizedBenefits.problemSolvingPotential:
       - Identify specific problems or challenges *currently existing in audio plugin development* that these results could help solve or alleviate.
       - Be specific about the problems (e.g., "difficulty in achieving realistic emulations of analog hardware," "high computational cost of current AI-based synthesizers").

    d. resultsInsights.contextualizedDrawbacks.limitationsForAudio:
       - Discuss potential limitations, downsides, or challenges when *applying these methods/results specifically to audio plugin development*.
       - Consider factors like real-time processing constraints, audio-specific data requirements, latency issues, CPU/memory footprint, and integration with existing audio APIs/SDKs (JUCE, VST, AU).

    e. resultsInsights.contextualizedDrawbacks.implementationHurdles:
       - What are the practical hurdles to implementing or adopting these findings in an actual audio plugin project?
       - Consider complexity of the method, availability of tools/libraries, required expertise, dataset availability/creation, and compatibility with typical audio development toolchains.

    f. resultsInsights.feasibilityAssessment:
       - Provide an overall assessment of the *practicality and feasibility* of leveraging the paper's findings in real-world audio plugin projects.
       - Consider the balance between potential benefits and the resources/effort required (e.g., time, cost, expertise).
       - Is it a low-hanging fruit, a significant research effort, or somewhere in between for the audio domain?

    g. resultsInsights.keyTakeawaysForAudioDev:
       - **IMPORTANT**: This must be formatted as an ARRAY of strings, not a single string.
       - List 3-5 key actionable takeaways or insights from the paper's results that would be *most valuable to an audio plugin developer or researcher*.
       - These should be concise, impactful statements that summarize the core value proposition or critical considerations for the audio field.
       - Format each takeaway as a separate string in the array, with the number included in the string.
       - **JSON Format Example**:
         json
         "keyTakeawaysForAudioDev": [
           "1. **Dynamic Temperature Can Improve C++ Generation:** The core idea of varying LLM sampling temperature based on context is likely beneficial for C++ code generation.",
           "2. **Identify 'Challenging' C++ Constructs:** Success hinges on effectively identifying what constitutes a 'challenging token' in this context.",
           "3. **Tune Hyperparameters for C++:** The optimal high/low temperature values will likely need specific tuning for C++ generation tasks.",
           "4. **Focus on Scaffolding and Boilerplate:** AdapT-enhanced LLMs are most promising for generating C++ boilerplate and class structures.",
           "5. **Human Oversight Remains Critical:** All LLM-generated C++ code for audio plugins requires thorough human review and optimization."
         ],
         
    

11. **Review and Refine JSON**
    
    a. Ensure valid JSON structure
    b. Verify all fields are completed with detailed content
    c. Check for typos and errors
    d. Ensure content is comprehensive and detailed
    

12. **Final Verification**
    
    Perform these quality checks:
    a. Verify JSON structure is valid and complete
    b. Ensure all scores are within 0-100 range
    c. Confirm all analysis sections are filled in with detailed content
    d. Ensure the conclusion provides a balanced assessment based on the total_weighted_score
    e. Ensure the level of detail matches what was previously in separate markdown files
    

> IMPORTANT: The JSON file must be comprehensive and detailed. Each section should contain thorough analysis with specific examples, technical details, and implementation considerations. The goal is to create a single JSON file that contains ALL the information that was previously split between evaluation and results files.

> NOTE: No separate markdown results file is needed. All content should be in the single JSON file.
`;