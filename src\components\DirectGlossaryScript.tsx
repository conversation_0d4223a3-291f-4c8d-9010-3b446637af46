'use client';

import React, { useEffect, useRef } from 'react';
import { useGlossary } from '@/context/GlossaryContext';
import { usePathname } from 'next/navigation';

export default function DirectGlossaryScript() {
  const { terms, isLoading } = useGlossary();
  const pathname = usePathname();
  const processedNodes = useRef(new WeakSet());

  // Skip processing on the glossary page
  const shouldSkip = pathname === '/glossary';

  useEffect(() => {
    if (shouldSkip || isLoading || !terms || terms.length === 0) {
      return;
    }

    // Create tooltip container if it doesn't exist
    let tooltipContainer = document.getElementById('glossary-tooltip');
    if (!tooltipContainer) {
      tooltipContainer = document.createElement('div');
      tooltipContainer.id = 'glossary-tooltip';
      tooltipContainer.style.position = 'fixed';
      tooltipContainer.style.zIndex = '9999';
      tooltipContainer.style.backgroundColor = 'white';
      tooltipContainer.style.color = 'black';
      tooltipContainer.style.padding = '8px';
      tooltipContainer.style.borderRadius = '4px';
      tooltipContainer.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';
      tooltipContainer.style.maxWidth = '300px';
      tooltipContainer.style.fontSize = '14px';
      tooltipContainer.style.pointerEvents = 'none';
      tooltipContainer.style.opacity = '0';
      tooltipContainer.style.transition = 'opacity 0.2s';
      document.body.appendChild(tooltipContainer);
    }

    // Create tooltip arrow if it doesn't exist
    let tooltipArrow = tooltipContainer.querySelector('.tooltip-arrow') as HTMLElement;
    if (!tooltipArrow) {
      tooltipArrow = document.createElement('div');
      tooltipArrow.className = 'tooltip-arrow';
      tooltipArrow.style.position = 'absolute';
      tooltipArrow.style.width = '10px';
      tooltipArrow.style.height = '10px';
      tooltipArrow.style.backgroundColor = 'white';
      tooltipArrow.style.transform = 'rotate(45deg)';
      tooltipArrow.style.bottom = '-5px';
      tooltipArrow.style.left = '50%';
      tooltipArrow.style.marginLeft = '-5px';
      tooltipContainer.appendChild(tooltipArrow);
    }

    // Create a map of terms for faster lookup
    const termMap = new Map();
    terms.forEach(term => {
      if (term.term.length >= 3) {
        const normalizedTerm = term.term.toLowerCase();
        if (!termMap.has(normalizedTerm)) {
          termMap.set(normalizedTerm, {
            term: term.term,
            definition: `${term.term}${term.acronym ? ` (${term.acronym})` : ''}: ${term.definition}`
          });
        }
      }

      if (term.acronym && term.acronym.length >= 2) {
        const normalizedAcronym = term.acronym.toLowerCase();
        if (!termMap.has(normalizedAcronym)) {
          termMap.set(normalizedAcronym, {
            term: term.acronym,
            definition: `${term.term}${term.acronym ? ` (${term.acronym})` : ''}: ${term.definition}`
          });
        }
      }
    });

    // Function to show tooltip
    const showTooltip = (element: HTMLElement, definition: string) => {
      if (!tooltipContainer) return;

      tooltipContainer.textContent = '';
      tooltipContainer.appendChild(document.createTextNode(definition));
      tooltipContainer.appendChild(tooltipArrow!);

      const rect = element.getBoundingClientRect();
      tooltipContainer.style.left = `${rect.left + (rect.width / 2) - (tooltipContainer.offsetWidth / 2)}px`;
      tooltipContainer.style.top = `${rect.top - tooltipContainer.offsetHeight - 10}px`;
      tooltipContainer.style.opacity = '1';
    };

    // Function to hide tooltip
    const hideTooltip = () => {
      if (tooltipContainer) {
        tooltipContainer.style.opacity = '0';
      }
    };

    // Function to process a single text node
    const processTextNode = (node: Text) => {
      const text = node.textContent;
      if (!text || text.trim().length < 3) return false;

      const parent = node.parentElement;
      if (!parent || processedNodes.current.has(parent)) return false;

      // Check if any parent has data-no-glossary attribute
      let currentElement: HTMLElement | null = parent;
      while (currentElement) {
        if (currentElement.hasAttribute('data-no-glossary')) {
          return false; // Skip processing this node and its children
        }
        currentElement = currentElement.parentElement;
      }

      // Skip if in excluded elements
      if (
        parent.tagName === 'SCRIPT' ||
        parent.tagName === 'STYLE' ||
        parent.tagName === 'CODE' ||
        parent.tagName === 'PRE' ||
        parent.closest('script, style, code, pre, textarea, input, button, [data-no-glossary], .no-glossary')
      ) {
        return false;
      }

      // Check if text contains any glossary terms using proper word boundary matching
      let hasGlossaryTerm = false;
      for (const [key, termInfo] of termMap.entries()) {
        // Escape special regex characters in the term
        const escapedKey = key.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        // Create regex with word boundaries to ensure exact word matching
        const regex = new RegExp(`\\b${escapedKey}\\b`, 'i');
        if (regex.test(text)) {
          hasGlossaryTerm = true;
          break;
        }
      }

      if (!hasGlossaryTerm) return false;

      // Mark as processed
      processedNodes.current.add(parent);

      // Create document fragment to hold the new content
      const fragment = document.createDocumentFragment();
      let lastIndex = 0;

      // Create array of all potential matches with their positions
      const matches: Array<{
        index: number;
        length: number;
        term: string;
        definition: string;
        originalTerm: string;
      }> = [];

      // Find all matches using proper word boundary regex
      for (const [key, termInfo] of termMap.entries()) {
        // Escape special regex characters in the term
        const escapedKey = key.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        // Create regex with word boundaries to ensure exact word matching
        const regex = new RegExp(`\\b(${escapedKey})\\b`, 'gi');

        let match;
        while ((match = regex.exec(text)) !== null) {
          matches.push({
            index: match.index,
            length: match[0].length,
            term: match[0], // Use the actual matched text (preserves case)
            definition: termInfo.definition,
            originalTerm: key
          });
        }
      }

      // Sort matches by position to process them in order
      matches.sort((a, b) => a.index - b.index);

      // Remove overlapping matches (keep the first one found)
      const filteredMatches = [];
      for (const match of matches) {
        const hasOverlap = filteredMatches.some(existing =>
          (match.index < existing.index + existing.length) &&
          (match.index + match.length > existing.index)
        );
        if (!hasOverlap) {
          filteredMatches.push(match);
        }
      }

      // Process each match
      for (const match of filteredMatches) {
        // Add text before the match
        if (match.index > lastIndex) {
          fragment.appendChild(
            document.createTextNode(text.substring(lastIndex, match.index))
          );
        }

        // Add the term as a span with tooltip
        const span = document.createElement('span');
        span.className = 'glossary-term';
        span.style.borderBottom = '1px dotted #9ca3af';
        span.style.cursor = 'help';
        span.textContent = match.term;

        // Add event listeners
        span.addEventListener('mouseenter', (e) => {
          showTooltip(span, match.definition);
        });

        span.addEventListener('mouseleave', hideTooltip);

        fragment.appendChild(span);
        lastIndex = match.index + match.length;
      }

      // Add any remaining text
      if (lastIndex < text.length) {
        fragment.appendChild(document.createTextNode(text.substring(lastIndex)));
      }

      // Only replace if we found matches
      if (filteredMatches.length > 0) {
        parent.replaceChild(fragment, node);
        return true;
      }

      return false;
    };

    // Function to process the entire document
    const processDocument = () => {
      // Use requestIdleCallback to avoid blocking the main thread
      const processBatch = (deadline: IdleDeadline) => {
        let nodesProcessed = 0;
        const walker = document.createTreeWalker(
          document.body,
          NodeFilter.SHOW_TEXT,
          {
            acceptNode: (node) => {
              if (!node.textContent || node.textContent.trim().length < 3 ||
                  node.parentElement?.hasAttribute('data-glossary-processed')) {
                return NodeFilter.FILTER_REJECT;
              }
              // Check if any parent has data-no-glossary attribute
              let currentElement = node.parentElement;
              while (currentElement) {
                if (currentElement.hasAttribute('data-no-glossary')) {
                  return NodeFilter.FILTER_REJECT; // Skip processing this node and its children
                }
                currentElement = currentElement.parentElement;
              }
              return NodeFilter.FILTER_ACCEPT;
            }
          }
        );

        let node;
        while ((node = walker.nextNode()) && nodesProcessed < 100 && deadline.timeRemaining() > 0) {
          if (processTextNode(node as Text)) {
            nodesProcessed++;
          }
        }

        if (node) {
          // If there are more nodes to process, schedule another batch
          requestIdleCallback(processBatch);
        }
      };

      // Start processing
      requestIdleCallback(processBatch);
    };

    // Initial processing
    const processTimeout = setTimeout(processDocument, 500);

    // Set up a MutationObserver to process new content
    const observer = new MutationObserver((mutations) => {
      // Only process if we're not already processing
      if (document.body.querySelector('[data-processing-glossary]')) return;

      document.body.setAttribute('data-processing-glossary', 'true');

      // Use requestIdleCallback to batch DOM updates
      requestIdleCallback(() => {
        let shouldProcess = false;

        for (const mutation of mutations) {
          if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
            shouldProcess = true;
            break;
          }
        }

        if (shouldProcess) {
          processDocument();
        }

        document.body.removeAttribute('data-processing-glossary');
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    // Clean up on unmount
    return () => {
      clearTimeout(processTimeout);
      observer.disconnect();
      if (tooltipContainer && tooltipContainer.parentNode) {
        tooltipContainer.parentNode.removeChild(tooltipContainer);
      }
    };
  }, [terms, isLoading, shouldSkip]);

  return null;
}