Summarization is (Almost) Dead

Wangxuan Institute of Computer Technology, <NAME_EMAIL> {gao<PERSON><PERSON>, wanx<PERSON><PERSON><PERSON>}@pku.edu.cn

, <PERSON><PERSON>

proved evaluation methods.

2.1 Datasets

cutoff date.

2 Experimental Settings

dataset consists of 50 samples.

In this section, we provide an overview of the datasets and models used for human evaluation, as well as the experimental process and details.

To ensure that the large language model has not "seen" the data during training, we use the latest data to build the datasets specifically for human evaluation in each summarization task.1 Each

In conducting single-news, multi-news, and dialogue summarization tasks, our methodology emulates the dataset construction approaches utilized by CNN/DailyMail (<PERSON> et al., 2017; <PERSON>

1According to OpenAI's disclosure, the training data for GPT-3, GPT-3.5, and GPT-4 are cut off until September 2021. Therefore, our datasets are sourced from a time point after that

Our quantitative and qualitative comparisons between LLM-generated summaries, human-written summaries, and summaries generated by fine-tuned models revealed that LLM summaries are significantly preferred by the human evaluators, which also demonstrate higher factuality.

Given the impressive performance of LLMs across these tasks, we question the need for further refinement of text summarization models with higher metric scores. After sampling and examining 100 summarization-related papers published in ACL, EMNLP, NAACL, and COLING in the past 3 years, we find that the main contribution of about 70% papers was to propose a summarization approach and validate its effectiveness on standard datasets. As such, we provocatively assert that " Summarization is (almost) Dead." Nonetheless, we acknowledge existing challenges in the field such as the need for high-quality reference datasets, application-oriented approaches, and im-

, Mingqi Gao∗

Xiao Pu∗

Abstract How well can large language models (LLMs) generate summaries? We develop new datasets and conduct human evaluation experiments to evaluate the zero-shot generation capability of LLMs across five distinct summarization tasks. Our findings indicate a clear preference among human evaluators for LLM-generated summaries over human-written summaries and summaries generated by fine-tuned models. Specifically, LLM-generated summaries exhibit better factual consistency and fewer instances of extrinsic hallucinations. Due to the satisfactory performance of LLMs in summarization tasks (even surpassing the benchmark of reference summaries), we believe that most conventional works in the field of text summarization are no longer necessary in the era of LLMs. However, we recognize that there are still some directions worth exploring, such as the creation of novel datasets with higher quality and more reliable evaluation methods.

Text summarization, a natural language generation (NLG) task, aims to compress extensive source materials into brief summaries, including diverse content such as news articles, source codes, and cross-lingual text. Traditional methods used finetuning techniques on specific datasets (Lewis et al., 2019; Raffel et al., 2020; Zhang et al., 2020; Wang et al., 2021; Xue et al., 2021; Wang et al., 2022), but the emergence of large language models (LLMs) (Chung et al., 2022; Zhang et al., 2022; Touvron et al., 2023; Ouyang et al., 2022; OpenAI, 2023) has shifted the focus to their promising zero-shot

In an exploration of this potential, we evaluated the performance of LLMs on various summarization tasks—single-news, multi-news, dialogue, source codes, and cross-lingual summarization—using human-generated evaluation datasets.

1 Introduction

arXiv:2309.09558v1 [cs.CL] 18 Sep 2023

generation capability.

*Equal contribution.

Figure 1: Pairwise winning rates (%) between different systems across 5 tasks. Each data point represents the proportion of times System M (horizontal axis) is preferred over System N (vertical axis) in the comparisons. Red indicates a winning rate greater than 50%, indicating a preference for System M, while blue represents a winning rate less than 50%, indicating a preference for System N, with darker colors indicating the greater difference in

3

, and each annotator is assigned to complete all 50 questions of a single task. For each question, they are presented with a source article and summaries from all summarization systems selected in this task. They are then asked to compare the summaries pairwise. If there are a total of n systems in the task, each annotator will need to make C

parisons for one question. We calculate the overall Cohen's kappa coefficient (Cohen, 1968) and find that the inter-annotator agreement is acceptable,

3.1 Experiment 1: Comparing the overall

In this experiment, we engage human evaluators to compare the overall quality of different summaries.

the proportion of times system M is preferred by human evaluators when comparing it with system N. By comparing the pairwise winning rates among different systems, we can gain insights into the relative overall quality of the systems. Surprisingly, as depicted in Figure 1, summaries generated by the LLMs consistently outperform both human and summaries generated by fine-tuned models

This raises the question of why LLMs are able to outperform human-written summaries, which are traditionally regarded as flawless and oracle. Moreover, it prompts us to examine the specific limitations of human-written references. Our ini-

3We recruit graduate students with a good command of English to participate in our experiments. For the code summarization task, we hire two students majoring in computer science who are familiar with the Go programming language.

with a coefficient of 0.558.

3 Experiment Results

quality of summaries

Then we compute WinRateN

across all tasks.

2 n com-

M, which represents

winning rates between the two systems.We find that LLMs are highly preferred by human evaluators.

et al., 2015), Multi-News (Fabbri et al., 2019), and Mediasum (Zhu et al., 2021), respectively, to build the datasets for our experiments. The sources for the data remain consistent with the original datasets, such as the DailyMail website, yet are the latest. For the cross-lingual summarization task, our strategy aligns with Zhu et al. (2019) approach, which entails translating reference summaries in our single-news dataset from English to Chinese using Google Translate, followed by a post-editing

In relation to code summarization, we

adopt Bahrami et al. (2021)'s methodology to formulate a dataset, the source documents of which

For each task, we choose GPT-3(text-davinci-003) (Brown et al., 2020; Ouyang et al., 2022), GPT-3.5 and GPT-4 (OpenAI, 2023) as representatives of LLMs. For each summarization task, we additionally utilize 1-2 smaller models, previously fine-tuned on a dataset in this specific task. Precisely, we employ BART (Lewis et al., 2019) and T5 (Raffel et al., 2020) for single-news task, Pegasus (Zhang et al., 2020) and BART for multi-news task, T5 and BART for Dialogue task, MT5 (Xue et al., 2021) and MBART (Wang et al., 2022) for cross-lingual task and Codet5 (Wang et al., 2021)

process. 2

2.2 Models

for source code task.

2

2.3 Experimental process and details

We conduct human evaluation experiments to annotate the different types of summaries for each of five tasks. For each task, we hire two annotators

Post-editing is done by a graduate student in linguistics.

are Go language programs.

System Singlenews

Multinews

GPT-4 8 5 16 5 9 Human 13 62 15 15 46

Table 1: The number of hallucinations (sentence-level) found in GPT-4 and human-written summaries. We highlight the figures which is significantly large.

tial observations suggest that LLM-generated summaries exhibit high fluency and coherence. However, the comparative factual consistency between LLM summaries and human-written ones remains uncertain. Consequently, our next experiment focuses on exploring the factual consistency aspect.

3.2 Experiment 2: Comparing the factual

We further recruit annotators to identify sentencelevel hallucinations in the human- and LLMgenerated summaries, allowing us to compare their levels of factual consistency. Given the significant cost of annotation, we select GPT-4 as a representative LLM. As depicted in Table 1, humanwritten reference summaries exhibit either an equal or higher number of hallunications compared to GPT-4 summaries. In specific tasks such as multinews and code summarization, human-written summaries exhibit notably inferior factual consistency. To gain a deeper understanding of this observed phenomenon, we further investigate the types of these factual errors. Following Maynez et al. (2020) we divide all hallucinations into two categories: intrinsic and extrinsic hallucinations. Intrinsic hallucinations refer to inconsistencies between the factual information in the summary and the source text, while extrinsic hallucinations occur when the summary includes certain factual information that

consistency of summaries

is not present in the source text.

By analyzing the proportion of intrinsic and extrinsic hallucinations in both human-written and GPT-4 summaries, as shown in Table 2, we discover a notably higher occurrence of extrinsic hallucinations in tasks where human-written summaries demonstrate poor factual consistency, e.g., multi-news and code. Howerver, where there is little difference in factual consistency between human and GPT-4, the proportions of extrinsic hallucinations in both systems are similar. Therefore, we hypothesize that extrinsic hallucinations are the underlying cause for the inadequate factual consistency observed in human-

Cross-

lingual Dialogue Code

System Avg Single-

written summaries.

news

GPT-4 and human-written summaries.

3.3 Comparative Analysis

the presence of hallucinations.

topics when generating summaries.

4 The Changing Landscape of

Multinews

GPT-4 40% 50% 40% 38% 40% 33% Human 62% 62% 73% 33% 53% 89%

Table 2: The proportion of extrinsic hallucinations in

Here we delve into an analysis of the specific strengths exhibited by LLM summaries in comparison to both human and fintuned summaries. We have included some concrete examples and a more

Reference summaries vs. LLM summaries Compared to LLM summaries, we identify a specific issue with human-written reference summaries, their lack of fluency. As shown in Figure 2(a), human-written reference summaries are sometimes flawed with incomplete information. Another issue observed in some human-written reference summaries, as concluded in the previous chapter's quantitative analysis and shown in Figure 2(b) , is

Summaries generated by fine-tuned models vs. LLM summaries In comparison to LLM summaries, we find that summaries generated by fine-tuned models tend to have a fixed and rigid length, whereas LLMs are able to adjust the output length according to the input's information volume. Additionally, when the input contains multiple topics, the summaries generated by fine-tuned models demonstrate lower coverage of these topics, as exampled in Figure 3, while LLMs can capture all the

Summarization: Seeking New Horizons

Through the aforementioned manual evaluation, we have discovered that the quality of summaries generated by LLMs surpasses that of the reference summaries in many datasets. It is foreseeable that with the continuous improvement of future LLMs, their summarization capabilities will further enhance. Previous summarization methods were often tailored to specific categories, domains, or languages, resulting in limited generality, and their significance is gradually diminishing. As mentioned in the introduction, nearly 70% of the research is no

detailed version of analysis in Appendix B.

Cross-

lingual Dialogue Code

longer meaningful. However, we believe that the

(Kocmi and Federmann, 2023; Wang et al., 2023a; Liu et al., 2023; Luo et al., 2023; Gao et al., 2023). Furthermore, a shift in emphasis is necessary in the evaluation of summarization, with greater consideration given to the practical utility and applications

Extrinsic Evaluation (Pu et al., 2023): Measuring the effectiveness of a summary by using it as an input to another task (e.g., question-answering or decision-making tasks), to see if essential infor-

Evaluating the ability of LLMs on summarization. Goyal et al. (2023) show that news summaries generated by GPT-3 are overwhelmingly preferred by humans compared with those generated by fine-tuned models. Further, Zhang et al. (2023) find that news summaries generated by LLMs are evaluated to be on par with humanwritten summaries. Some studies have also explored the performance of LLMs such as ChatGPT with automatic evaluations on aspect-based summarization (Yang et al., 2023) and cross-lingual summarization (Wang et al., 2023b). Our work involves GPT-3.5 and GPT-4 and conducts human evaluations on various data on which they have not

Through the development of new evaluation datasets and the conduction of comprehensive human evluation experiments that cover a wide range of summarization scenarios, our study demonstrates the remarkable performance of LLMgenerated summaries compared to human-written reference summaries and summaries generated by fine-tuned models across diverse summarization tasks. LLM summaries exhibit superior fluency, factuality, and flexibility, especially in specialized and uncommon summarization scenarios. Our findings indicate that text summarization is undergoing significant transformation, rendering previous approaches less meaningful in light of the advancements in LLMs. We also offer an outlook on the tasks worth exploring in the field of text summarization in the future, focusing on three aspects: datasets, methods, and evaluation. We hope that this can bring inspiration to relevant researchers

of the generated summaries.

mation has been retained.

5 Related Work

been trained.

6 Conclusion

regarding their future work.

The role of the dataset shifts from model training to testing, necessitating higher-quality reference summaries. Previously generated datasets will gradually be phased out, and future reference summaries

The majority of current summarization datasets are in English and focused on news articles, scientific articles, or Wikipedia (Foundation; Merity et al., 2016). And the source documents are relatively short. In order to thoroughly assess the summarization capabilities of LLMs, it becomes imperative to incorporate other diverse genres of data, as well as other languages, especially those that are low-resource in nature. Additionally, there is a need to include longer documents, such as books, within the datasets to facilitate comprehen-

following directions are worth exploring:

will require human expert annotations.

4.2 Summarization Approaches

alizing the summarization process.

4.3 Summarization Evaluation

with the assistance of LLMs.

Further investigation is warranted in the realm of application-oriented summarization approaches,

Customized Summarization (Zhong et al., 2022): LLMs can be customized to generate summaries that align with individual user preferences, reading history, or expertise level, thereby person-

Real-time Summarization (Yang et al., 2022): The ability to condense information in real time plays a vital role in various contexts, such as live steams, stock market fluctuations, or social media monitoring. Research efforts in this domain could concentrate on enhancing the promptness and effi-

Interactive Summarization (Shapira et al., 2017): The development of models capable of interacting with users, soliciting clarification or feedback throughout the summarization process, holds promise for augmenting the accuracy and relevance

It is imperative to bid farewell to antiquated assessment metrics such as ROUGE (Lin, 2004), as they no longer align with the evolving landscape of summarization. Future automated evaluation techniques for summarization hold promise in their reliance on LLMs as demonstrated by recent studies

4.1 Summarization Datasets

sive evaluation.

ciency of LLMs.

of summaries.

Limitations

avenue for future research.

only for research purposes.

References

language models.

Ethics Statement

We do not include other popular LLMs like LLaMA and Vacuna because these newer models do not disclose the cutoff date of their training data. This lack of information makes it challenging for us to create a novel dataset specifically tailored for evaluating the zero-shot generation of summaries by LLMs. In the future, if these LLMs provide more details about their training data, it would be beneficial to incorporate additional LLMs and compare the differences among different series of such models. Due to the high cost, we only conduct human experiments on five common text summarization tasks. However, exploring some less common and more challenging summarization tasks, e.g. slides summarization, to test the capability of LLMs in the field text summarization would be an interesting

Mirac Suzgun, Xinyun Chen, Aakanksha Chowdhery, Alex Castro-Ros, Marie Pellat, Kevin Robinson, Dasha Valter, Sharan Narang, Gaurav Mishra, Adams Yu, Vincent Zhao, Yanping Huang, Andrew Dai, Hongkun Yu, Slav Petrov, Ed H. Chi, Jeff Dean, Jacob Devlin, Adam Roberts, Denny Zhou, Quoc V. Le, and Jason Wei. 2022. Scaling instruction-finetuned

Jacob Cohen. 1968. Weighted kappa: nominal scale agreement provision for scaled disagreement or partial credit. *Psychological bulletin*, 70(4):213.

Alexander Fabbri, Irene Li, Tianwei She, Suyi Li, and Dragomir Radev. 2019. Multi-news: A large-scale multi-document summarization dataset and abstractive hierarchical model. In *Proceedings of the 57th Annual Meeting of the Association for Computational Linguistics*, pages 1074–1084, Florence, Italy. Asso-

ciation for Computational Linguistics.

*search Repository*, arXiv:2304.02554.

arXiv:2209.12356. Version 2.

*processing systems*, 28.

arXiv:2302.14520.

arXiv:2303.16634.

*Repository*, arXiv:2303.15621.

Wikimedia Foundation. Wikimedia downloads.

Mingqi Gao, Jie Ruan, Renliang Sun, Xunjian Yin, Shiping Yang, and Xiaojun Wan. 2023. Human-like summarization evaluation with chatgpt. *Computing Re-*

Tanya Goyal, Junyi Jessy Li, and Greg Durrett. 2023. News summarization and evaluation in the era of gpt-3. *Computing Research Repository*,

Karl Moritz Hermann, Tomas Kocisky, Edward Grefenstette, Lasse Espeholt, Will Kay, Mustafa Suleyman, and Phil Blunsom. 2015. Teaching machines to read and comprehend. *Advances in neural information*

Tom Kocmi and Christian Federmann. 2023. Large language models are state-of-the-art evaluators of translation quality. *Computing Research Repository*,

Mike Lewis, Yinhan Liu, Naman Goyal, Marjan Ghazvininejad, Abdelrahman Mohamed, Omer Levy, Ves Stoyanov, and Luke Zettlemoyer. 2019. Bart: Denoising sequence-to-sequence pre-training for natural language generation, translation, and comprehension.

Chin-Yew Lin. 2004. ROUGE: A package for automatic evaluation of summaries. In *Text Summarization Branches Out*, pages 74–81, Barcelona, Spain.

Yang Liu, Dan Iter, Yichong Xu, Shuohang Wang, Ruochen Xu, and Chenguang Zhu. 2023. Gpteval: Nlg evaluation using gpt-4 with better human alignment. *Computing Research Repository*,

Zheheng Luo, Qianqian Xie, and Sophia Ananiadou. 2023. Chatgpt as a factual inconsistency evaluator for abstractive text summarization. *Computing Research*

Association for Computational Linguistics.

language models.

All annotators are paid 10 USD per hour, above the local minimum wage. During the process of constructing the dataset, we obtain text from publicly available websites, and it is possible that some of the texts may contain biased, offensive or violent elements. However, it is important to note that the inclusion of such content does not reflect our research standpoint or endorse any biased, offensive or violent views. Our intention is to use the dataset

Mehdi Bahrami, N. C. Shrikanth, Shade Ruangwan, Lei Liu, Yuji Mizobuchi, Masahiro Fukuyori, Wei-Peng Chen, Kazuki Munakata, and Tim Menzies. 2021. Pytorrent: A python library corpus for large-scale

Tom B. Brown, Benjamin Mann, Nick Ryder, Melanie Subbiah, Jared Kaplan, Prafulla Dhariwal, Arvind Neelakantan, Pranav Shyam, Girish Sastry, Amanda Askell, Sandhini Agarwal, Ariel Herbert-Voss, Gretchen Krueger, Tom Henighan, Rewon Child, Aditya Ramesh, Daniel M. Ziegler, Jeffrey Wu, Clemens Winter, Christopher Hesse, Mark Chen, Eric Sigler, Mateusz Litwin, Scott Gray, Benjamin Chess, Jack Clark, Christopher Berner, Sam McCandlish, Alec Radford, Ilya Sutskever, and Dario Amodei. 2020. Language models are few-shot learners.

Hyung Won Chung, Le Hou, Shayne Longpre, Barret Zoph, Yi Tay, William Fedus, Yunxuan Li, Xuezhi Wang, Mostafa Dehghani, Siddhartha Brahma, Albert Webson, Shixiang Shane Gu, Zhuyun Dai, Joshua Maynez, Shashi Narayan, Bernd Bohnet, and Ryan McDonald. 2020. On faithfulness and factuality in abstractive summarization. In *Proceedings of the 58th Annual Meeting of the Association for Computational Linguistics*, pages 1906–1919, Online. Association for Computational Linguistics.

Yiming Wang, Zhuosheng Zhang, and Rui Wang. 2023b. Element-aware summarization with large language models: Expert-aligned evaluation and chain-ofthought method. *Computing Research Repository*,

Yue Wang, Weishi Wang, Shafiq Joty, and Steven C.H. Hoi. 2021. Codet5: Identifier-aware unified pretrained encoder-decoder models for code understanding and generation. In *Proceedings of the 2021 Conference on Empirical Methods in Natural Language*

Linting Xue, Noah Constant, Adam Roberts, Mihir Kale, Rami Al-Rfou, Aditya Siddhant, Aditya Barua, and Colin Raffel. 2021. mt5: A massively multilingual

Saelyne Yang, Jisu Yim, Juho Kim, and Hijung Valentina Shin. 2022. Catchlive: Real-time summarization of live streams with stream content and interaction data. In *Proceedings of the 2022 CHI Conference on Human Factors in Computing Systems*, CHI '22, New York, NY, USA. Association

Xianjun Yang, Yan Li, Xinlu Zhang, Haifeng Chen, and Wei Cheng. 2023. Exploring the limits of chatgpt for query or aspect-based text summarization. *Computing Research Repository*, arXiv:2302.08081.

Jingqing Zhang, Yao Zhao, Mohammad Saleh, and Peter J. Liu. 2020. Pegasus: Pre-training with extracted gap-sentences for abstractive summarization.

Susan Zhang, Stephen Roller, Naman Goyal, Mikel Artetxe, Moya Chen, Shuohui Chen, Christopher Dewan, Mona Diab, Xian Li, Xi Victoria Lin, Todor Mihaylov, Myle Ott, Sam Shleifer, Kurt Shuster, Daniel Simig, Punit Singh Koura, Anjali Sridhar, Tianlu Wang, and Luke Zettlemoyer. 2022. Opt: Open pre-

Tianyi Zhang, Faisal Ladhak, Esin Durmus, Percy Liang, Kathleen McKeown, and Tatsunori B. Hashimoto. 2023. Benchmarking large language models for news summarization. *Computing Research Repository*,

Ming Zhong, Yang Liu, Suyu Ge, Yuning Mao, Yizhu Jiao, Xingxing Zhang, Yichong Xu, Chenguang Zhu, Michael Zeng, and Jiawei Han. 2022. Unsupervised multi-granularity summarization. In *Findings of the Association for Computational Linguistics: EMNLP 2022*, pages 4980–4995, Abu Dhabi, United Arab Emirates. Association for Computational Linguistics.

Chenguang Zhu, Yang Liu, Jie Mei, and Michael Zeng. 2021. Mediasum: A large-scale media interview dataset for dialogue summarization. *arXiv preprint*

Junnan Zhu, Qian Wang, Yining Wang, Yu Zhou, Jiajun Zhang, Shaonan Wang, and Chengqing Zong. 2019. Ncls: Neural cross-lingual summarization. *arXiv*

trained transformer language models.

arXiv:2301.13848.

*arXiv:2103.06410*.

*preprint arXiv:1909.00156*.

arXiv:2305.13412.

*Processing, EMNLP 2021*.

for Computing Machinery.

pre-trained text-to-text transformer.

Stephen Merity, Caiming Xiong, James Bradbury, and Richard Socher. 2016. Pointer sentinel mixture mod-

Long Ouyang, Jeffrey Wu, Xu Jiang, Diogo Almeida, Carroll Wainwright, Pamela Mishkin, Chong Zhang, Sandhini Agarwal, Katarina Slama, Alex Ray, et al. 2022. Training language models to follow instructions with human feedback. *Advances in Neural Information Processing Systems*, 35:27730–27744.

Xiao Pu, Mingqi Gao, and Xiaojun Wan. 2023. Is summary useful or not? an extrinsic human evaluation of text summaries on downstream tasks. *Computing*

Colin Raffel, Noam Shazeer, Adam Roberts, Katherine Lee, Sharan Narang, Michael Matena, Yanqi Zhou, Wei Li, and Peter J. Liu. 2020. Exploring the limits of transfer learning with a unified text-to-text trans-

Abigail See, Peter J. Liu, and Christopher D. Manning. 2017. Get to the point: Summarization with pointergenerator networks. In *Proceedings of the 55th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers)*, pages 1073– 1083, Vancouver, Canada. Association for Computa-

Ori Shapira, Hadar Ronen, Meni Adler, Yael Amsterdamer, Judit Bar-Ilan, and Ido Dagan. 2017. Interactive abstractive summarization for event news tweets. In *Proceedings of the 2017 Conference on Empirical Methods in Natural Language Processing: System Demonstrations*, pages 109–114, Copenhagen, Denmark. Association for Computational Linguistics.

Hugo Touvron, Thibaut Lavril, Gautier Izacard, Xavier Martinet, Marie-Anne Lachaux, Timothée Lacroix, Baptiste Rozière, Naman Goyal, Eric Hambro, Faisal Azhar, Aurelien Rodriguez, Armand Joulin, Edouard Grave, and Guillaume Lample. 2023. Llama: Open

Jiaan Wang, Yunlong Liang, Fandong Meng, Haoxiang Shi, Zhixu Li, Jinan Xu, Jianfeng Qu, and Jie Zhou. 2023a. Is chatgpt a good nlg evaluator? a preliminary study. *Computing Research Repository*,

Jiaan Wang, Fandong Meng, Ziyao Lu, Duo Zheng, Zhixu Li, Jianfeng Qu, and Jie Zhou. 2022. Clidsum: A benchmark dataset for cross-lingual dialogue

and efficient foundation language models.

*Research Repository*, arXiv:2305.15044.

OpenAI. 2023. Gpt-4 technical report.

els.

former.

tional Linguistics.

arXiv:2303.04048.

summarization.

A Case Studies

Compared to LLM, the weaknesses of humanwritten reference summaries lie in incomplete information and hallucinations. As shown in Figure 2(a), human-written reference summaries are sometimes flawed with incomplete information, where we are only informed about the event without knowledge of the involved participants. In addition, some references lack fluency resulting in decreased readability. Another issue observed in some human-written reference summaries, as concluded in the previous chapter's quantitative analysis, is the presence of factual errors or hallucination. For instance, source text of Figure 2(b) only mentioned two incidents occurring within several months, while the human-written reference summary incorrectly stated "three months."

Compared to LLM summaries, the drawbacks of fine-tuned summaries lie in their inability to effectively capture all the topics mentioned in a long article. They tend to focus on only a small sub-set of the topics. As shown in Figure 3, the source text discusses four news events sequentially, while the fine-tuned BART summaries only covers the information of the first news event. In contrast, GPT-4 successfully summarizes all four

Apart from pairwise comparisons of the performance of different systems, we also introduce human preference score to indicate the relative quality of a system in each task. It equals to the overall winning rate of the system in comparisons with any other systems, based on annotators' judgements. As shown in Figure 4, the human preference scores for the large models exceed 50%, indicating a strong preference for their summaries and highlighting the capability of LLMs in text summa-

news events within a similar length.

B Human preference score

rization.

Figure 2: A case study comparing human-written reference and GPT-4 summaries, we have color-coded the issues in the human summaries for better clarity: orange for incomplete information, blue for broken sentences, and green

Figure 3: A case study comparing fine-tuned and GPT-4 summaries. The dialogue is a transcript of a news program where a total of four topics are discussed. For the source text, we annotate the transitions between dialogue topics in

for hallucinations.

red.

Figure 4: Human preference scores for different summary systems across 5 tasks. LLMs are highly preferred by human evaluators, as demonstrated by the higher scores assigned to LLM-generated summaries compared to other

systems.

