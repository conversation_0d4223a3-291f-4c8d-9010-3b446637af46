Graph of Thoughts: Solving Elaborate Problems with Large Language Models

1ETH Zurich, 2Cledar, 3Warsaw University <NAME_EMAIL>, <EMAIL>, <EMAIL>

, <PERSON><PERSON>

, <PERSON><PERSON><PERSON>

, <PERSON>1

ing the rigid tree structure on the thought process.

are not naturally expressible with CoT or ToT.

We observe that these (and many other) thought transformations can be naturally enabled when *modeling a reasoning process of an LLM as a graph*. For this, we propose Graph of Thoughts (GoT), an approach that *enhances LLMs' capabilities through networked reasoning* (contribution #1). In GoT, an LLM thought is modeled as a vertex, while an edge is a dependency between such thoughts. Using GoT, one can aggregate arbitrary thoughts by constructing vertices that have more than one incoming edge. Overall, the graph abstraction harnessed by GoT seamlessly generalizes CoT and ToT to more complex thought patterns, *without resorting to any model updates*. Yet, putting GoT to practice requires solving several design challenges. For example, what is the best graph structure for different tasks? How to best aggregate thoughts to maximize accuracy and minimize cost? To answer these and

, <PERSON><PERSON>

, <PERSON><PERSON><PERSON>3

CoT, Self-Consistency with CoT (CoT-SC) [66], is a scheme where multiple CoTs are generated, and then the best one is selected as the outcome. More recently, CoT and CoT-SC were extended with Tree of Thoughts (ToT) [43, 76, 74], which models the LLM reasoning process with a tree. This facilitates using different paths of thoughts, and offers novel capabilities such as backtracking from non-promising outcomes. Unfortunately, the ToT approaches still fundamentally limit the reasoning abilities within a prompt by impos-

In this work, we argue that fundamentally more powerful prompting can be achieved by enabling LLM thoughts to form an arbitrary graph structure. This is motivated by numerous phenomena such as human reasoning, brain structure, or algorithmic execution. When working on a novel idea, a human would not only follow a chain of thoughts (as in CoT) or try different separate ones (as in ToT), but would actually form a more complex network of thoughts. For example, one could explore a certain chain of reasoning, backtrack and start a new one, then realize that a certain idea from the previous chain could be combined with the currently explored one, and merge them both into a new solution, taking advantage of their strengths and eliminating their weaknesses. Similarly, brains form complex networks, with graph-like patterns such as recurrence [28]. Executing algorithms also expose networked patterns, often represented by Directed Acyclic Graphs. The corresponding *graph-enabled transformations* bring a promise of more powerful prompting when applied to LLM thoughts, but they

,

,

Maciej Besta1*, Nils Blach1*, Ales Kubicek1

Hubert Niewiadomski2

, Joanna Gajda2

Lukas Gianinazzi1

of which form complex networks.

arXiv:2308.09687v2 [cs.CL] 21 Aug 2023

commonsense, or symbolic reasoning.

*Equal contribution

Abstract We introduce Graph of Thoughts (GoT): a framework that advances prompting capabilities in large language models (LLMs) beyond those offered by paradigms such as Chain-of-Thought or Tree of Thoughts (ToT). The key idea and primary advantage of GoT is the ability to model the information generated by an LLM as an *arbitrary graph*, where units of information ("LLM thoughts") are vertices, and edges correspond to dependencies between these vertices. This approach enables combining arbitrary LLM thoughts into synergistic outcomes, distilling the essence of whole networks of thoughts, or enhancing thoughts using feedback loops. We illustrate that GoT offers advantages over state of the art on different tasks, for example increasing the quality of sorting by 62% over ToT, while simultaneously reducing costs by >31%. We ensure that GoT is extensible with new thought transformations and thus can be used to spearhead new prompting schemes. This work brings the LLM reasoning closer to human thinking or brain mechanisms such as recurrence, both

Website & code: https://github.com/spcl/graph-of-thoughts

1 Introduction Large language models (LLMs) are taking over the world of AI. Recent years saw a rapid development of models primarily based on the decoder-only Transformer variant [64], such as GPT [52, 51, 14, 13], PaLM [19], or LLaMA [62]. *Prompt engineering* is a resource-efficient approach for solving different LLM tasks. In brief, one includes the task description within the input sent to an LLM. If this description is appropriately formulated, the LLM solves the task using its autoregressive token-based mechanism for generating text. Such prompts may contain example tasks with solutions (few-shot prompting, also referred to as in-context learning (ICL)), or even no example tasks at all (zero-shot prompting). Recent years shown that this mechanism can be used to solve a broad set of tasks that involve mathematical,

Chain-of-Thought (CoT) [70] is an approach for prompting, in which one includes the intermediate steps of reasoning within the prompt (intermediate "thoughts"), besides the task input/output. CoT was shown to significantly improve the capability of LLMs to solve problems without resorting to any model updates. One major improvement over many other questions, we carefully design a modular architecture for implementing GoT (contribution #2), coming with two design highlights. First, we enable a *fine-grained control over individual thoughts*. This enables us to fully control the ongoing conversation with the LLM, and apply advanced thought transformations, such as combining most promising thoughts from the ongoing reasoning into a new one. Second, we ensure that our architecture can be seamlessly extended with novel thought transformations, patterns of reasoning (i.e., graphs of thoughts), and LLM models. This enables rapid prototyping of novel prompting ideas using GoT, while experimenting with different models such as 2 Background & Notation

We first outline background concepts and notation.

timization), and so on.

intermediate thoughts.

ing [24].

backtracking.

BFS or DFS).

2.1 Language Models & In-Context Learning The conversation with the LLM consists of user messages (*prompts*) and LLM replies (*thoughts*). We follow the established notation [76] and we denote a pre-trained language model (LM) with parameters θ as pθ. Lowercase letters such as x, y, z, ... indicate LLM thoughts. We purposefully do not prescribe what is a single "thought", and instead make it usecase specific. Hence, a single thought can be a paragraph (e.g., in article summary), a document (e.g., in document generation), a block of code (e.g., in code debugging or op-

We next describe specific prompting approaches.

Input-Output (IO) The Input-Output (IO) prompting is a straightforward approach, in which we use an LLM to turn an input sequence x into the output y *directly*, without any

Chain-of-Thought (CoT) Second, in Chain-of-Thought (CoT), one introduces intermediate thoughts a1, a2, ... between x and y. This strategy was shown to significantly enhance various LM tasks over the plain IO baseline, such as mathematical puzzles [70] or general mathematical reason-

Multiple CoTs Third, one can generalize CoT into *multiple CoTs* by generating *several* (independent) k CoTs, and returning the one with the best output (according to some prescribed scoring metric). It was introduced by Wang et al. in the scheme called Self-Consistency with CoT (CoT-SC) [66]. This approach enhances CoT because it offers an opportunity to explore different reasoning paths. However, it does not offer "local exploration" within a path, such as

Tree of Thoughts (ToT) Finally, the Tree of Thoughtd (ToT) scheme was introduced independently by Yao [76] and Long [43] (where it is referred to as Tree-of-Thought); it was used implicitly to a certain degree by other schemes such as thought decomposition [74]. It enhances CoT-SC by modeling the process or reasoning as a *tree* of thoughts. A single tree node represents a partial solution. Based on a given node, the *thought generator* constructs a given number k of new nodes. Then, the *state evaluator* generates scores for each such new node. Depending on the use case, the evaluation could be conducted using an LLM itself, or it can harness human scores. Finally, the schedule of extending the tree is dictated by the utilized search algorithm (for example

3 The GoT Framework We now detail the GoT framework. We present it in Figure 1,

Formally, GoT can be modeled as a tuple (G, T , E, R), where G is the "LLM reasoning process" (i.e., all the LLM thoughts within the context, with their relationships), T are

and compare it to other prompting strategies.

We illustrate several use cases for GoT (sorting, keyword counting for summaries, set operations, document merging) and we detail how to implement them using the graph-based paradigm (contribution #3). We evaluate GoT and show its advantages over the state of the art (contribution #4). Overall, we observe that GoT is particularly well-suited for tasks that can be naturally decomposed into smaller subtasks that are solved individually and then merged for a final solution. Here, GoT outperforms other schemes, for example improving upon CoT and ToT by, respectively, ≈70% and ≈62%, in terms of the quality of sorting, while *simultaneously* re-

We qualitatively compare GoT to other prompting schemes in Table 1. GoT is the only one to enable arbitrary graph-based thought transformations within a prompt, such as aggregation, embracing all previously proposed schemes.

Scheme Sc? Mc? Tr? Ag? Chain-of-Thought (CoT) [70] é é é Self-Consistency with CoT [66] é é Thought decomposition [74] é Tree-of-Thought (ToT) [43] é Tree of Thoughts (ToT) [76] é Graph of Thoughts (GoT) Table 1: Comparison of prompting schemes, with respect to the supported transformations of thoughts. "Sc?": single chain of thoughts? "Mc?": multiple chains of thoughts? "Tr?": tree of thoughts? "Ag?": arbitrary graph of thoughts? "": full support, "": partial support, "é": no support. Note that we do not include a recent scheme called Graph-of-Thought [78] because it is not a prompting scheme. While its name suggests close connections to ToT and CoT, as a fine-tuning scheme, it resorts to model up-

dates, and is thus outside the focus of this work.

damentally larger volumes than other schemes.

Finally, we propose a new metric for evaluating a prompting strategy, the *volume of a thought* (contribution #5). With this metric, we aim to understand better the differences between prompting schemes. For a given thought v, the volume of v is *the number of LLM thoughts, from which one can reach* v *using directed edges*. Intuitively, these are all the LLM thoughts that have had the potential to contribute to v. We show that GoT, by incorporating thought transformations such as aggregation, enables thoughts to have fun-

GPT-3.5, GPT-4, or Llama-2 [63].

ducing costs by >31% over ToT.

Input

**Basic Input-Output (IO)**

Input

Input

Branching out from a chain

**Key novelty (beyond CoT-SC):** Generating several new thoughts based on a given arbitrary thought, exploring it further, and possibly backtracking from it

Figure 1: Comparison of Graph of Thoughts (GoT) to other prompting strategies.

**Aggregation**

**Generation**

V

A vertex models a thought. An edge models dependency

transformations.

′ = (V ∪ V

the sets V

**Tree of Thoughts (ToT) Graph of Thoughts (GoT)**

Intermediate thoughts are also scored

Backtracking from a chain

Output

...

Article 2

Keyword summary

Combining articles into a coherent summary ...

> Article 1

Generating summaries from an article, to maximize quality

Article 3

Keyword summary 2

Article 1

Keyword summary 1

> ′ , E′

− and E′ = (E ∪ E+) \ E−. V

), where

− and E−, re-

+

Aggregating thoughts

[This work]

Input

**Key novelty (beyond ToT):** Arbitrary graph-based thought transformations (aggregating thoughts into a new one, looping over a thought to

Aggregating chains

refine it)

... **Graph theory view Example sorting task Example writing task**

... **1 2 7 8 2 3 6 7 1 1 4 5**

**1 1 1 2 2 3 4 5 6 7 7 8**

into a sorted array of numbers

**1 4 6 2 4 2 4 9 8 7 5 4**

**1 4 6 2 4 2 4 9 8 7 5 4**

Splitting an unsorted array into subarrays, for subsequent sorting

Figure 2: Examples of aggregation and generation thought

rays of numbers into a final sorted array. We illustrate exam-

and E+ are new vertices and edges inserted into G to model the new thoughts and their dependencies, respectively. To maximize the expressiveness of GoT – we also enable the user to explicitly *remove* thoughts, by specifying the corre-

spectively). Here, it is the user's responsibility to ensure that

formations (i.e., for example, that the user does not attempt to remove a vertex that does not exist). This enables seamless incorporation of schemes where, in order to save space within the context, one can remove parts of reasoning that

+, E+, V −, and E− come with consistent trans-

Formally, each such transformation can be modeled as T (G, pθ) where G = (V, E) is the graph reflecting the current state of the reasoning, and pθ is the used LLM. T modifies G usually by adding new vertices and their incom-

ples of aggregation and generation in Figure 2.

ing edges. We have G′ = T (G, pθ) = (V

sponding vertices and edges to be removed (V

+) \ V

do not promise improvements.

... ... Merging sorted subarrays

Backtracking

Refining

score Output

Selecting a chain with the best score

Output Output

the potential thought transformations, E is an evaluator function used to obtain scores of thoughts, and R is a ranking

We model the reasoning process as a directed graph G = (V, E); V is a set of vertices and E ⊆ V × V is a set of edges. G is directed and thus the edges are a subset of ordered vertex pairs E ⊆ V × V . A vertex contains a *solution* to a problem at-hand (be it an initial, intermediate, or a final one). The concrete form of such a thought depends on a use case; it could be a paragraph (in writing tasks) or a sequence of numbers (in sorting). A directed edge (t1, t2) indicates that thought t2 has been constructed using t1 as "direct input", i.e., by explicitly instructing the LLM to use

In certain use cases, graph nodes belong to different *classes*. For example, in writing tasks, some vertices model *plans of writing a paragraph*, while other vertices model *the actual paragraphs of text*. In such cases, GoT embraces a heterogeneous graph G = (V, E, c) to model the LLM reasoning, where c maps vertices V into their respective classes C (in the above case, it would be C = {plan, par}). Hence, any vertex v can model different aspects of reasoning.

We associate G with the LLM reasoning process. To advance this process, one applies thought transformations to G. An example such transformation is to merge best-scoring (so far) thoughts into a new one. Another example is to loop over a thought, in order to enhance it. Note that these transformations strictly extend the set of transformations avail-

GoT enables novel transformations of thoughts thanks to the graph-based model for reasoning. We refer to them as graph-enabled transformations. For example, in writing, one could combine several input articles into one coherent summary. In sorting, one could merge several sorted subar-

function used to select most relevant thoughts.

3.1 Reasoning Process

t1 for generating t2.

able in the CoT, CoT-SC, or ToT.

3.2 Transformations of Thoughts

Abandon a chain **Key novelty (beyond CoT):** Harnessing multiple independent chains of thoughts

**Chain-of- Multiple CoTs (CoT-SC)**

**-Thought (CoT)**

Input

**Key novelty:** Intermediate LLM thoughts within a chain

Output

Negative

Dependencies between thoughts Abandon thought Backtrack

Positive score

Thoughts: Unscored

**Legend**

The specific form of T and how it impacts G depends on a specific transformation. We first detail the primary graphenabled thought transformations, and then proceed to describe how GoT embraces the transformations from the ear-

Aggregation Transformations First, with GoT, one can aggregate arbitrary thoughts into new ones, to combine and reinforce the advantages of these thoughts, while eliminating their disadvantages. In the basic form, in which

{(v1, v+), ...,(vk, v+)}, where v1, ..., vk are the merged k thoughts. More generally, this enables aggregating reasoning paths, i.e., longer chains of thoughts, beyond just individual thoughts. With the graph model, is it simply achieved by adding outgoing edges from the vertices v1, ..., vk modeling final thoughts in several chains, into a single thought

Refining Transformations Another thought transformation is the refining of a current thought v by modifying its

graph indicates an iterated thought with the same connec-

Generation Transformations Finally, one can generate one or more new thoughts based on an existing single thought v. This class embraces analogous reasoning steps from earlier schemes, such as ToT or CoT-SC. Formally, we

Thoughts are scored to understand whether the current solution is good enough. A score is modeled as a general function E(v, G, pθ), where v is a thought to be evaluated. We use the state of the whole reasoning process (G) in E for maximum generality, because – for example – in some evaluation scenarios, scores may be relative to other thoughts. GoT can also rank thoughts. We model this with a function R(G, pθ, h) where h specifies the number of highestranking thoughts in G to be returned by R. While the specific form of R depends on a use case, we most often use a simple yet effective strategy where h thoughts with highest

scores are returned, i.e., v1, ..., vh = R(G, pθ, h).

Specific forms of E and R depend on a use case. We discuss the details in Section 5. For example, the score (or rank) for sorting corresponds to the count of elements correctly sorted (or incorrectly, when obtaining the error as a score).

4 System Architecture & Extensibility The GoT architecture consists of a set of interacting modules, see Figure 3 (the blue part). These modules are the Prompter (prepares the messages for the LLM), the Parser (extracts information from LLMs' replies), the Scoring module (verifies and scores the LLM replies), and the Controller (coordinates the entire reasoning process, and decides on how to progress it). The Controller contains two further important elements: the Graph of Operations (GoO) and the Graph Reasoning State (GRS). GoO is a static structure that

+ = {} and E+ = {(v, v)}. This loop in the

} and E+ = {(v, v+

1

), ...,(v, v+ k )}.

− = E− = ∅.

+} and E+ =

specifies the *graph decomposition of a given task*, i.e., it prescribes transformations to be applied to LLM thoughts, together with their order & dependencies. GRS is a dynamic structure that maintains the state of the ongoing LLM reasoning process (the history of its thoughts and their states).

The Prompter prepares the prompt to be sent to the LLM. This module is responsible for the specifics of encoding the graph structure within the prompt. The GoT architecture enables the user to implement use-case specific graph encod-

The Parser extracts information from LLM's thoughts. For each such thought, the Parser constructs the *thought state*, which contains this extracted information. The thought state

Here, we verify whether a given LLM's thought satisfies potential correctness conditions, and then we assign it a score. Depending on how the score is derived, the module may consult the LLM. Moreover, depending on the use case, the score may also be assigned by a human. Finally, use cases

The Controller implements a specific strategy for selecting thoughts from its GRS structure. It also selects what transformations should be applied to which thoughts, and then passes this information to the Prompter. It also decides whether the whole process should be finalized, or whether the next round of interaction with the LLM should be initiated. In our current design, this is dictated by the execution

The user constructs a GoO instance, which prescribes the execution plan of thought operations. GoO is a static structure that is constructed once, before the execution starts. Each operation object knows its predecessor operations and successor operations. Then, during the execution, an instance of GoO maintains the continually updated information about the LLM reasoning process. This includes which operation has been executed so far, the states of all the generated LLM thoughts, their validity and scores, and any other relevant

The above elements offer extensible APIs, enabling straightforward implementations of different prompting schemes. The APIs are outlines in the green part of Figure 3, and detailed in the documentation. We also provide examples of prompts used by these operations and a corre-

5 Example Use Cases

sponding GRS in the red part of Figure 3.

We now describe several use cases of GoT.

such as sorting use simple local scoring functions.

ings by providing full access to the graph structure.

is then used to update GRS accordingly.

4.3 Scoring & Validation

4.1 Prompter

4.2 Parser

4.4 Controller

plan specified in GoO.

4.5 GoO & GRS

information.

+ = {v

lier schemes. Unless stated otherwise, V

only one new vertex is created, V

+ combining these chains.

tions as the original thought.

3.3 Scoring & Ranking Thoughts

+ = {v + 1 , ..., v+ k

v

content: V

have V

**Goal**: Build a prompt to be sent to the LLM

**Goal**: Extract information from

GoT system **Graph of**

**User**

**LLM**

**Human** or **LLM** **Parser**

**Prompter**

quality of the LLM's solution

**Scoring & Ranking validation**

*<Approach>*

**A prompt used by**

*</Approach> <Examples>*

*3, 9, 5, 6, 1]*

*6s and one 9.*

*9, 9, 9, 9, 9] </Examples> Input: {input}*

*7, 7, 8, 8, 9, 9, 9, 9, 9]*

**2**

*To fix the incorrectly sorted list follow these steps:*

*match the frequency of that number in the input list.*

*Input: [3, 7, 0, 2, 8, 1, 2, 2, 2, 4, 7, 8, 5, 5, 3, 9]*

*three extra 9s and is missing two 2s. Output: [0, 1, 2, 2, 2, 2, 3, 3, 4, 5, 5, 7, 7, 8, 8, 9]* 

*Incorrectly Sorted: {incorrectly_sorted}*

The input thought t

This prompt is used by an operation Improve(t), which enhances a given thought t using information provided in another thought. Depending on how the Improve + Repeat operation is implemented by the user within GoT, it can either generate a number of new thoughts in GRS (the upper graph on the right), similar to Generate + Repeat, or may refine the same thought in GRS (the lower graph on the right), chaining k=4 refinement iterations together.

**Specifying the Structure of Graph of Operations (GoO)**

**3**

**Controller Goal**: Initiate, coordinate, manage, and progress the GoT execution

**Operations Goal**: Specify LLM thought

**Goal**: Indicate the top-scoring thoughts

**4**

**Graph Reasoning State**

transformations

**Goal**: Maintain the ongoing LLM reasoning process

Improve(t)+Repeat(k=4) *<Instruction> The following two lists represent an unsorted list of numbers and a sorted variant of that list. The sorted variant is not correct. Fix the sorted variant so that it is correct. Make sure that the output list is sorted in ascending order, has the same number of elements as the input list ({length}), and contains the same elements as the input list. </Instruction>*

*1. For each number from 0 to 9, compare the frequency of that number in the incorrectly sorted list to the frequency of that number in the input list. 2. Iterate through the incorrectly sorted list and add or remove numbers as needed to make the frequency of each number in the incorrectly sorted list*

*Incorrectly Sorted: [0, 0, 0, 0, 0, 1, 2, 2, 3, 3, 4, 4, 4, 5, 5, 7, 7, 8, 8, 9, 9, 9, 9] Reason: The incorrectly sorted list contains four extra 0s, two extra 4s and*

*Input: [6, 4, 5, 7, 5, 6, 9, 7, 6, 9, 4, 6, 9, 8, 1, 9, 2, 4, 9, 0, 7, 6, 5, 6, 6, 2, 8,*

*Incorrectly Sorted: [0, 1, 1, 2, 2, 3, 4, 4, 4, 4, 4, 5, 5, 5, 5, 6, 6, 6, 6, 6, 6, 7,*

*Reason: The incorrectly sorted list contains two extra 4s and is missing two*

*Output: [0, 1, 1, 2, 2, 3, 4, 4, 4, 5, 5, 5, 5, 6, 6, 6, 6, 6, 6, 6, 6, 7, 7, 7, 8, 8, 9,*

...

...

LLM's thought **Goal**: Assess the

Graph of Operations enables seamless specification of not only GoT, but also existing schemes such as CoT, CoT-SC, ToT

**Example prompts and the Graph Reasoning State for the sorting use case** (some examples within each prompt are omitted due to space constraints)

*<Instruction> Sort the following list of numbers in ascending order. Output only the sorted list of numbers, no additional text. </Instruction>*

*Input: [3, 7, 0, 2, 8, 1, 2, 2, 2, 4, 7, 8, 5, 5, 3, 9, 4, 3, 5, 6, 6, 4, 4, 5,* 

*Output: [0, 0, 1, 1, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 4, 4, 4, 4, 5, 5, 5, 5,* 

The input thought t

This prompt is used by an operation Generate where the branching factor is k=1, which means, only one thought is generated. However, as we chain it with the operation Repeat with k=4, the underlying GoT framework ensures that Generate executes 4 times and results in 4 separate thoughts. Note that, from the graph theory perspective, the GRS is identical to that in the operation Generate(t, k=4). The difference between these two is that Generate(t, k=4) gives the user more control over how these multiple thoughts are constructed, while Generate(t, k=1)+Repeat(k=4) is less flexible but more easy to use. Moreover, with Repeat one has 4 context-isolated responses from the LLM for identical prompts, whereas without Repeat there is only one context where all 4 thoughts are generated and must be explicitly handled in a single prompt/session.

Aggregate(t1,t2)+Repeat(k=3)+KeepBest(N=1)

*<Instruction> Merge the following 2 sorted lists of length {length1} each, into one sorted list of length {length2} using a merge sort style approach. Only output the final merged list without any additional text or thoughts!*

*To merge the two lists in a merge-sort style approach, foloow these steps:*

*4. Append the remaining elements of the non-empty list to the merged list.*

This prompt is used by an operation Aggregate where the aggregation factor is k = 2 (2 input thoughts, t1 and t2, are aggregated). This is repeated by GoT 3 times, to maximize quality. Finally, the best result is selected. Note that, in this example, the prompt explicitly requests the merge operation only. All the remaining operations are specified in GoO and are handled by the underlying GoT framework.

Figure 3: The system architecture of GoT, and the APIs of respective modules. The user can straightforwardly extend the design towards new prompting schemes, experiment with novel thought transformations, and plug in different LLMs. The blue part of the figure contains the architecture overview, the green part lists the API, and the red part contains example prompts together

*2. Append the smaller element to the merged list and move to the next element in the list from which the smaller element came. 3. Repeat steps 1 and 2 until one of the lists is empty.*

Generate(t,k=1)+Repeat(k=4)

**A prompt used by**

*1. Compare the first element of both lists.*

*Merge the following two lists into one sorted list:*

The input thoughts t1, t2

*</Instruction> <Approach>*

*</Approach>*

*1: {input1} 2: {input2} Merged list:*

**A prompt used by**

*<Example>*

**I**

**4**

**1**

**3**

*2, 0, 9, 3, 3, 9, 2, 1]*

*6, 6, 7, 7, 8, 8, 9, 9, 9] </Example> Input: {input}*

**Legend Architecture overview**

associated operations Thought state

Score

+ thought's score

External entity Prompt Thought

*➡ //LLM params: model used, temperature, max tokens, api key, org, ... ➡ //LLM cost features: prompt token cost, response token cost, ... ➡ //Instances of Prompter + Parser + Graph of Operations, ➡ //Any additional input parameters (e.g., numbers to be sorted).*

**Available operations when building GoO (extensible)**

*➡* Repeat(k) *//Repeat a given operation k times, generating k thoughts. //For example, this enables "Aggregate" to generate multiple outcomes //of the combination operation. Each such thought is maintained //within the Graph Reasoning State and scored individually.*

*➡* Generate*,* Aggregate*,* Score*, ... //see Prompter API ➡* KeepBest(N) *//preserves N best scoring thoughts*

➡ Generate(t,k) *//generate a prompt for k new thoughts, using thought t*

➡ ValidateAndImprove(t) *//generate a prompt to enhance thought t,* ➡ Aggregate(t1,...,tk) *//generate a prompt to combine thoughts t1, ..., tk* 

➡ Validate(t) *//generate a prompt to validate the correctness of thought t*

*//Each of the above routines is responsible for parsing an LLM's reply //to a corresponding Prompter routine (e.g., ParseScore parses Score).*

ParseGenerate, ParseImprove, ParseScore, ParseAggregate, ParseValidate, ...

**Initial/system prompt** (optional) *Hello. I want to sort the following input sequence of numbers: {input}*

Operation Thought state + its

Dependency Module of the

Thought state

Gray block Blue block

**API for Controller**

**API for Prompter (extensible)**

➡ Score(t) *//score thought t*

**API for Parser (extensible)**

**A prompt used by** Generate(t,k=4) *<Instruction> Split the following list of 64 numbers into 4 lists of 16 numbers each, the first list should contain the first 16 numbers, the second list the second 16 numbers, the third list the third 16 numbers and the fourth list the fourth 16 numbers. Only output the final 4 lists in the following format without any additional text or thoughts!*

**1 2**

 *"List 1": [3, 1, 9, 3, 7, 5, 5, 4, 8, 1, 5, 3, 3, 2, 3, 0], "List 2": [9, 7, 2, 2, 4, 4, 8, 5, 0, 8, 7, 3, 3, 8, 7, 0], "List 3": [9, 5, 1, 6, 7, 6, 8, 9, 0, 3, 0, 6, 3, 4, 8, 0], "List 4": [6, 9, 8, 4, 1, 2, 9, 0, 4, 8, 8, 9, 9, 8, 5, 9]*

This prompt is used by an operation Generate where the branching factor k = 4. Four new thoughts are constructed based on the LLM reply to this prompt.

with a GRS and operations involved.

The input thought t

*Input: [3, 1, 9, 3, 7, 5, 5, 4, 8, 1, 5, 3, 3, 2, 3, 0, 9, 7, 2, 2, 4, 4, 8, 5, 0, 8, 7, 3, 3, 8, 7, 0, 9, 5, 1, 6, 7, 6, 8, 9, 0, 3, 0, 6, 3, 4, 8, 0, 6, 9, 8, 4, 1,* 

 *"List 1": [3, 4, 3, 5, 7, 8, 1, ...], "List 2": [2, 9, 2, 4, 7, 1, 5, ...], "List 3": [6, 9, 8, 1, 9, 2, 4, ...], "List 4": [9, 0, 7, 6, 5, 6, 6, ...] }} </Instruction> <Example>*

*2, 9, 0, 4, 8, 8, 9, 9, 8, 5, 9]*

*{{*

**I**

*Output: {{*

*}} </Example> Input: {input}* 5.1 Sorting

rors:

X = mX−1 i=1

Y = X 9

i=0

max(n − error-scope, 0).

5.2 Set Operations

Due to space constraints, we detail one use case (sorting). We focus on its decomposition and Graph of Operations, which are central for implementing and executing any workload within GoT. We consider sorting numbers 0–9 with duplicates. The considered LLMs are unable to sort a sequence of such numbers correctly beyond a certain length consis-

In GoT, we employ merge-based sorting: First, one decomposes the input sequence of numbers into subarrays. Then, one sorts these subarrays individually, and then respectively merges them into a final solution. Figure 4 illustrates this use case together with its graph decomposition. Here, an LLM thought is a sequence of sorted numbers. To score an outcome, denote an input sequence with [a1, a2, ..., an] and an output one with [b1, b2, ..., bm]. We use the following score that determines "the scope" of er-

error-scope = X + Y

sgn(max(bi − bi+1, 0)),

Here, X indicates how many consecutive pairs of numbers are incorrectly sorted. If two numbers i and i + 1 are incorrectly sorted (i.e., bi > bi+1), then the expression within the summation returns 1, increasing the error score by one. For two numbers correctly sorted, this expression amounts to 0. Then, Y determines how well a given output sequence preserves the frequency of output numbers. Specifically, for each considered number x (x ∈ {0, ..., 9}), we obtain the difference between the count of input elements being equal to x, vs. the count of output elements equal to x. For an output sequence perfectly preserving the frequency of x, this would amount to 0. Any single "deviation" in this count, increases the "error scope" by 1). We then sum this over all considered values of x. When plotting this score, to improve the clarity of plots, we additionally apply clipping min(error-scope, n), as some baselines (IO, CoT) result in large numbers of outliers with high error scope. Finally, to use a "positive score" describing "the scope of correctly sorted" elements, one can use the value

Moreover, we also consider set operations, focusing on set intersection. They have numerous applications (particularly set intersection) in problems ranging from genome or document comparisons to pattern matching [20, 57, 38, 1, 27, 49, 10, 9]. Set intersection of two sets is implemented similarly as the sorting. The second input set is split into subsets and the intersection of those subsets with the first input set is determined with the help of the LLM. Afterwards the resulting

| |{bp : bp = i}| − |{aq : aq = i}| |

**.....**

**1 3 ... 4 8** 16 numbers **Partial solution**

**N = 3 Generate(N)**

64 numbers

**G Score**

Sort

**Score**

**1 4 ... 4 3** 16 numbers

**Partial solution**

**S Score**

Details of the highlighted part of GoO are below

**K**

**8 2 ... 1 3** 16 numbers

Sort **Generate(N) N = 3**

**1 4 6 2 4 ... 9 8 7 5 4**

**Score Score**

**K K**

**K K**

**Details of the highlighted part of GoO from above**

**Generate(N) N = 4 Input**

**G Score**

**A G**

**A**

**1 2 ... 7 8** 16 numbers

Assess how well each sequence is sorted

**1 2 ... 7 8** 16 numbers

**N = 10**

**Score: 100%**

**1 3 ... 4 8** 16 numbers **Partial solution**

**KeepBest(N)** Keep the best scored thoughts **N = 1**

**Partial solution Partial solution**

**Partial solution Partial solution**

**Score: 78% Score: 86%**

**Graph of Operations (GoO) for sorting 64 numbers**

**G**

**S Score**

**K K K**

**S Score**

Note that this is an example graph decomposition. The structure of connections between all operations can be arbitrarily modified.

**S Score**

**..... .....**

**..... .....**

**.....**

Merge into a 64 element subarray **Aggregate(N)**

Sort **Generate(N)**

**K**

Sort KeepBest Aggregate

The first Generate splits the 64-element input array into four 16-element chunks.

**S**

**G Legend** Generate

> **A K**

**Score**

**A**

**Score**

**K**

**How do we score?**

To obtain the score, for every number 0 - 9, we get the difference between the input and the sorted list, and we sum all 10 values. Zero indicates correctly sorted. To show "the higher the better", we do max(input_length - score, 0)

Sorting is implemented within the Generate operation. Here, N=3 means that, for each 16 element chunk, we generate three different sortings.

> **1 1 ... 8 9** 32 numbers **Partial solution**

> > **N = 2**

**Score: 100%**

Here, N=1 means that we maintain a single best sorting outcome out of the three input ones.

**N = 3 N = 3**

**1 1 ... 4 2 1 9 ... 5 4** 16 numbers 16 numbers

**Partial solution Partial solution Partial solution**

Splitting into four 16-element chunks

> Sort **Generate(N)**

**1 1 ... 5 7** 16 numbers

**1 1 ... 5 7** 16 numbers

> **1 3 ... 6 8** 32 numbers **Partial solution**

**Score: 97%**

Figure 4: An example graph decomposition of the sorting use case in GoT. All the used operations (Generate, Aggre-

gate, Score, KeepBest) are described in Figure 3.

**.....**

**1 3 ... 4 6** 16 numbers **Partial solution**

**Score: 97%**

Merge into a 32 element subarray **Aggregate(N)**

Here, N=10 means that we try 10 different aggregations of the two input 16-element subarrays.

**1 3 ... 4 8** 16 numbers **Partial solution**

**Score: 100%**

tently because duplicate counts do not match.

where p ∈ {1, ..., m}, q ∈ {1, ..., n}, and

intersection sets are aggregated for the final results. For the evaluation we use different set sizes of 32, 64 and 128 elements and we vary the number of elements found in both

The structure of the schemes is as follows. CoT-SC consists of k independent chains originating from a single starting thought. ToT is a complete k-ary tree. Finally, in GoT, a complete k-ary tree is joined at its leaves with a "mirrored" k-ary tree of the same size but with its edges reversed.

The analysis is detailed in Table 2. CoT offers a large volume of up to N, but at the cost of a high latency of N. CoT-SC reduces the latency by a factor of k (which corresponds to its branching factor), but it simultaneously decreases the volume by k as well. ToT offers a latency of logk N but also has low volume. GoT is the only scheme to come with both a low latency of logk N and a high volume N. This is enabled by the fact that GoT harnesses aggregations of thoughts, making it possible to reach the final thought from any other intermediate thought in the graph decomposition.

Scheme Latency Volume Chain-of-Thought (CoT) N N Self-Consistency with CoT (CoT-SC) N/k N/k Tree of Thoughts (ToT) logk N O(logk N)

Table 2: Comparison of prompting schemes, with respect to their fundamental tradeoff between latency and volume.

7 Evaluation We show the advantages of GoT over the state of the art. We focus on comparing GoT to ToT, as it was shown to consistently outperform other schemes. Still, for a broad comparison, we also experiment with IO, CoT, and CoT-SC. As our analysis results in a large evaluation space, we present representative results and omit data that does not bring relevant

We use 100 input samples for each task and comparison baseline. We set temperature to be 1.0 and we use 4k context unless stated otherwise. For each experiment, we fix the numbers of thoughts in respective schemes to achieve simi-

Parameters We experiment extensively with the branching factor k and the number of levels L to ensure that we compare GoT to cost-effective and advantageous configurations. We plot two variants of ToT: one with higher k and lower depth (ToT), the other with lower k but higher L (ToT2). We usually aim to achieve a sweetspot in the tradeoff between sparser generation rounds (lower k) vs. more rounds (larger L). Usually more responses per round is more expensive (e.g., 80 vs. 60 total responses for Figure 7 but $6 vs. $3 costs). We also try different problem sizes P (e.g., in sorting,

Used LLMs Due to budget restrictions, we focus on GPT-3.5, using GPT-4. We also experimented with Llama-2, but it was usually worse than GPT-3.5 and also much slower to

P states how many numbers are to be sorted).

run, making it infeasible to obtain enough samples.

Graph of Thoughts (GoT) logk N N

GoT offers the best tradeoff.

insights (e.g., CoT-SC).

7.1 Evaluation Methodology

lar costs in each experiment.

Our score indicates the total number of missing or incorrectly included elements in the final intersection. Specifically, denote two input sets with A = [a1, a2, ..., an] and B = [b1, b2, ..., bn], and the output set with C =

error-scope = X1 + X2 + Xd where X1 = |C \ (A ∩ B)| are the number of elements in C that are not supposed to be there, X2 = |(A∩B)\C| are the number of elements missing from C, and Xd is the number of duplicates in C (because the LLM expresses the set as a list in natural language). Finally, to use a "positive score" describing "the scope of correctly computed" elements, one

Keyword counting finds the frequency of keywords in a given category (countries in our example implementation) within the input text. GoT splits the input text into multiple passages, counts the keywords in each passage and aggregates the sub-results. The number of passages is configurable and can also be left to the LLM, making it possible to treat each sentence as a separate passage. Here, to score a thought, we first – for each keyword – derive the absolute difference between the computed count and the correct one. We then sum all these differences to get the final score.

Finally, we also provide document merging. Here, the goal is to generate a new Non-Disclosure Agreement (NDA) document based on several input ones that partially overlap in terms of their contents. The goal is to ensure minimal amount of duplication, while maximizing information retention. Document merging is broadly applicable in, e.g., legal procedures, where multiple sources of information have to be combined into a single document or article. To score a solution, we query the LLM for two values (3 times for each value, and take the average). The first value corresponds to the solution redundancy (10 indicates no redundancy, 0 implies at least half the information is redundant), the second value stands for information retention (10 indicates all information is retained, 0 says that none is retained). We compute

6 The Latency-Volume Tradeoff We now show that GoT improves upon previous prompting schemes in terms of the tradeoff between latency (number of hops in the graph of thoughts to reach a given final thought) and *volume*. We define volume – for a given thought t – as *the number of preceding LLM thoughts that could have impacted* t. Formally, the volume of t is the number of thoughts from which there exists a path to t in the graph of thoughts. We assume that outputting a single thought costs O(1) time and fix the total cost to Θ(n) for each prompting scheme.

can use the value max(n − error-scope, 0).

5.3 Keyword Counting

5.4 Document Merging

the harmonic mean of these values.

sets to be between 25% and 75%.

[c1, c2, ..., cm]. Then,

IO CoT ToT ToT2**GoT**

The results of analysis are in Figure 5 (sorting), 6 (set intersection), 7 (keyword counting), and 8 (document merging); see Section 5 for the description of specific use cases. *Overall, GoT improves the quality of outcomes over all the considered baselines and it reduces inference costs compared to*

GoT vs. ToT GoT improves upon ToT and ToT2 by a large margin over all the considered problem instances. ToT usually comes with somewhat higher quality than ToT2, but simultaneously much higher costs. GoT's costs are always lower than ToT, and comparable (in some cases lower, in others higher) than ToT2. For example, it reduces median error by ≈62%, thereby achieving a higher quality of sorting, for P = 128 in comparison to ToT while ensuring >31% cost reductions. These advantages are due to GoT's ability to decompose complex tasks into simpler sub-tasks, solve these sub-tasks independently, and then incrementally merge

GoT vs. IO and CoT GoT consistently delivers much higher quality of outcomes than IO/CoT. For example, for sorting (P = 64), GoT's median error is ≈65% and ≈83% lower than, respectively, CoT and IO. Yet, the costs of GoT – and ToT – are much higher than in IO and CoT. This is mostly due to our configuration of CoT, where we do not artificially inflate the lengths of the chains of reasoning if this does not improve the outcomes. The higher costs of GoT and ToT are driven by k new thoughts built for each Generate operation; these multiple thoughts are one of the reasons for

Increasing Complexity of Tackled Problems Most importantly, the advantages of GoT in the quality *increase for all the baselines with the growing size of the problem* P. For example, in sorting, while for P = 32 GoT only negligibly improves upon ToT2, its median error count becomes lower by ≈61% for P = 64 and ≈69% for P = 128. The quartiles also become respectively better. The results for other schemes also follow the intuition; for example, IO becomes

0.0 0.2 0.4 0.6 0.8 1.0 1.2 1.4 1.6

IO CoT ToT ToT2**GoT**

Figure 5: Number of errors and cost in sorting tasks with ChatGPT-3.5. L and k indicate the structure of ToT (see Sections 3.2

0.0 0.3 0.6 0.9 1.2 1.5 1.8 2.1 2.4 2.7 3.0 3.3 3.6 3.9 4.2 4.5 4.8

problem sizes.

GoT: Figure 4 clipped

IO CoT ToT ToT2**GoT**

7.3 Discussion on Task Decomposition

difference between GoT8 and GoTx in Figure 7).

cessful when aggregating the final solution.

8.1 Prompting Paradigms & Approaches

The overall goal when conducting graph decomposition is to break down a task to the point, where the LLM can solve it correctly for the majority of time using a single prompt (or with a few additional improvement steps). This significantly lowers the number of improvement/refinement steps needed during the later stages of the graph exploration. Furthermore, as indicated by our results, combining or concatenating sub-results is usually an easier task than solving large task instances from scratch. Hence, the LLM is often suc-

8 Related Work We summarize relations between GoT and related work.

We detail different prompting paradigms in Section 1 and Table 1. There are numerous other work related to prompting. We now briefly summarize selected most related ones;

consistently worse with the increasing P, which is expected as a single thought is unlikely to solve a large problem instance. *Overall, this analysis illustrates that GoT is indeed well-suited for elaborate problem cases*, as the execution schedules usually become more complex with the growing

When splitting a task into subtasks and then solving these subtasks, the size of responses and the input (in tokens) are reduced proportionally to the degree of task decomposition. However, the "static" part of the prompt (i.e., few-shot examples) may become a significant overhead (see GoT4 to GoT8 in Figure 7). Here, we observe that these few-shot examples can usually also be reduced in size (e.g., the passages used to demonstrate keyword counting can also be made smaller and still be indicative of the actual input size), thus actively working towards decreasing the cost (e.g., see the

Total Cost ($); **the lower the better**

L=10 k=10

GoT: Figure 4

**128 elements**

L=4 k=20

**64 elements**

L=7 k=10

clipped

L=4 k=20

**32 elements**

L=2 k=20

> L=3 k=10

GoT: Figure 4

7.2 Analysis of GoT's Advantages

these outcomes into the final result.

GoT's superiority in quality.

and 6).

*ToT*.

#incorrectly sorted elements; **the lower the better**

IO CoT ToT ToT2**GoT**

0 0 1 0 8 7 25

L=6 k=10

L=4 k=20

Splits the input text into 4 passages, counts keywords in each one, aggregates the subresults always 2 at a time

IO CoT ToT ToT2 **GoT4 GoT8 GoTx**

Figure 7: Number of errors and cost in keyword counting with ChatGPT-3.5. L and k indicate the structure of ToT (see

more extensive descriptions can be found in dedicated surveys [68, 40, 69, 34]. Wang et al. proposed Plan-and-Solve, an approach to enhance CoT with an explicit planning stage [65]. Using complexity-based criteria to enhance prompting within a CoT was designed by Fu et al. [66, 29]. The self-taught reasoner (STaR) [79] generates several chain of thoughts, and selects the ones that are valid. Similarly, a scheme by Shum et al. [60] generates a pool of CoT candidates, and selects the best candidate based on whether the candidates match the ground truth and on a policy gradientbased method. Automatic prompt generation overcomes the issues of scaling in CoT [58, 42, 41]. Zhou et al. proposes to harness selecting the best prompt out of a candidate set [83]. Finally, in prompt chaining, one cascades different LLMs. This enables prompting different LLMs via different con-

0

Sections 3.2 and 6).

5

10

15

20

Number of errors; **the lower the better**

25

30

35

Samples solved correctly

0.0 0.2 0.4 0.6 0.8 1.0 1.2 1.4 1.6 1.8

Total Cost ($); **the lower the better**

Splits the input into sentences (each input has 12-19 sentences)

As GoT4, but splits the input text into 8 passages

IO CoT ToT ToT2**GoT**

Figure 6: Number of errors and cost in set intersection with ChatGPT-3.5. L and k indicate the structure of ToT (see Sections 3.2

0.0 0.6 1.2 1.8 2.4 3.0 3.6 4.2 4.8

0

tokens.

2

4

Score (out of 10); **the higher the better**

6

8

IO CoT ToT ToT2**GoT**

L=3 k=10

IO CoT ToT **GoT GoT2**

Figure 8: Score and cost in document merging with ChatGPT-3.5. L and k indicate the structure of ToT (see Sections 3.2 and 6). Number of samples: 50; context size: 16k

texts, enabling more powerful reasoning [21, 47, 72, 23, 50, 71, 72]. GoT is orthogonal to this class of schemes, as it

Self-reflection and self-evaluation were introduced recently [59, 48, 45, 74]. They are used to enhance different tasks, for example for code generation [17] or computer operation tasks [39]. In GoT, we partially rely on self-evaluation when taking decisions on how to expand the

There are many works recently on how to plan complex tasks with LLMs [36, 80, 77, 75, 67, 37]. GoT could be seen

focuses on a single context capabilities.

graph of thoughts within a prompt.

8.3 LLMs & Planning

8.2 Self-Reflection & Self-Evaluation

0

3

6

9

Total Cost ($); **the lower the better**

12

15

Aggregation of fully merged NDAs

> Aggregation of partially merged NDAs

Total Cost ($); **the lower the better**

0 0 0 0 0 **128 elements**

> L=4 k=25

> > L=9 k=10

32 0 0 0 0 4 **64 elements**

> L=4 k=20

L=7 k=10

7 6 31 29 43 **32 elements**

> L=2 k=20

> > L=3 k=10

and 6).

#incorrect elements; **the lower the better**

Solved correctly: as a generic framework that could potentially be used to enhance such schemes, by offering a paradigm for generating Ault and Daint machines, and for their excellent technical support. We thank Timo Schneider for help with infrastructure at SPCL. This project received funding from the European Research Council (Project PSAP, No. 101002047), and the European High-Performance Computing Joint Undertaking (JU) under grant agreement No. 955513 (MAELSTROM). This project was supported by the ETH Future Computing Laboratory (EFCL), financed by a donation from Huawei Technologies. This project received funding from the European Union's HE research and innovation programme under the grant agreement No. 101070141 (Project

References [1] M. Besta et al. Graphminesuite: Enabling highperformance and programmable graph mining algorithms with set algebra. *arXiv preprint*

[2] M. Besta et al. Sisa: Set-centric instruction set architecture for graph mining on processing-in-memory systems. *arXiv preprint arXiv:2104.07582*, 2021. [3] M. Besta et al. Practice of streaming processing of dynamic graphs: Concepts, models, and systems. *IEEE*

[4] M. Besta et al. High-performance graph databases that are portable, programmable, and scale to hundreds of thousands of cores. *arXiv preprint arXiv:2305.11162*,

[5] M. Besta, R. Gerstenberger, N. Blach, M. Fischer, and T. Hoefler. Gdi: A graph database interface standard. Technical report, 2023. Available at https://spcl.inf. ethz.ch/Research/Parallel Programming/GDI/. [6] M. Besta, R. Grob, C. Miglioli, N. Bernold, G. Kwasniewski, G. Gjini, R. Kanakagiri, S. Ashkboos, L. Gianinazzi, N. Dryden, et al. Motif prediction with graph

[7] M. Besta and T. Hoefler. Parallel and distributed graph neural networks: An in-depth concurrency analysis.

[8] M. Besta, P. Iff, F. Scheidl, K. Osawa, N. Dryden, M. Podstawski, T. Chen, and T. Hoefler. Neural graph

[9] M. Besta, R. Kanakagiri, H. Mustafa, M. Karasikov, G. Ratsch, T. Hoefler, and E. Solomonik. ¨ Communication-efficient jaccard similarity for high-performance distributed genome comparisons.

[10] M. Besta, C. Miglioli, P. S. Labini, J. Tetek, P. Iff, ˇ R. Kanakagiri, S. Ashkboos, K. Janda, M. Podstawski, G. Kwasniewski, et al. Probgraph: High-performance and high-accuracy graph mining with probabilistic set representations. In *ACM/IEEE Supercomputing*, 2022. [11] M. Besta, E. Peter, R. Gerstenberger, M. Fischer, M. Podstawski, C. Barthels, G. Alonso, and T. Hoefler. Demystifying graph databases: Analysis and taxonomy of data organization, system designs, and graph queries. *arXiv preprint arXiv:1910.09017*, 2019.

neural networks. In *ACM KDD*, 2022.

*arXiv preprint arXiv:2205.09702*, 2022.

*arXiv preprint arXiv:1911.04200*, 2019.

databases. In *LOG*, 2022.

GLACIATION).

*arXiv:2103.03653*, 2021.

*TPDS*, 2022.

2023.

Graphs have become an immensely popular and important part of the general computing landscape [44, 46, 32, 31, 55]. Recently, there has been a growing interest in domains such as graph databases [53, 54, 11, 4, 5, 8], graph pattern matching [25, 18, 61, 10, 2, 1], graph streaming [26, 22, 3], and graph machine learning as well as graph neural networks [33, 73, 82, 81, 16, 33, 12, 6, 30, 56, 7]. The graph abstraction has been fruitful for many modern research domains, such as social sciences (e.g., studying human interactions), bioinformatics (e.g., analyzing protein structures), chemistry (e.g., designing chemical compounds), medicine (e.g., drug discovery), cybersecurity (e.g., identifying intruder machines), healthcare (e.g., exposing groups of people who submit fraudulent claims), web graph analysis (e.g., providing accurate search services), entertainment services (e.g., predicting movie popularity), linguistics (e.g., modeling relationships between words), transportation (e.g., finding efficient routes), physics (e.g., understanding phase transitions and critical phenomena), and many others [44, 20, 38, 35, 15]. In this work, we harness the graph abstraction as a key mechanism that enhances prompting capabilities in

9 Conclusion Prompt engineering is one of the central new domains of the large language model (LLM) research. It enables using LLMs efficiently, without any model updates. However, de-

In this work, we propose Graph of Thoughts (GoT), a new paradigm that enables the LLM to solve different tasks effectively without any model updates. The key idea is to model the LLM reasoning as an arbitrary graph, where thoughts are vertices and dependencies between thoughts are edges. This enables novel transformations of thoughts, such as aggregation. Human's task solving is often non-linear, and it involves combining intermediate solutions into final ones, or changing the flow of reasoning upon discovering new in-

GoT outperforms other prompting schemes, for example ensuring 62% increase in the quality of sorting over ToT, while simultaneously reducing costs by >31%. We also propose a novel metric for a prompting scheme, the volume of a thought, to indicate the scope of information that a given LLM output could carry with it, where GoT also excels. This provides a step towards more principled prompt engineering. The graph abstraction has been the foundation of several successful designs in computing and AI over last decades, for example AlphaFold for protein predictions. Our work harnesses it within the realm of prompt engineering.

Acknowledgements We thank Hussein Harake, Colin McMurtrie, Mark Klein, Angelo Mangili, and the whole CSCS team granting access to the

signing effective prompts is a challenging task.

sights. GoT reflects this with its graph structure.

complex graph-based plans.

LLMs.

8.4 Graphs and Graph Computing

[12] M. M. Bronstein, J. Bruna, Y. LeCun, A. Szlam, and P. Vandergheynst. Geometric deep learning: going beyond euclidean data. *IEEE Signal Processing Maga-* [26] G. Feng et al. Distinger: A distributed graph data structure for massive dynamic graph processing. In *IEEE*

[27] A. Friggeri, G. Chelius, and E. Fleury. Triangles to capture social cohesion. In *2011 IEEE Third International Conference on Privacy, Security, Risk and Trust and 2011 IEEE Third International Conference on So-*

*cial Computing*, pages 258–265. IEEE, 2011. [28] K. Friston. Hierarchical models in the brain. *PLoS computational biology*, 4(11):e1000211, 2008. [29] Y. Fu, H. Peng, A. Sabharwal, P. Clark, and T. Khot. Complexity-based prompting for multi-step reasoning.

*arXiv preprint arXiv:2210.00720*, 2022.

*POOSC*, 2005.

*arXiv:1709.05584*, 2017.

*arXiv:2204.08892*, 2022.

158–167. ACM, 2004.

PMLR, 2022.

[30] L. Gianinazzi, M. Fries, N. Dryden, T. Ben-Nun, and T. Hoefler. Learning combinatorial node labeling algorithms. *arXiv preprint arXiv:2106.03594*, 2021. [31] D. Gregor and A. Lumsdaine. Lifting sequential graph algorithms for distributed-memory parallel computation. *ACM SIGPLAN Notices*, 40(10):423–437, 2005. [32] D. Gregor and A. Lumsdaine. The parallel bgl: A generic library for distributed graph computations.

[33] W. L. Hamilton et al. Representation learning on graphs: Methods and applications. *arXiv preprint*

[34] M. Hartmann and D. Sonntag. A survey on improving nlp models with human explanations. *arXiv preprint*

[35] T. Horvath, T. G ´ artner, and S. Wrobel. Cyclic pattern ¨ kernels for predictive graph mining. In *KDD*, pages

[36] W. Huang, P. Abbeel, D. Pathak, and I. Mordatch. Language models as zero-shot planners: Extracting actionable knowledge for embodied agents. In *International Conference on Machine Learning*, pages 9118–9147.

[37] W. Huang, F. Xia, T. Xiao, H. Chan, J. Liang, P. Florence, A. Zeng, J. Tompson, I. Mordatch, Y. Chebotar, et al. Inner monologue: Embodied reasoning through planning with language models. *arXiv*

[38] C. Jiang, F. Coenen, and M. Zito. A survey of frequent subgraph mining algorithms. *The Knowledge*

[39] G. Kim, P. Baldi, and S. McAleer. Language models can solve computer tasks. *arXiv preprint*

[40] P. Lertvittayakumjorn and F. Toni. Explanation-based human debugging of nlp models: A survey. *Transactions of the Association for Computational Linguistics*,

[41] B. Lester, R. Al-Rfou, and N. Constant. The power of scale for parameter-efficient prompt tuning. *arXiv*

*Engineering Review*, 28(1):75–105, 2013.

*preprint arXiv:2207.05608*, 2022.

*preprint arXiv:2104.08691*, 2021.

*arXiv:2303.17491*, 2023.

9:1508–1528, 2021.

*Big Data*, pages 1814–1822, 2015.

[13] T. Brown, B. Mann, N. Ryder, M. Subbiah, J. D. Kaplan, P. Dhariwal, A. Neelakantan, P. Shyam, G. Sastry, A. Askell, et al. Language models are few-shot learners. *Advances in neural information processing*

[14] S. Bubeck, V. Chandrasekaran, R. Eldan, J. Gehrke, E. Horvitz, E. Kamar, P. Lee, Y. T. Lee, Y. Li, S. Lundberg, et al. Sparks of artificial general intelligence: Early experiments with GPT-4. *arXiv preprint*

[15] D. Chakrabarti and C. Faloutsos. Graph mining: Laws, generators, and algorithms. *ACM computing surveys*

[16] I. Chami, S. Abu-El-Haija, B. Perozzi, C. Re,´ and K. Murphy. Machine learning on graphs: A model and comprehensive taxonomy. *arXiv preprint*

[17] X. Chen, M. Lin, N. Scharli, and D. Zhou. Teaching ¨ large language models to self-debug. *arXiv preprint*

[18] J. Cheng, J. X. Yu, B. Ding, S. Y. Philip, and H. Wang. Fast graph pattern matching. In *2008 IEEE 24th International Conference on Data Engineering*, pages 913–

[19] A. Chowdhery, S. Narang, J. Devlin, M. Bosma, G. Mishra, A. Roberts, P. Barham, H. W. Chung, C. Sutton, S. Gehrmann, et al. Palm: Scaling language modeling with pathways. *arXiv preprint*

[20] D. J. Cook and L. B. Holder. *Mining graph data*. John

[21] A. Creswell, M. Shanahan, and I. Higgins. Selectioninference: Exploiting large language models for interpretable logical reasoning. *arXiv preprint*

[22] L. Dhulipala et al. Low-latency graph streaming using compressed purely-functional trees.

[23] D. Dohan, W. Xu, A. Lewkowycz, J. Austin, D. Bieber, R. G. Lopes, Y. Wu, H. Michalewski, R. A. Saurous, J. Sohl-Dickstein, et al. Language model cascades.

[24] I. Drori, S. Zhang, R. Shuttleworth, L. Tang, A. Lu, E. Ke, K. Liu, L. Chen, S. Tran, N. Cheng, et al. A neural network solves, explains, and generates university math problems by program synthesis and few-shot learning at human level. *Proceedings of the National Academy of Sciences*, 119(32):e2123433119, 2022. [25] W. Fan, J. Li, S. Ma, N. Tang, Y. Wu, and Y. Wu. Graph pattern matching: from intractable to polynomial time. *Proceedings of the VLDB Endowment*, 3(1-

*arXiv preprint arXiv:2207.10342*, 2022.

*zine*, 34(4):18–42, 2017.

*systems*, 33:1877–1901, 2020.

*arXiv:2303.12712*, 2023.

*(CSUR)*, 38(1):2, 2006.

*arXiv:2005.03675*, 2020.

*arXiv:2304.05128*, 2023.

*arXiv:2204.02311*, 2022.

*arXiv:2205.09712*, 2022.

*arXiv:1904.08380*, 2019.

2):264–275, 2010.

Wiley & Sons, 2006.

922. IEEE, 2008.

[42] X. L. Li and P. Liang. Prefix-tuning: Optimizing continuous prompts for generation. *arXiv preprint* [58] T. Shin, Y. Razeghi, R. L. Logan IV, E. Wallace, and S. Singh. Autoprompt: Eliciting knowledge from language models with automatically generated prompts.

[59] N. Shinn, B. Labash, and A. Gopinath. Reflexion: an autonomous agent with dynamic memory and selfreflection. *arXiv preprint arXiv:2303.11366*, 2023. [60] K. Shum, S. Diao, and T. Zhang. Automatic prompt augmentation and selection with chain-of-thought from labeled data. *arXiv preprint arXiv:2302.12822*,

[61] C. H. Teixeira, A. J. Fonseca, M. Serafini, G. Siganos, M. J. Zaki, and A. Aboulnaga. Arabesque: a system for distributed graph mining. In *Proceedings of the 25th Symposium on Operating Systems Principles*,

[62] H. Touvron, T. Lavril, G. Izacard, X. Martinet, M.-A. Lachaux, T. Lacroix, B. Roziere, N. Goyal, E. Hambro, ` F. Azhar, et al. Llama: Open and efficient foundation language models. *arXiv preprint arXiv:2302.13971*,

[63] H. Touvron, L. Martin, K. Stone, P. Albert, A. Almahairi, Y. Babaei, N. Bashlykov, S. Batra, P. Bhargava, S. Bhosale, et al. Llama 2: Open foundation and finetuned chat models. *arXiv preprint arXiv:2307.09288*,

[64] A. Vaswani, N. Shazeer, N. Parmar, J. Uszkoreit, L. Jones, A. N. Gomez, Ł. Kaiser, and I. Polosukhin.

[66] X. Wang, J. Wei, D. Schuurmans, Q. Le, E. Chi, and D. Zhou. Self-consistency improves chain of thought reasoning in language models. *arXiv preprint*

[67] Z. Wang, S. Cai, A. Liu, X. Ma, and Y. Liang. Describe, explain, plan and select: Interactive planning with large language models enables open-world multitask agents. *arXiv preprint arXiv:2302.01560*, 2023. [68] Z. Wang, G. Zhang, K. Yang, N. Shi, W. Zhou, S. Hao, G. Xiong, Y. Li, M. Y. Sim, X. Chen, et al. Interactive natural language processing. *arXiv preprint*

[69] Z. J. Wang, D. Choi, S. Xu, and D. Yang. Putting humans in the natural language processing loop: A sur-

[71] T. Wu, E. Jiang, A. Donsbach, J. Gray, A. Molina, M. Terry, and C. J. Cai. Promptchainer: Chaining large language model prompts through visual programming.

vey. *arXiv preprint arXiv:2103.04044*, 2021. [70] J. Wei, X. Wang, D. Schuurmans, M. Bosma, E. Chi, Q. Le, and D. Zhou. Chain of thought prompting elicits reasoning in large language models. *arXiv preprint*

Attention is all you need. In *NeurIPS*, 2017. [65] L. Wang, W. Xu, Y. Lan, Z. Hu, Y. Lan, R. K.-W. Lee, and E.-P. Lim. Plan-and-solve prompting: Improving zero-shot chain-of-thought reasoning by large language models. *arXiv preprint arXiv:2305.04091*,

pages 425–440. ACM, 2015.

*arXiv preprint arXiv:2010.15980*, 2020.

2023.

2023.

2023.

2023.

*arXiv:2203.11171*, 2022.

*arXiv:2305.13246*, 2023.

*arXiv:2201.11903*, 2022.

[43] J. Long. Large language model guided tree-of-thought.

[44] A. Lumsdaine, D. Gregor, B. Hendrickson, and J. Berry. Challenges in parallel graph processing. *Par-*

[47] M. Nye, A. J. Andreassen, G. Gur-Ari, H. Michalewski, J. Austin, D. Bieber, D. Dohan, A. Lewkowycz, M. Bosma, D. Luan, et al. Show your work: Scratchpads for intermediate computation with language models. *arXiv preprint arXiv:2112.00114*,

[48] D. Paul, M. Ismayilzada, M. Peyrard, B. Borges, A. Bosselut, R. West, and B. Faltings. Refiner: Reasoning feedback on intermediate representations. *arXiv*

[49] A. Prat-Perez, D. Dominguez-Sal, J. M. Brunat, and J.- ´ L. Larriba-Pey. Shaping communities out of triangles. In *Proceedings of the 21st ACM international conference on Information and knowledge management*,

[50] S. Qiao, Y. Ou, N. Zhang, X. Chen, Y. Yao, S. Deng, C. Tan, F. Huang, and H. Chen. Reasoning with language model prompting: A survey. *arXiv preprint*

[51] A. Radford, K. Narasimhan, T. Salimans, and I. Sutskever. Improving language understanding by

[52] A. Radford, J. Wu, R. Child, D. Luan, D. Amodei, I. Sutskever, et al. Language models are unsupervised multitask learners. *OpenAI blog*, 1(8):9, 2019. [53] I. Robinson, J. Webber, and E. Eifrem. *Graph databases*. " O'Reilly Media, Inc.", 2013. [54] I. Robinson, J. Webber, and E. Eifrem. *Graph databases: new opportunities for connected data*. "

[55] S. Sakr et al. The future is big graphs! a community view on graph processing systems. *arXiv preprint*

[56] F. Scarselli, M. Gori, A. C. Tsoi, M. Hagenbuchner, and G. Monfardini. The graph neural network model. *IEEE transactions on neural networks*, 20(1):61–80,

[57] S. E. Schaeffer. Graph clustering. *Computer science*

*allel Processing Letters*, 17(01):5–20, 2007. [45] A. Madaan, N. Tandon, P. Gupta, S. Hallinan, L. Gao, S. Wiegreffe, U. Alon, N. Dziri, S. Prabhumoye, Y. Yang, et al. Self-refine: Iterative refinement with self-feedback. *arXiv preprint arXiv:2303.17651*, 2023. [46] G. Malewicz, M. H. Austern, A. J. Bik, J. C. Dehnert, I. Horn, N. Leiser, and G. Czajkowski. Pregel: a system for large-scale graph processing. In *ACM SIGMOD*,

*arXiv preprint arXiv:2305.08291*, 2023.

*arXiv:2101.00190*, 2021.

pages 135–146. ACM, 2010.

*preprint arXiv:2304.01904*, 2023.

pages 1677–1681, 2012.

*arXiv:2212.09597*, 2022.

generative pre-training, 2018.

O'Reilly Media, Inc.", 2015.

*arXiv:2012.06171*, 2020.

*review*, 1(1):27–64, 2007.

2008.

2021.

In *CHI Conference on Human Factors in Computing Systems Extended Abstracts*, pages 1–10, 2022. [72] T. Wu, M. Terry, and C. J. Cai. AI chains: Transparent and controllable human-AI interaction by chaining large language model prompts. In *Proceedings of the 2022 CHI Conference on Human Factors in Comput-*

A Positive Score Evaluation The following figures plot the same data as Figures 5 and 6 respectively, however use the "positive score" described in

**64 elements**

GoT: Figure 4 GoT: Figure 4 GoT: Figure 4

L=7

IO CoT ToT ToT2**GoT**

Figure 9: Accuracy and cost in sorting tasks with ChatGPT-3.5. L and k indicate the structure of ToT (see Sections 3.2

> 0 0 0 0 4 **64 elements**

> > L=7 k=10

IO CoT ToT ToT2**GoT**

Figure 10: Accuracy and cost in set intersection with ChatGPT-3.5. L and k indicate the structure of ToT (see Sec-

L=4 k=20 0.0 0.2 0.4 0.6 0.8 1.0 1.2 1.4 1.6 1.8 2.0 2.2 2.4

L=4 k=20 0.0 0.3 0.6 0.9 1.2 1.5 1.8 2.1 2.4 2.7 3.0 3.3 3.6 3.9 4.2 4.5 4.8

k=10 L=4

IO CoT ToT ToT2**GoT**

128 0 0 0 0 0 **128 elements**

L=4 k=25 L=9 k=10

IO CoT ToT ToT2**GoT**

0.0 0.5 1.0 1.5 2.0 2.5 3.0 3.5 4.0 4.5 5.0 5.5 6.0 6.5 7.0 7.5 8.0

Total Cost ($); **the lower the better**

Total Cost ($); **the lower the better**

L=10 k=10

**128 elements**

k=20

0.0 0.2 0.4 0.6 0.8 1.0 1.2 1.4

Sections 5.1 and 5.2.

**32 elements**

L=2 1.6 k=20

L=3 k=10

IO CoT ToT ToT2**GoT**

7 6 31 29 43 **32 elements**

> L=2 k=20 L=3 k=10

IO CoT ToT ToT2**GoT**

tions 3.2 and 6).

0.0 0.2 0.4 0.6 0.8 1.0 1.2 1.4 1.6 1.8 2.0 2.2 2.4

and 6).

Samples solved correctly:

#correct elements; **the higher the better**

#correct elements; **the higher the better**

[73] Z. Wu et al. A comprehensive survey on graph neural networks. *IEEE Transactions on Neural Networks and*

[74] Y. Xie, K. Kawaguchi, Y. Zhao, X. Zhao, M.-Y. Kan, J. He, and Q. Xie. Decomposition enhances reasoning via self-evaluation guided decoding. *arXiv preprint*

[75] S. Yang, O. Nachum, Y. Du, J. Wei, P. Abbeel, and D. Schuurmans. Foundation models for decision making: Problems, methods, and opportunities. *arXiv*

[76] S. Yao, D. Yu, J. Zhao, I. Shafran, T. L. Griffiths, Y. Cao, and K. Narasimhan. Tree of thoughts: Deliberate problem solving with large language models. *arXiv*

[77] S. Yao, J. Zhao, D. Yu, N. Du, I. Shafran, K. Narasimhan, and Y. Cao. React: Synergizing reasoning and acting in language models. *arXiv preprint*

[78] Y. Yao, Z. Li, and H. Zhao. Beyond chain-of-thought, effective graph-of-thought reasoning in large language models. *arXiv preprint arXiv:2305.16582*, 2023. [79] E. Zelikman, Y. Wu, J. Mu, and N. Goodman. Star: Bootstrapping reasoning with reasoning. *Advances in Neural Information Processing Systems*, 35:15476–

[80] S. Zhang, Z. Chen, Y. Shen, M. Ding, J. B. Tenenbaum, and C. Gan. Planning with large language models for code generation. *arXiv preprint arXiv:2303.05510*,

[81] Z. Zhang, P. Cui, and W. Zhu. Deep learning on graphs: A survey. *IEEE Transactions on Knowledge and Data*

[82] J. Zhou et al. Graph neural networks: A review of methods and applications. *AI Open*, 1:57–81, 2020. [83] Y. Zhou, A. I. Muresanu, Z. Han, K. Paster, S. Pitis, H. Chan, and J. Ba. Large language models are human-level prompt engineers. *arXiv preprint*

*ing Systems*, pages 1–22, 2022.

*Learning Systems*, 2020.

*arXiv:2305.00633*, 2023.

*preprint arXiv:2303.04129*, 2023.

*preprint arXiv:2305.10601*, 2023.

*arXiv:2210.03629*, 2022.

15488, 2022.

*Engineering*, 2020.

*arXiv:2211.01910*, 2022.

2023.

