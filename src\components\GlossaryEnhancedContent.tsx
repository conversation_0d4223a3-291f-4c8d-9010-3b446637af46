'use client';

import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { useGlossary } from '@/context/GlossaryContext';
import Tooltip from '@/components/Tooltip';

interface GlossaryEnhancedContentProps {
  content: string;
  className?: string;
}

const GlossaryEnhancedContent: React.FC<GlossaryEnhancedContentProps> = ({
  content,
  className = ''
}) => {
  const { terms, isLoading } = useGlossary();

  // Create a map of terms for faster lookup
  const termMap = React.useMemo(() => {
    if (!terms || terms.length === 0) return new Map<string, string>();

    const map = new Map<string, string>();
    terms.forEach(term => {
      // Add the main term
      map.set(term.term.toLowerCase(), `${term.term}${term.acronym ? ` (${term.acronym})` : ''}: ${term.definition}`);

      // Add the acronym if it exists
      if (term.acronym) {
        map.set(term.acronym.toLowerCase(), `${term.term}${term.acronym ? ` (${term.acronym})` : ''}: ${term.definition}`);
      }
    });

    return map;
  }, [terms]);

  // Process text to add tooltips to glossary terms
  const processText = (text: string) => {
    if (isLoading || !text || text.trim() === '') return text;

    // Create a map of all terms (case-insensitive) for quick lookups
    const termRegexes: Array<{regex: RegExp, term: string, definition: string}> = [];

    // Build regex patterns for each term
    Array.from(termMap.entries()).forEach(([key, definition]) => {
      // Escape special regex characters in the term
      const escapedKey = key.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      // Create a regex that matches the term as a whole word
      termRegexes.push({
        regex: new RegExp(`\\b${escapedKey}\\b`, 'i'),
        term: key,
        definition
      });
    });

    // Check if text contains any glossary terms
    let containsTerms = false;
    let processedText = text;

    // First check if there are any terms to process
    for (const {regex} of termRegexes) {
      if (regex.test(text)) {
        containsTerms = true;
        break;
      }
    }

    // If no terms found, return original text
    if (!containsTerms) return text;

    // Process terms one by one
    const elements: React.ReactNode[] = [];
    let lastIndex = 0;

    // Sort terms by their position in the text
    const matches: Array<{index: number, length: number, term: string, definition: string}> = [];

    for (const {regex, term, definition} of termRegexes) {
      // Find all matches
      const matches_ = [...text.matchAll(new RegExp(regex, 'gi'))];
      for (const match of matches_) {
        if (match.index !== undefined) {
          matches.push({
            index: match.index,
            length: match[0].length,
            term: match[0],
            definition
          });
        }
      }
    }

    // Sort matches by their position in the text
    matches.sort((a, b) => a.index - b.index);

    // Remove overlapping matches (keep the first one found)
    const filteredMatches = [];
    for (const match of matches) {
      const hasOverlap = filteredMatches.some(existing =>
        (match.index < existing.index + existing.length) &&
        (match.index + match.length > existing.index)
      );
      if (!hasOverlap) {
        filteredMatches.push(match);
      }
    }

    // Process matches
    for (const match of filteredMatches) {
      // Add text before the match
      if (match.index > lastIndex) {
        elements.push(
          <React.Fragment key={`text-${lastIndex}`}>
            {text.substring(lastIndex, match.index)}
          </React.Fragment>
        );
      }

      // Add the tooltip for the term
      elements.push(
        <Tooltip key={`term-${match.index}`} text={match.definition}>
          <span className="border-b border-dotted border-gray-400 cursor-help">
            {match.term}
          </span>
        </Tooltip>
      );

      lastIndex = match.index + match.length;
    }

    // Add any remaining text
    if (lastIndex < text.length) {
      elements.push(
        <React.Fragment key={`text-${lastIndex}`}>
          {text.substring(lastIndex)}
        </React.Fragment>
      );
    }

    return elements.length > 0 ? elements : text;
  };

  // Enhanced text processing for ReactMarkdown
  const enhanceTextWithTooltips = (text: string): React.ReactNode => {
    if (isLoading || !text || text.trim() === '' || termMap.size === 0) {
      return text;
    }

    // Create array of all potential matches with their positions
    const matches: Array<{
      index: number;
      length: number;
      text: string;
      definition: string;
    }> = [];

    // Find all matches using proper word boundary regex
    for (const [key, definition] of termMap.entries()) {
      // Escape special regex characters in the term
      const escapedKey = key.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      // Create regex with word boundaries to ensure exact word matching
      const regex = new RegExp(`\\b(${escapedKey})\\b`, 'gi');

      let match;
      while ((match = regex.exec(text)) !== null) {
        matches.push({
          index: match.index,
          length: match[0].length,
          text: match[0], // Use the actual matched text (preserves case)
          definition: definition
        });
      }
    }

    // Sort matches by position
    matches.sort((a, b) => a.index - b.index);

    // Remove overlapping matches (keep the first one found)
    const filteredMatches = [];
    for (const match of matches) {
      const hasOverlap = filteredMatches.some(existing =>
        (match.index < existing.index + existing.length) &&
        (match.index + match.length > existing.index)
      );
      if (!hasOverlap) {
        filteredMatches.push(match);
      }
    }

    // If no matches, return original text
    if (filteredMatches.length === 0) return text;

    // Build the final elements array
    const elements: React.ReactNode[] = [];
    let lastIndex = 0;

    for (const match of filteredMatches) {
      // Add text before the match
      if (match.index > lastIndex) {
        elements.push(text.substring(lastIndex, match.index));
      }

      // Add tooltip for the matched term
      elements.push(
        <Tooltip key={`tooltip-${match.index}-${match.text}`} text={match.definition}>
          <span className="border-b border-dotted border-gray-400 cursor-help">
            {match.text}
          </span>
        </Tooltip>
      );

      lastIndex = match.index + match.length;
    }

    // Add any remaining text
    if (lastIndex < text.length) {
      elements.push(text.substring(lastIndex));
    }

    return elements.length > 0 ? <>{elements}</> : text;
  };

  // Custom components for ReactMarkdown
  const components: any = {
    // Process all text nodes
    text: ({ value }: { value: string }) => {
      return enhanceTextWithTooltips(value);
    },

    // Also process paragraph content
    p: ({ children, ...props }: any) => {
      return <p {...props}>{children}</p>;
    },

    // Leave code elements as is (no tooltips in code)
    code: (props: any) => {
      const { node, inline, className, children, ...rest } = props;
      return (
        <code className={className} {...rest}>
          {children}
        </code>
      );
    }
  };

  return (
    <div className={className}>
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        components={components}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
};

export default GlossaryEnhancedContent;
