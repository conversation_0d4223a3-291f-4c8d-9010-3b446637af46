'use client';

import React, { useMemo } from 'react';
import { useGlossary } from '@/context/GlossaryContext';
import Tooltip from '@/components/Tooltip';

interface InlineGlossaryTextProps {
  text: string;
  className?: string;
}

/**
 * A component that adds tooltips to glossary terms while maintaining inline text flow.
 * Unlike GlossaryText, this component doesn't use ReactMarkdown to avoid block-level elements.
 */
export default function InlineGlossaryText({ text, className = '' }: InlineGlossaryTextProps) {
  const { terms, isLoading } = useGlossary();

  // Process the content to add tooltips to terms
  const processedContent = useMemo(() => {
    if (isLoading || !terms || terms.length === 0 || !text) {
      return [{ type: 'text', content: text }];
    }

    // Create a map of terms for faster lookup
    const termMap = new Map<string, { display: string; definition: string }>();
    terms.forEach(term => {
      // Skip very short terms to avoid false positives
      if (term.term.length >= 3) {
        termMap.set(term.term.toLowerCase(), {
          display: term.term,
          definition: `${term.term}${term.acronym ? ` (${term.acronym})` : ''}: ${term.definition}`
        });
      }

      if (term.acronym && term.acronym.length >= 2) {
        termMap.set(term.acronym.toLowerCase(), {
          display: term.acronym,
          definition: `${term.term}${term.acronym ? ` (${term.acronym})` : ''}: ${term.definition}`
        });
      }
    });

    // Sort terms by length (longest first) to handle nested terms
    const sortedTerms = Array.from(termMap.keys()).sort((a, b) => b.length - a.length);

    // Process text directly into segments without intermediate markers
    const segments: Array<{ type: 'text' | 'tooltip'; content: string; definition?: string }> = [];
    let remainingText = text;

    while (remainingText.length > 0) {
      let foundMatch = false;
      let earliestMatch = { index: remainingText.length, term: '', termInfo: null as any };

      // Find the earliest occurring term in the remaining text
      for (const term of sortedTerms) {
        const termInfo = termMap.get(term);
        if (!termInfo) continue;

        // Create a regex that matches the term as a whole word
        const regex = new RegExp(`\\b(${term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})\\b`, 'i');
        const match = remainingText.match(regex);

        if (match && match.index !== undefined && match.index < earliestMatch.index) {
          earliestMatch = {
            index: match.index,
            term: match[0],
            termInfo
          };
          foundMatch = true;
        }
      }

      if (foundMatch && earliestMatch.termInfo) {
        // Add text before the match
        if (earliestMatch.index > 0) {
          const beforeText = remainingText.substring(0, earliestMatch.index);
          segments.push({ type: 'text', content: beforeText });
        }

        // Add the tooltip segment
        segments.push({
          type: 'tooltip',
          content: earliestMatch.term,
          definition: earliestMatch.termInfo.definition
        });

        // Update remaining text
        remainingText = remainingText.substring(earliestMatch.index + earliestMatch.term.length);
      } else {
        // No more matches, add remaining text
        segments.push({ type: 'text', content: remainingText });
        break;
      }
    }

    // If no segments were created, return the original text
    if (segments.length === 0) {
      segments.push({ type: 'text', content: text });
    }

    return segments;
  }, [text, terms, isLoading]);

  // Render the processed content
  return (
    <span className={className}>
      {processedContent.map((segment, index) => {
        if (segment.type === 'tooltip' && segment.definition) {
          return (
            <Tooltip key={`tooltip-${index}`} text={segment.definition}>
              <span className="border-b border-dotted border-gray-400 dark:border-gray-600 cursor-help">
                {segment.content}
              </span>
            </Tooltip>
          );
        } else {
          return (
            <span key={`text-${index}`}>
              {segment.content}
            </span>
          );
        }
      })}
    </span>
  );
}
