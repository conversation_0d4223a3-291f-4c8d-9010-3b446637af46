'use client';

import { useState } from 'react';
import CollapsiblePaperCard from '@/components/CollapsiblePaperCard';
import GlossaryEnhancedContent from '@/components/GlossaryEnhancedContent';

export default function TestTooltips() {
  const [testText] = useState(`
# Tooltip Test Page

This page is designed to test the glossary tooltip functionality to ensure that partial word matching is fixed.

## Test Cases

### Case 1: CoD vs Code
- **CoD** should show a tooltip for "Chain of Density"
- **Code** should NOT show a tooltip for the first 3 letters
- **Coding** should NOT show a tooltip for the first 3 letters
- **Decode** should NOT show a tooltip for the last 3 letters

### Case 2: Other Terms
- **Chain of Thought** should show a tooltip
- **CoT** should show a tooltip for "Chain of Thought"
- **Transformer** should show a tooltip
- **AI** should show a tooltip

### Case 3: Edge Cases
- Words with punctuation: CoD, CoT. AI! Transformer?
- Words in sentences: "The CoD method improves summarization quality."
- Mixed case: cod, COD, Cod should all work for CoD
- Code generation and CoD are different concepts.

### Case 4: Real Content
The Chain of Density (CoD) prompting technique is used for text summarization. Unlike traditional code generation approaches, CoD focuses on iteratively increasing entity density. When writing code, developers often use Chain of Thought (CoT) reasoning, but CoD is specifically designed for summarization tasks.

This approach differs from standard coding practices and demonstrates how AI models can be prompted to produce more information-dense summaries without increasing length.

### Case 5: Paper Titles (Testing CollapsiblePaperCard)
- **Adaptive Temperature Sampling** - should show tooltips for relevant terms
- **Chain of Density Prompting** - should show CoD tooltip
- **AI-Powered Code Generation** - should show AI tooltip but not partial matches

### Case 6: Methods and Results Content
Testing content that would appear in expanded cards:
- The **CoD** method improves summarization by increasing entity density
- **AI** systems can benefit from adaptive temperature sampling
- **Chain of Thought** reasoning helps with complex problems
- Code generation differs from CoD summarization techniques
  `);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-zinc-900 font-roboto">
      {/* Header */}
      <div className="bg-white dark:bg-zinc-900 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
            Tooltip Testing Page
          </h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            This page tests the glossary tooltip functionality to ensure proper word boundary matching.
          </p>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="bg-white dark:bg-zinc-900 rounded-lg shadow-sm p-8 border border-gray-200 dark:border-gray-700">
          <div className="prose dark:prose-invert max-w-none">
            {/* Split the text into paragraphs and render each one */}
            {testText.split('\n').map((line, index) => {
              if (line.trim() === '') {
                return <br key={index} />;
              }

              if (line.startsWith('# ')) {
                return (
                  <h1 key={index} className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">
                    {line.substring(2)}
                  </h1>
                );
              }

              if (line.startsWith('## ')) {
                return (
                  <h2 key={index} className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-3 mt-6">
                    {line.substring(3)}
                  </h2>
                );
              }

              if (line.startsWith('### ')) {
                return (
                  <h3 key={index} className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2 mt-4">
                    {line.substring(4)}
                  </h3>
                );
              }

              if (line.startsWith('- ')) {
                return (
                  <li key={index} className="text-gray-700 dark:text-gray-300 mb-1">
                    {line.substring(2)}
                  </li>
                );
              }

              return (
                <p key={index} className="text-gray-700 dark:text-gray-300 mb-4">
                  {line}
                </p>
              );
            })}
          </div>
        </div>

        {/* Test GlossaryEnhancedContent Component */}
        <div className="mt-8">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6">
            GlossaryEnhancedContent Tests (Results Page Style)
          </h2>
          <div className="bg-white dark:bg-zinc-900 rounded-lg shadow-sm p-6 border border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">
              Sample Results Content
            </h3>
            <GlossaryEnhancedContent
              content={`
## Claimed Outcomes

The **Chain of Density (CoD)** method significantly improves summarization quality by iteratively increasing entity density. This approach leverages **AI** models to generate more informative summaries.

## Benefits

- **Chain of Thought** reasoning enhances model performance
- **AI** systems can better understand context
- **CoD** provides structured summarization approach

## Technical Details

The **Transformer** architecture enables effective processing of sequential data. When combined with **CoT** prompting, models show improved reasoning capabilities.

**Code** generation differs from **CoD** summarization - this should NOT show tooltips for "Cod" within "Code".
              `}
              className="prose dark:prose-invert max-w-none [&_p]:text-gray-700 [&_p]:dark:text-gray-300 [&_p]:mb-4 [&_ul]:list-disc [&_ul]:pl-5 [&_ul]:mb-4 [&_ul]:text-gray-700 [&_ul]:dark:text-gray-300 [&_li]:mb-1 [&_li]:text-gray-700 [&_li]:dark:text-gray-300 [&_strong]:font-semibold [&_strong]:text-gray-800 [&_strong]:dark:text-gray-100"
            />
          </div>
        </div>

        {/* Test CollapsiblePaperCard Components */}
        <div className="mt-8">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6">
            CollapsiblePaperCard Title Tooltip Tests
          </h2>
          <div className="space-y-4">
            <CollapsiblePaperCard
              title="Adaptive Temperature Sampling for Code Generation"
              slug="adaptive-temperature-sampling"
              summary="This paper explores adaptive temperature sampling techniques."
            >
              <p className="text-gray-700 dark:text-gray-300">
                Content about adaptive temperature sampling and how it improves code generation quality.
              </p>
            </CollapsiblePaperCard>

            <CollapsiblePaperCard
              title="Chain of Density (CoD) Prompting for Summarization"
              slug="chain-of-density"
              summary="Research on CoD techniques for better summarization."
            >
              <p className="text-gray-700 dark:text-gray-300">
                The CoD method focuses on iteratively increasing entity density in summaries.
              </p>
            </CollapsiblePaperCard>

            <CollapsiblePaperCard
              title="AI-Powered Code Analysis and Generation"
              slug="ai-code-analysis"
              summary="How AI systems can analyze and generate code effectively."
            >
              <p className="text-gray-700 dark:text-gray-300">
                AI models can assist with code generation, but this differs from CoD summarization.
              </p>
            </CollapsiblePaperCard>
          </div>
        </div>

        {/* Instructions */}
        <div className="mt-8 bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6 border border-blue-200 dark:border-blue-800">
          <h3 className="text-lg font-medium text-blue-900 dark:text-blue-100 mb-2">
            Testing Instructions
          </h3>
          <ul className="text-blue-800 dark:text-blue-200 space-y-1">
            <li>• Hover over "CoD" - should show "Chain of Density" tooltip</li>
            <li>• Hover over "Code" - should NOT show any tooltip</li>
            <li>• Hover over "Coding" - should NOT show any tooltip</li>
            <li>• Hover over "CoT" - should show "Chain of Thought" tooltip</li>
            <li>• Hover over "AI" - should show appropriate tooltip</li>
            <li>• Check that partial matches within words are ignored</li>
            <li>• Test card titles: "Adaptive Temperature Sampling", "CoD", "AI" should show tooltips</li>
            <li>• Expand cards to test content tooltips</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
