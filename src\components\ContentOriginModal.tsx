'use client';

import { useState, useEffect } from 'react';
import { SparklesIcon, CheckCircleIcon } from '@heroicons/react/24/outline';

const MODAL_SESSION_DISMISSED_KEY = 'contentOriginModalSessionDismissed_v1';
const MODAL_PERMANENTLY_DISMISSED_KEY = 'contentOriginModalPermanentlyDismissed_v1';

export default function ContentOriginModal() {
  const [isOpen, setIsOpen] = useState(false);
  const [dontShowAgain, setDontShowAgain] = useState(false);

  useEffect(() => {
    const permanentlyDismissed = localStorage.getItem(MODAL_PERMANENTLY_DISMISSED_KEY);
    if (permanentlyDismissed === 'true') {
      setIsOpen(false);
      return;
    }

    const sessionDismissed = sessionStorage.getItem(MODAL_SESSION_DISMISSED_KEY);
    if (!sessionDismissed) {
      setIsOpen(true);
    }
  }, []);

  const handleDismiss = () => {
    if (dontShowAgain) {
      localStorage.setItem(MODAL_PERMANENTLY_DISMISSED_KEY, 'true');
    } else {
      sessionStorage.setItem(MODAL_SESSION_DISMISSED_KEY, 'true');
    }
    setIsOpen(false);
  };

  if (!isOpen) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-zinc-800 p-6 sm:p-8 rounded-xl shadow-2xl max-w-lg w-full border border-gray-200 dark:border-zinc-700">
        <div className="flex items-center mb-4">
          <CheckCircleIcon className="h-8 w-8 text-sky-500 mr-3" />
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Content Transparency</h2>
        </div>
        <div className="space-y-4 text-gray-700 dark:text-gray-300">
          <p>
            This website features both human-authored and AI-assisted content to provide a comprehensive narrative.
            We believe in transparency, so here&apos;s how you can distinguish them:
          </p>
          <ul className="space-y-3 pl-1">
            <li className="flex items-start">
              <span className="font-merriweather mr-2 text-sky-600 dark:text-sky-400 font-semibold w-28 shrink-0">Human Content:</span> 
              <span className="font-merriweather">
                Displayed in the <strong className="font-semibold">Merriweather</strong> font. This is content written directly by the author.
              </span>
            </li>
            <li className="flex items-start">
              <span className="font-roboto mr-2 text-purple-600 dark:text-purple-400 font-semibold w-28 shrink-0">AI-Assisted:</span> 
              <span className="font-roboto">
                Displayed in the <strong className="font-semibold">Roboto</strong> font. This content involved AI in its generation or processing, such as literature reviews or data synthesis.
              </span>
            </li>
          </ul>
          <p>
            Additionally, AI-assisted sections and navigation links are marked with a <SparklesIcon className="inline-block h-5 w-5 text-amber-500 mx-1" /> icon.
          </p>
        </div>
        <div className="mt-6 mb-6">
          <label htmlFor="dont-show-again" className="flex items-center text-sm text-gray-600 dark:text-gray-400 cursor-pointer">
            <input 
              type="checkbox" 
              id="dont-show-again"
              checked={dontShowAgain}
              onChange={(e) => setDontShowAgain(e.target.checked)}
              className="h-4 w-4 text-sky-600 border-gray-300 rounded focus:ring-sky-500 dark:border-zinc-600 dark:bg-zinc-700 dark:focus:ring-sky-600 dark:focus:ring-offset-zinc-800 mr-2"
            />
            Don&apos;t show this message again
          </label>
        </div>

        <button
          onClick={handleDismiss}
          className="w-full bg-sky-600 hover:bg-sky-700 text-white font-semibold py-3 px-4 rounded-lg transition duration-150 ease-in-out focus:outline-none focus:ring-2 focus:ring-sky-500 focus:ring-offset-2 dark:focus:ring-offset-zinc-800"
        >
          Acknowledge & Continue
        </button>
      </div>
    </div>
  );
}
