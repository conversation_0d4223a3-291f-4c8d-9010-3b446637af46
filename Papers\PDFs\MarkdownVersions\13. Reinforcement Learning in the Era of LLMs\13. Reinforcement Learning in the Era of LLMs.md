Reinforcement Learning in the Era of LLMs: What is Essential? What is needed? An RL Perspective on RLHF, Prompting, and Beyond.

> Hao Sun Department of Applied Mathematics and Theoretical Physics University <NAME_EMAIL>

> > Abstract

Recent advancements in Large Language Models (LLMs) have garnered wide attention and led to successful products such as ChatGPT and GPT-4. Their proficiency in adhering to instructions and delivering harmless, helpful, and honest (3H) responses can largely be attributed to the technique of Reinforcement Learning from Human Feedback (RLHF). In this paper, we aim to link the research in conventional RL to RL techniques used in LLM research. Demystify this technique by discussing why, when, and how RL excels. Furthermore, we explore potential future avenues that could either benefit from or contribute to RLHF research.

2. RLHF > SFT because Imitation Learning (and Inverse RL) > Behavior

3. The RM step in RLHF generates a proxy of the expensive human feedback, such an insight can be generalized to other LLM tasks such as *prompting*

4. The policy learning in RLHF is more challenging than conventional problems studied in IRL due to their high action dimensionality and feedback sparsity. 5. The main superiority of PPO over off-policy value-based methods is its stability gained from (almost) on-policy data and conservative policy updates.

1. RLHF is Online Inverse RL with Offline Demonstration Data.

Cloning (BC) by alleviating the problem of compounding error.

evaluation and optimization where feedback is also expensive.

1 A Crash Introduction to RL: Online RL, Offline RL, and Inverse RL

background, can grasp the intricacies of RL and its impact on Large Language Models.

actions, that maximizes the expected cumulative reward over time.

about the *dynamics model* and the *reward function* .

In this section, we will briefly introduce some basic concepts needed in our discussion later. We begin by highlighting the important intuitions behind the technique of Reinforcement Learning (RL), followed by a more technical formalism. Our goal is to ensure everyone, regardless of their

In RL, an agent learns through interacting with an environment and receiving feedback in the form of rewards. The fundamental objective of RL is to find a policy, which is a mapping from states to

• (Environment = + .) When we are talking about an *Environment* we are talking

*Highlighted Takeaways*:

arXiv:2310.06147v1 [cs.LG] 9 Oct 2023

1.1 Essential Concepts

Preliminary Work.

Here are several useful concepts:

• (Agent ) An *Agent* is the subject of a *policy* that interacts with the environment. In a sequential decision-making problem, there can be multiple decision steps, and a smart policy will make its decision at every step by considering every piece of information it can collect

• (Difficulties ) Why is it hard to learn? 1. the learning objective is non-differentiable, it engages the unknown environment. 2. the policy needs to trade off between exploring *random* novel behaviors that potentially can be better than the current, yet as those are random behaviors, they are usually worse than the current — you may imagine how hard it would be for the LLM generation tasks when there are 10k tokens (as action) to choose

• (Learning ) The key insight behind the *learning* step in RL is to increase the probability of executing the *good* actions (which leads to a high cumulative future reward) and decrease the probability of executing *bad* actions (which have a low cumulative future reward). An easy-to-follow approach can be performing supervised learning on a collected set of good actions. e.g., Using Supervised Learning to mimic successful trajectories as an alternative

RL can be formally represented using the Markov Decision Processes (MDPs), where decisions are made in discrete time steps, and each decision affects the state of the environment in the subsequent

space, A is the action space. Broadly, the environment includes T and R, the former denotes the transition dynamics T : S × A 7→ ∆(S) that controls transitions between states, and the reward function R : S × A 7→ R provides feedback. In the most common settings, we assume the feedback is a scalar, yet in risk-sensitive or cost-sensitive settings, the reward function can be a vector, where constrained optimization techniques can be applied [5, 6]. ρ0 = p(s0) ∈ ∆(S) denotes the initial state distribution. γ is the discount factor that trades off between short-term and long-term returns.

Figure 1: A pictorial illustration of RL: an agent interacts with the environment and learns from trial and error.

In the *Online RL* setting, an agent with policy π ∈ Π : S 7→ ∆(A) learns through trial and error. It actively interacts with the environments — including both transition dynamics T and the reward

At each time step t, an agent observes a state st from the environment and selects an action at ∼ π. Upon taking the action, the agent receives a reward rt and transit to a new state st+1. The agent's

Eat∼π,st+1∼T ,s0∼ρ0

We can alternatively denote the trajectory generated by a policy π to be τ = {s0, a0 ∼ π(a0|s0), s1 ∼

2

X T

t=0 γ

t=0π(at|st)T (st+1|st, at), (2)

d denotes the d-dim state

tR(st, at), (1)

Formally, we denote the MDP as M = {S, A, T , R, ρ0, γ}, where S ⊂ R

till then. e.g., using recurrent networks to record histories in [1, 2]

from ...

approach to RL [3, 4].

1.2 Technical Formumation

1.2.1 Markov Decision Processes

step.

1.2.2 Online RL

function R.

objective is to maximize its expected return.

π

∗ = arg max π∈Π

T (s1|s0, a0), a1 ∼ π(a1|s1), ...} and denote the trajectory distribution of π as

T

pπ(τ ) = ρ0Π

where T denotes the length of decision sequences. The learning objective can be expressed as

Eτ∼pπ(τ)

In the *Offline RL* setting, interactions with the environment are strictly forbidden. The learning problem is no longer online learning but learning from a static dataset of decision logs DOff−RL =

The most obvious difficulty in the offline RL setting is such a setting prohibits exploration — hence it hinders the improvement of policy learning to be improved over the demonstration data (though

Another fundamental challenge is the *distributional shift*: although offline RL learns from a static dataset, its evaluation is actually based on rolling out a policy in an environment — this is different from the ordinary supervised learning settings where the training set and test set are sampled from the same distribution. In offline RL training, the state distribution is sampled from rolling out the behavior policy πβ, whereas in its evaluation, the state distribution is sampled from rolling out the

To be more specific, assuming the decision dataset is collected from an optimal behavior policy π

the expected number of mistakes made by the learned policy π based on such an expert decision

"X T

t=0

Theorem 1.1 (Behavior Clone Error Bound. Ross et al. [7]). *If* π *is trained via empirical risk*

*Remark* 1.2 (Compounding Error.)*.* An intuitive interpretation of this quadratic relationship between the error bound and the generalization error is that those errors aggregate along the trajectory. i.e., whenever the learned policy makes a mistake, it tends to make more mistakes from then on as that action is not optimal and will lead to other out-of-distribution states, which will lead to further

*Remark* 1.3 (Behavior Clone)*.* We can always set up a supervised learning objective in offline RL to

Figure 2: In Offline RL, a behavior policy interacts with the environment and generates a decision dataset. Then

In order to alleviate the challenge of compounding error we discussed above, *Imitation Learning* (IL)

3

such a decision dataset is used to learn a policy without access to the environment (offline).

considers the setting where a dynamics model is available during learning.

∗ t

is optimal. We denote the state-action pairs in the dataset as (st, a∗

1(π(st) ̸= a

∗ t ) #

ϵ *is the best possible bound on the expected error of the learned policy.*

*, and attains generalization error* ϵ *on* st ∼

)}, that is generated by some unknown behavior policy πβ.

"X T

t=0 γ tR(st, at)

#

. (3)

∗ β ,

(4)

(5)

t ), then

π

i t

2

minimize the difference between decision demonstration pairs. i.e.,

π = arg min π E(s i t ,ai t )∼D||a i t − π(s i t )||2

1.2.3 Offline RL

learned policy π.

such that every decision a

dataset can be denoted as

*minimization on* st ∼ pπβ

1.2.4 Imitation Learning

(τ )*, then* ℓ(π) ≤ C + T

pπβ

mistakes.

Then we have the following theorems:

{(s i t , ai t , si t+1, ri t ∗ = arg max π

sometimes the learned policy can be better than the demonstration).

ℓ(π) = Epπ(τ)

(τ ) *and optimal labels* a

Another Motivation of IL: Reward Design is Hard The setup of IL is especially common for problems where reward engineering is hard. This is because although the "reward hypothesis" tells us whenever we can define a reward function for a task, it can be solved by RL, it does not consider whether this task can be efficiently solved. For instance, in playing Go or StarCraft, it's easy to define a reward function that returns +1 when winning and 0 when losing. However, it will not be hard to imagine that such a reward function is extremely sparse to provide helpful information during learning. In another example of teaching robots to finish complex tasks, imitation can also circumvent

A Method for Reward Engineering In a previous paper [9], we show and illustrate why using a 0 for win and −1 for lose is better than using +1/0. A simple reward shifting with a few lines of code added to the RL reward function can be used to improve exploration (for Online RL) or enhance

To alleviate the challenge of reward engineering in RL tasks, IL is introduced to learn to use the dynamics model but without a pre-defined reward model. Consider those examples: (1) in learning humanoid robotics locomotion skills, it is hard to define an objective to let the robot "walk as a human" — however, providing demonstration data to show how humans walk is much easier. (2) in autonomous driving, it is hard to define the objective of "driving safe and well" — however, we should be able to provide human driving videos or control sequences as demonstrations of good and

The objective of IL is to learn from a (decision) demonstration dataset, with access to a dynamics model — such that the current policy can be rolled out in the real environment. Intuitively, with such

Figure 3: In Imitation Learning (IL), the agent learns from feedback from the decision dataset, but the

There are many practical methods for implementing such a learning process, and the most famous work in the Deep-RL era is the GAIL [10], which conducts IL through adversarial learning: the policy is a *generator* of behaviors, while a *discriminator* then tries to identify whether a trajectory is

Theorem 1.4 (DAgger Error Bound, Ross et al. [7]). *If* π *is trained via empirical risk minimization*

*Remark* 1.5*.* This requires the additional assumption of being able to access the behavior (expert)

Takeaway: Comparing Theorem 1.1 and Theorem 1.4, we see that having access to a *dynamics*

Inverse reinforcement learning (IRL) is actually just one of the many solutions to IL problems, with an emphasis on reward model learning. It first learns a reward model, and then uses such a reward

Offline IL and Offline IRL: What if both the reward model and dynamics model are not available? This situation is clearly more challenging. The demonstration dataset in such settings will be in the

4

*, and attains generalization error* ϵ *on* st ∼ pπ(τ )*, then*

(τ ) but could be st ∼ pπ(τ )

the difficulty of describing a motion sequence with a reward function [8].

a dynamics model, the optimization objective will no longer be st ∼ pπβ

generated by the behavior policy πβ or by the generator (the policy learned).

∗ t

model — combined with the dynamics model — to perform online RL.

ℓ(π) ≤ C + T ϵ *is the best possible bound on the expected error of the learned policy.*

policy πβ actively to acquire the expert for those roll-out trajectories generated by π .

— the distributional shift problem can be alleviated.

observations are from a real dynamics model.

*on* st ∼ pπ(τ ) *and optimal labels* a

1.2.5 Inverse Reinforcement Learning

For the theory results, we have the following theorem:

*model* is essential in controlling the error bound.

conservative exploitation (for Offline RL).

safe driving behaviors.

Figure 4: Inverse Reinforcement Learning (IRL) solves the IL tasks in two steps: (1). reward modeling that distills the knowledge of underlying learning objectives that the behavior policy seems to optimize from the offline decision demonstration dataset. (2). combining such a learned reward model and the accessible dynamics

approaches like the energy-based method SBIL [11], and the latent space decomposition method ABC [12]. ABC can be regarded as an accountable counterpart of BC, therefore, it works in all

Another related but different topic is Learning from Demonstrations (LfD) [13–15], which leverages the demonstration dataset as a warm-start for RL. For instance, in the aforementioned tasks of Go or StarCraft, we can first use the demonstration dataset to perform behavior cloning (BC) and then use the learned BC policy as a warm start for RL. LfD also benefits the exploration of robotics control tasks where the reward can be extremely sparse, and defined as "whether the goal is achieved". In a nutshell, LfD uses demonstrations to improve exploration in reward sparse tasks, and those demonstrations may not be optimal (e.g., non-expert players' replay of StarCraft [16]), LfD then returns to RL and a sparse reward function to further refine the policy learned from demonstration

The table below summarizes the differences between RL, Offline-RL, IL, IRL, Offline-IRL, and LfD.

Table 1: Summarization of the differences between RL, Offline-RL, IL, IRL, Offline-IRL, and LfD.

RL PPO [17], TD3 [18],SAC [19] Offline-RL or BC, ABC [12], CQL [20], WGCSL [21] IL or BC, ABC [12], GAIL [10] IRL BC, ABC [12], T-REX [22] Offline-IRL BC, ABC [12], SBIL [11] LfD DQNfD [14], DDPGfD [15], AlphaStar [16]

2 RLHF: Solving the Problem of Offline RL with Online Inverse RL

In the task of LLM alignment from human feedback, LLMs are fine-tuned to better follow user instructions. In the seminal paper of OpenAI [23], such an alignment includes two general parts: supervised fine-tuning (SFT) and reinforcement learning from human feedback (RLHF). Figure 5 below illustrates those concrete steps. The first part of SFT is relatively easy to follow and implement,

5

Problem External External Learned (Near)-Expert Example Settings Dynamics Reward Reward Demonstration Solvers

t+1)}. Besides the behavior cloning method, there are several alternative

model, everything needed for an online RL algorithm is right there.

form of DOIL = {(s

dataset.

i t , ai t , si

1.2.6 Learning from Demonstrations

1.2.7 Comparison Between Different Settings

Model Model Model

2.1 LLM Alignment from Human Feedback

yet the secret and insight behind RLHF are more intricate.

settings where BC can be applied.

Figure 5: (From Ouyang et al. [23]) There are 3 steps to align LLMs to human preference. Step 1: supervised fine-tuning of pre-trained LLM to follow instructions (generated by human demonstration data). Step 2: sample multiple responses for every query, and rank those responses according to human preference. Then a reward model can be learned to mimic the human preference. Step 3: Optimize the language model through RL to

Ideally, the RLHF phase can be conducted with human-in-the-loop, as shown in Figure 6. In such an online setting, Human provides feedback to every response of LLMs, and LLMs learn from the external reward model of human preference. In fact, OpenAI now should be able to conduct such a process by collecting user's feedback on ChatGPT's response. But usually, such an online setting is

Figure 6: RLHF as an online RLproblem: Human preference is the underlying reward model, however, querying

Practically, RLHF addresses such a difficulty by generating an *offline alignment dataset* that contains different queries (i.e., states s in RL), responses (i.e., trajectory τ in RL), and preferences provided by human annotators (i.e., reward r in RL). From such a perspective, the RLHF may seem to be a natural online RL problem but adjusted into *an offline RL problem* due to cost considerations. Figure 7

Recall the problems of distributional shift and compounding error we discussed above in Offline RL, it seems RLHF must suffer from such problems. However, we show in the next section that RLHF

The essential observation we would highlight in RLHF is that *the dynamics model in response generation is known.* Specifically, harking back to Figure 6, the actions are tokens generated by LLMs, and the responses (trajectories) are concatenations of those generated tokens. Given the

6

can actually be solved as an Online IL problem, rather than an offline RL problem.

2.2 Aligning with Human Preference: the Online Nature and Offline Practice

maximize the feedback from the reward model

infeasible due to its high cost of keeping humans in the loop.

humans to provide feedback on every response is usually infeasible.

2.3 RLHF: From Offline-RL to Online Imitation

illustrates such a generation process.

Figure 7: Because of the high cost of keeping humans in the loop, the practice of RLHF considers learning with an offline dataset generated by interactions between (the SFT) LLMs and Human annotators. The generated

auto-regression nature of LLMs' generation process, given an initial query denoted as s0, we can

• s0 ∼ p(s0): (*Interpretation:*) sample from query distribution | (*RL Language:*) sample from

• a0 ∼ ℓ(s0): (*Interpretation:*) sample the next token with ℓ | (*RL Language:*) sample action

• s1 = T (s0, a0) = Concat(s0, a0): (*Interpretation:*) concatenate the generated token and the query as input for LLM for the next token generation | (*RL Language:*) the transition

Figure 8 showcases why the LLM alignment task can be solved as an online IL in practice (c.f.

Figure 8: When aligning LLMs using an offline dataset, the dynamics model is a concatenation of the generated

Practically, RLHF chooses to use the Inverse RL approach for the IL problem — with the first step explicitly learning a reward model, and the second step conducting RL using such a reward model.

Figure 9: RLHF is online IRL. The reward modeling step learns a reward function from the alignment dataset, and given such a reward model and the known transition dynamics (concatenation), online RL algorithms like

Takeaway: When aligning LLMs with an *offline human-preference alignment dataset*, RLHF uses an *online IRL* approach. This is because the transition dynamics model is known. Leveraging such a property, the compounding error and distributional shift problems of offline RL can be

7

token and existing tokens, therefore, the offline RL problem can be solved by online IL.

formally write the trajectory generation process of an LLM ℓ as follows:

offline dataset is then used for LLM alignment.

initial state distribution

dynamics gives the next state

from the policy

• a1 ∼ ℓ(s1): ...

Figure 3: pictorial illustration of IL)

Figure 9 illustrates the learning procedure.

PPO can then be applied.

alleviated.

• ...

2.4 Challenges and Open Questions from an RL Perspective

Given the discussions above, the reason RLHF can be better than SFT — from an RL perspective is that RLHF leverages the fact that the dynamics model is known, and uses IL to solve the alignment problem. On the other hand, SFT corresponds to the behavior clone approach, which suffers from the

Recently, several works have proposed alternatives to RLHF, including DPO [24] that directly optimizes the LLM using human-preference data without reward modeling, RRHF [25] and RAFT [26] propose ranking-based sampling methods as alternatives to PPO to address the computational insta-

So clearly, there is still a lot of space for future improvement over PPO. We would like to mention

1. PPO works well in large-scale discrete tasks [16]. The action space of LLM is far larger

2. PPO has a faster wall-clock training time compared to off-policy methods like DQN [27]. PPO can be highly environment-parallelized. In fact, this is normally an implementation problem: in DQN a higher Update-to-Data (UTD) ratio [28] is used: updates of networks in DQN are conducted every time step, but in PPO the updates of networks only happen at the

3. Aligning to human preference is a sparse reward problem. In the sense that only at the end of an episode will the agent receive a reward signal (provided by human feedback or the learned reward function). Such a setting is relevant to the multi-goal robotics control tasks [29] where the idea of Hindsight learning shines with the value-based methods [30, 3] — rather than policy-based methods like TRPO [31] and PPO. There are several attempts

4. A fun fact is that policy-gradient and value-based methods are almost equivalent [34]. But in practice, the studies on LLM finetuning now mainly focus on the on-policy policy-based methods. The performance differences between policy-based methods and value-based methods can be mainly attributed to (1). on-policy/ off-policy data — the staleness of the data they used for value and policy learning; and (2). whether using an aggressive and explicit or conservative and implicit policy learning — while the policy-gradient methods like PPO and TRPO use a value function *implicitly* for policy learning (i.e., use them as *critics* to calculate policy gradient values that improve policy quality), the value-based methods like TD3 and SAC *explicitly* turns the learned value function into policies (i.e., through either deterministic policy gradient DPG [35] in TD3 or the Boltzmann policy [36]

1. Credit Assignment: The preference provided by humans is on a trajectory level. Hence the learned reward model can only compare responses on an entire level. Is there a way to assign credit to different tokens or part of tokens? A known fact in RL is dense reward problems are much easier to learn, though they do not necessarily outperform the sparse reward settings. [29] (because of local minima, again, a reward engineering problem) 2. Algorithmic Design: RL algorithms are seldom designed in a way that assumes knowing the dynamics model. But in LLM alignment, the actions are actually generated in an autoregressive manner. Is there a more efficient and stable RL algorithm that works better than

PPO in such a *series generation* setting? This is a sort of Auto-Regressive MDP.

8

3. Prompting: Is the prompting strategy optimized? Maybe the prompting strategy is not correct in getting the desired answer. Prompt optimization can definitely help improve the performance of LLMs. *To address such a point, we introduce recent work on querydependent prompt optimization [38] in the next section, which also links RL and LLM.*

problem of compounding error. Therefore, as long as IL > BC, we have RLHF > SFT.

the following pros and cons (mostly based on empirical observations) of PPO:

end of an episode. e.g., after the entire response is generated.

using the Hindsight relabeling trick for LLM fine-tuning [32, 33].

2.4.1 Why is RLHF better than SFT?

2.4.2 Is PPO the Only Solution?

bility issue and high GPU memory demand of PPO.

than normal RL problems.

as in SAC/soft Q-learning [37].)

2.4.3 What to Improve?

3 Prompting with Offline IRL: Prompt Optimization is RL from AI Feedback

Figure 10: (From Sun [38].) A motivating example (left, right). No prompt is perfect that works for all queries. The optimal prompt is query-dependent. Yet the seeking of such prompts is hindered by 2 challenges. Prompt-OIRL [38] optimizes prompt during inference on a query-dependent level effectively and cost-efficiently.

Out of the many attempts, *prompting* — a natural language prefix or instruction that explains how to complete the task — stands out as a lightweight promising solution for eliciting the capabilities of LLMs without model parameter tuning. While the advances in zero-shot prompting strategies highlight the potential for finding effective query-independent solutions, its reliance on manual crafting efforts and the vast search space over natural language intensifies the difficulty in discovering effective prompts. Moreover, as demonstrated in Figure 10, the optimal prompt is query dependent —

Prompt-OIRL is a novel approach grounded in offline inverse reinforcement learning, designed to reconcile effective and cost-efficient query-dependent prompt evaluation and optimization. This method leverages offline datasets from existing evaluations, utilizing Inverse-RL to craft a reward model tailored for offline, query-specific prompt evaluations. Prompt-OIRL offers several benefits: it forecasts prompt efficacy, minimizes costs, and explores the prompt space more effectively — all at a query-dependent level. We validate our approach across various LLMs and arithmetic reasoning datasets, underscoring its viability as a formidable solution for query-dependent offline prompt

While Prompt-OIRL primarily centers on arithmetic reasoning tasks, we wish to underscore the versatility of Prompt-OIRL's insights for broader applications, especially where there exists a prompting demonstration dataset accompanied by ratings of the prompted responses. As a hypothetical approach to dataset construction with human annotators incorporated into the process, consider this: human annotators could employ LLMs to accomplish specific tasks. They might offer multiple prompts as instructions for the task, and the ensuing LLM responses can then be graded based on proficiency in executing the given task. In fact, these annotators could be everyday LLM users keen on evaluating

[1] Hao Sun, Ziping Xu, Meng Fang, Zhenghao Peng, Jiadong Guo, Bo Dai, and Bolei Zhou. Safe exploration by solving early terminated mdp. *arXiv preprint arXiv:2107.04200*, 2021. [2] Tianwei Ni, Benjamin Eysenbach, and Ruslan Salakhutdinov. Recurrent model-free rl can be a

[3] Hao Sun, Zhizhong Li, Xiaotong Liu, Bolei Zhou, and Dahua Lin. Policy continuation with hindsight inverse dynamics. *Advances in Neural Information Processing Systems*, 32, 2019. [4] Hao Sun, Ziping Xu, Yuhang Song, Meng Fang, Jiechao Xiong, Bo Dai, and Bolei Zhou. Zeroth-order supervised policy improvement. *arXiv preprint arXiv:2006.06600*, 2020.

9

diverse responses. We earmark this intriguing concept for subsequent exploration.

strong baseline for many pomdps. *arXiv preprint arXiv:2110.05038*, 2021.

3.2 Prompt-OIRL: Prompt Evaluation and Optimization with Offline Inverse RL

3.1 The Query-Dependent Prompting Problem

there is no perfect prompt that works for all queries.

evaluation and optimization.

3.3 Potential Applications

References

[5] Hao Sun, Zhenghao Peng, Bo Dai, Jian Guo, Dahua Lin, and Bolei Zhou. Novel policy seeking

[6] Hao Sun, Ziping Xu, Zhenghao Peng, Meng Fang, Taiyi Wang, Bo Dai, and Bolei Zhou. Constrained mdps can be solved by eearly-termination with recurrent models. In *NeurIPS 2022*

[7] Stéphane Ross, Geoffrey Gordon, and Drew Bagnell. A reduction of imitation learning and structured prediction to no-regret online learning. In *Proceedings of the fourteenth international conference on artificial intelligence and statistics*, pages 627–635. JMLR Workshop and

[8] Xue Bin Peng, Pieter Abbeel, Sergey Levine, and Michiel Van de Panne. Deepmimic: Exampleguided deep reinforcement learning of physics-based character skills. *ACM Transactions On*

[9] Hao Sun, Lei Han, Rui Yang, Xiaoteng Ma, Jian Guo, and Bolei Zhou. Exploit reward shifting in value-based deep-rl: Optimistic curiosity-based exploration and conservative exploitation via linear reward shaping. *Advances in Neural Information Processing Systems*, 35:37719–37734,

[10] Jonathan Ho and Stefano Ermon. Generative adversarial imitation learning. *Advances in neural*

[11] Daniel Jarrett, Ioana Bica, and Mihaela van der Schaar. Strictly batch imitation learning by energy-based distribution matching. *Advances in Neural Information Processing Systems*, 33:

[12] Hao Sun, Alihan Hüyük, Daniel Jarrett, and Mihaela van der Schaar. Accountable batched control with decision corpus. *Advances in Neural Information Processing Systems*, 36, 2023.

[13] Stefan Schaal. Learning from demonstration. *Advances in neural information processing*

[14] Todd Hester, Matej Vecerik, Olivier Pietquin, Marc Lanctot, Tom Schaul, Bilal Piot, Dan Horgan, John Quan, Andrew Sendonaris, Ian Osband, et al. Deep q-learning from demonstrations. In

[15] Ashvin Nair, Bob McGrew, Marcin Andrychowicz, Wojciech Zaremba, and Pieter Abbeel. Overcoming exploration in reinforcement learning with demonstrations. In *2018 IEEE international*

[16] Oriol Vinyals, Igor Babuschkin, Wojciech M Czarnecki, Michaël Mathieu, Andrew Dudzik, Junyoung Chung, David H Choi, Richard Powell, Timo Ewalds, Petko Georgiev, et al. Grandmaster level in starcraft ii using multi-agent reinforcement learning. *Nature*, 575(7782):350–354, 2019.

[17] John Schulman, Filip Wolski, Prafulla Dhariwal, Alec Radford, and Oleg Klimov. Proximal

[18] Scott Fujimoto, Herke Hoof, and David Meger. Addressing function approximation error in actor-critic methods. In *International conference on machine learning*, pages 1587–1596.

[19] Tuomas Haarnoja, Aurick Zhou, Kristian Hartikainen, George Tucker, Sehoon Ha, Jie Tan, Vikash Kumar, Henry Zhu, Abhishek Gupta, Pieter Abbeel, et al. Soft actor-critic algorithms

[20] Aviral Kumar, Aurick Zhou, George Tucker, and Sergey Levine. Conservative q-learning for offline reinforcement learning. *Advances in Neural Information Processing Systems*, 33:

[21] Rui Yang, Yiming Lu, Wenzhe Li, Hao Sun, Meng Fang, Yali Du, Xiu Li, Lei Han, and Chongjie Zhang. Rethinking goal-conditioned supervised learning and its connection to offline rl. *arXiv*

10

*Proceedings of the AAAI conference on artificial intelligence*, volume 32, 2018.

*conference on robotics and automation (ICRA)*, pages 6292–6299. IEEE, 2018.

policy optimization algorithms. *arXiv preprint arXiv:1707.06347*, 2017.

and applications. *arXiv preprint arXiv:1812.05905*, 2018.

with constrained optimization. *arXiv preprint arXiv:2005.10696*, 2020.

*Foundation Models for Decision Making Workshop*, 2022.

Conference Proceedings, 2011.

2022.

7354–7365, 2020.

*systems*, 9, 1996.

PMLR, 2018.

1179–1191, 2020.

*preprint arXiv:2202.04478*, 2022.

*Graphics (TOG)*, 37(4):1–14, 2018.

*information processing systems*, 29, 2016.

[22] Daniel Brown, Wonjoon Goo, Prabhat Nagarajan, and Scott Niekum. Extrapolating beyond suboptimal demonstrations via inverse reinforcement learning from observations. In *International*

[23] Long Ouyang, Jeffrey Wu, Xu Jiang, Diogo Almeida, Carroll Wainwright, Pamela Mishkin, Chong Zhang, Sandhini Agarwal, Katarina Slama, Alex Ray, et al. Training language models to follow instructions with human feedback. *Advances in Neural Information Processing Systems*,

[24] Rafael Rafailov, Archit Sharma, Eric Mitchell, Stefano Ermon, Christopher D Manning, and Chelsea Finn. Direct preference optimization: Your language model is secretly a reward model.

[25] Zheng Yuan, Hongyi Yuan, Chuanqi Tan, Wei Wang, Songfang Huang, and Fei Huang. Rrhf: Rank responses to align language models with human feedback without tears. *arXiv preprint*

[26] Hanze Dong, Wei Xiong, Deepanshu Goyal, Rui Pan, Shizhe Diao, Jipeng Zhang, Kashun Shum, and Tong Zhang. Raft: Reward ranked finetuning for generative foundation model

[27] Volodymyr Mnih, Koray Kavukcuoglu, David Silver, Alex Graves, Ioannis Antonoglou, Daan Wierstra, and Martin Riedmiller. Playing atari with deep reinforcement learning. *arXiv preprint*

[28] Michael Janner, Justin Fu, Marvin Zhang, and Sergey Levine. When to trust your model: Model-based policy optimization. *Advances in neural information processing systems*, 32,

[29] Matthias Plappert, Marcin Andrychowicz, Alex Ray, Bob McGrew, Bowen Baker, Glenn Powell, Jonas Schneider, Josh Tobin, Maciek Chociej, Peter Welinder, et al. Multi-goal reinforcement learning: Challenging robotics environments and request for research. *arXiv*

[30] Marcin Andrychowicz, Filip Wolski, Alex Ray, Jonas Schneider, Rachel Fong, Peter Welinder, Bob McGrew, Josh Tobin, OpenAI Pieter Abbeel, and Wojciech Zaremba. Hindsight experience

[31] John Schulman, Sergey Levine, Pieter Abbeel, Michael Jordan, and Philipp Moritz. Trust region policy optimization. In *International conference on machine learning*, pages 1889–1897.

[32] Hao Liu, Carmelo Sferrazza, and Pieter Abbeel. Languages are rewards: Hindsight finetuning

[33] Tianjun Zhang, Fangchen Liu, Justin Wong, Pieter Abbeel, and Joseph E Gonzalez. The wisdom of hindsight makes language models better instruction followers. *arXiv preprint*

[34] John Schulman, Xi Chen, and Pieter Abbeel. Equivalence between policy gradients and soft

[35] David Silver, Guy Lever, Nicolas Heess, Thomas Degris, Daan Wierstra, and Martin Riedmiller. Deterministic policy gradient algorithms. In *International conference on machine learning*,

[36] Brendan O'Donoghue, Remi Munos, Koray Kavukcuoglu, and Volodymyr Mnih. Combining

[37] Tuomas Haarnoja, Haoran Tang, Pieter Abbeel, and Sergey Levine. Reinforcement learning with deep energy-based policies. In *International conference on machine learning*, pages

[38] Hao Sun. Offline prompt evaluation and optimization with inverse reinforcement learning.

11

policy gradient and q-learning. *arXiv preprint arXiv:1611.01626*, 2016.

replay. *Advances in neural information processing systems*, 30, 2017.

using human feedback. *arXiv preprint arXiv:2302.02676*, 2023.

q-learning. *arXiv preprint arXiv:1704.06440*, 2017.

*conference on machine learning*, pages 783–792. PMLR, 2019.

35:27730–27744, 2022.

*arXiv:2304.05302*, 2023.

*arXiv:1312.5602*, 2013.

*preprint arXiv:1802.09464*, 2018.

2019.

PMLR, 2015.

*arXiv:2302.05206*, 2023.

pages 387–395. Pmlr, 2014.

1352–1361. PMLR, 2017.

*arXiv preprint arXiv:2309.06553*, 2023.

*arXiv preprint arXiv:2305.18290*, 2023.

alignment. *arXiv preprint arXiv:2304.06767*, 2023.

