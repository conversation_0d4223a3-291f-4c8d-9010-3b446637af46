'use client';

import Link from 'next/link';
import { SparklesIcon, UserIcon, CogIcon, ArchiveBoxIcon } from '@heroicons/react/24/outline';

export default function CriticalReview() {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-zinc-900 font-merriweather">
      {/* Hero Section - Updated to match Home/Literature Analysis Banner Style */}
      <section className="relative isolate text-white pt-12 md:pt-16 pb-16 md:pb-20 bg-[url('/images/banner-bg.jpg')] bg-cover bg-center">
        {/* Standard Overlay */}
        <div className="absolute inset-0 bg-gradient-to-b from-sky-700/50 via-indigo-800/70 to-purple-900/90 dark:from-sky-700/60 dark:via-indigo-800/80 dark:to-purple-900/95 backdrop-blur-sm"></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          {/* Title Section (Existing Sizing Retained) */}
          <div className="text-center mb-10 md:mb-14 font-merriweather">
            <h1 className="text-4xl font-bold text-black dark:text-white mb-4">Critical Review</h1>
            <p className="text-xl text-black dark:text-white max-w-3xl mx-auto">
              A collection of research papers evaluated by AI.
            </p>
          </div>
        </div>

        {/* Process Steps Icons & Full-Width Line - Positioned at the bottom */}
        {/* The outer container's positioning (bottom-0, w-full) is relative to this section */}
        <div className="absolute bottom-0 left-0 right-0 w-full px-4 sm:px-6 lg:px-8 z-10"> 
          {/* REMOVED original connecting line from here, new one added at end of section */}
          
          {/* Container for the icons - adjusted to -mb-14 to center icons on the NEW line that will be below this div */}
          <div className="relative flex justify-between max-w-4xl mx-auto z-10 -mb-14"> 
            {/* Analysis Step */}
            <div className="flex flex-col items-center z-10">
              <div className="w-16 h-16 rounded-full bg-blue-700 dark:bg-blue-800 border-4 border-blue-400 flex items-center justify-center mb-3">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-blue-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
              </div>
              <span className="text-sm font-medium text-black dark:text-white font-sans">Analysis</span>
            </div>
            
            {/* Evaluation Step */}
            <div className="flex flex-col items-center z-10">
              <div className="w-16 h-16 rounded-full bg-purple-700 dark:bg-purple-800 border-4 border-purple-400 flex items-center justify-center mb-3">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-purple-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <span className="text-sm font-medium text-black dark:text-white font-sans">Evaluation</span>
            </div>
            
            {/* Integration Step */}
            <div className="flex flex-col items-center z-10">
              <div className="w-16 h-16 rounded-full bg-rose-700 dark:bg-rose-800 border-4 border-rose-400 flex items-center justify-center mb-3">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-rose-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
              </div>
              <span className="text-sm font-medium text-black dark:text-white font-sans">Integration</span>
            </div>
          </div>
        </div>
        {/* Bottom Gradient Bar - Tailored to icon colors */}
        <div className="absolute bottom-0 left-0 right-0 h-1 w-full bg-gradient-to-r from-blue-500 via-purple-500 to-red-500"></div>
      </section>

      {/* Main Page Content (Cards) - Add top padding to account for overlapping icons */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 md:py-16 pt-20 md:pt-24">
        {/* Main Sections Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Literature Analysis Section */}
          <Link 
            href="/critical-review/literature-analysis"
            className="group font-sans"
          >
            <div className="relative bg-white dark:bg-zinc-900 rounded-lg shadow-sm dark:shadow-zinc-800/30 p-8 h-full hover:shadow-md transition-shadow border border-gray-200 dark:border-gray-700">
              <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-500 to-purple-600 dark:from-blue-400 dark:to-purple-500 rounded-t-lg"></div>
              <div className="flex items-start justify-between">
                <div>
                  <h2 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-4 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors font-merriweather flex items-center">
                    <CogIcon className="h-6 w-6 mr-2 text-blue-500 dark:text-blue-400 flex-shrink-0" />
                    The Process
                    <UserIcon className="ml-2 h-5 w-5 text-yellow-500 dark:text-yellow-400 flex-shrink-0" />
                  </h2>
                  <p className="text-gray-600 dark:text-gray-300 mb-4 font-merriweather">
                    Here you will find the exact steps and prompt content used to evaluate each paper.
                  </p>
                  <ul className="text-gray-600 dark:text-gray-300 list-disc list-inside space-y-1 font-merriweather">
                    <li>Context Documents</li>
                    <li>Scoring Criteria</li>
                    <li>Methods Extraction</li>
                    <li>Results & Insights</li>
                    <li>JSON Evaluation Template</li>
                  </ul>
                </div>
                <span className="text-blue-600 group-hover:translate-x-1 transition-transform">→</span>
              </div>
            </div>
          </Link>

          {/* Source Material Section */}
          <Link 
            href="/critical-review/source-material"
            className="group font-sans"
          >
            <div className="relative bg-white dark:bg-zinc-900 rounded-lg shadow-sm dark:shadow-zinc-800/30 p-8 h-full hover:shadow-md transition-shadow border border-gray-200 dark:border-gray-700">
              <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-purple-600 to-red-500 dark:from-purple-500 dark:to-red-400 rounded-t-lg"></div>
              <div className="flex items-start justify-between">
                <div>
                  <h2 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-4 group-hover:text-red-600 dark:group-hover:text-red-400 transition-colors flex items-center">
                    <ArchiveBoxIcon className="h-6 w-6 mr-2 text-red-500 dark:text-red-400 flex-shrink-0" />
                    Source Material
                    <SparklesIcon className="ml-2 h-5 w-5 text-yellow-400 dark:text-yellow-300" />
                  </h2>
                  <p className="text-gray-600 dark:text-gray-300 mb-4 font-roboto">
                    Access the collection of research papers and their evaluations. <br /><br />
                  </p>
                  <ul className="text-gray-600 dark:text-gray-300 list-disc list-inside space-y-1 font-roboto">
                    <li>Browse and Sort Papers</li>
                    <li>View Original PDF and Markdown</li>
                    <li>Read Evaluation Results</li>
                    <li>View Detailed Scoring</li>
                    <li>See Raw JSON</li>
                  </ul>
                </div>
                <span className="text-red-600 group-hover:translate-x-1 transition-transform">→</span>
              </div>
            </div>
          </Link>
        </div>
      </div>
    </div>
  );
}
