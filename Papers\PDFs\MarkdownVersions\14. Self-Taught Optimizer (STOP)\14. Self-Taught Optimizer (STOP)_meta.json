{"table_of_contents": [], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 135], ["Line", 61], ["Text", 24]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 435], ["Line", 65], ["Text", 27]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 224], ["Line", 68], ["Text", 22]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 568], ["Line", 73], ["Text", 55]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 304], ["Line", 77], ["Text", 30]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 246], ["Line", 65], ["Text", 14]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 728], ["Line", 84], ["Text", 50]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 177], ["Line", 53], ["Text", 11]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 183], ["Line", 52], ["Text", 19]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 130], ["Line", 55], ["Text", 14]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 147], ["Line", 49], ["Text", 33]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 156], ["Line", 51], ["Text", 30]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 160], ["Line", 50], ["Text", 30]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 180], ["Line", 50], ["Text", 33]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 128], ["Line", 44], ["Text", 26]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 133], ["Line", 29], ["Text", 17]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 306], ["Line", 61], ["Text", 39]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 667], ["Line", 88], ["Text", 43]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 531], ["Line", 102], ["Text", 45]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 700], ["Line", 80], ["Text", 61]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 661], ["Line", 84], ["Text", 54]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 504], ["Line", 63], ["Text", 55]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 515], ["Line", 56], ["Text", 44]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 590], ["Line", 88], ["Text", 72]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 475], ["Line", 57], ["Text", 43]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 529], ["Line", 61], ["Text", 48]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 640], ["Line", 78], ["Text", 53]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 27, "text_extraction_method": "pdftext", "block_counts": [["Span", 650], ["Line", 84], ["Text", 62]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 28, "text_extraction_method": "pdftext", "block_counts": [["Span", 458], ["Line", 58], ["Text", 40]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 29, "text_extraction_method": "pdftext", "block_counts": [["Span", 429], ["Line", 44], ["Text", 32]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 30, "text_extraction_method": "pdftext", "block_counts": [["Span", 35], ["Line", 16], ["Text", 5]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 31, "text_extraction_method": "pdftext", "block_counts": [["Span", 341], ["Line", 41], ["Text", 34]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 32, "text_extraction_method": "pdftext", "block_counts": [["Span", 354], ["Line", 39], ["Text", 26]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 33, "text_extraction_method": "pdftext", "block_counts": [["Span", 279], ["Line", 34], ["Text", 25]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 34, "text_extraction_method": "pdftext", "block_counts": [["Span", 336], ["Line", 57], ["Text", 39]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 35, "text_extraction_method": "pdftext", "block_counts": [["Span", 409], ["Line", 50], ["Text", 40]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 36, "text_extraction_method": "pdftext", "block_counts": [["Span", 451], ["Line", 53], ["Text", 41]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 37, "text_extraction_method": "pdftext", "block_counts": [["Span", 268], ["Line", 50], ["Text", 39]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 38, "text_extraction_method": "pdftext", "block_counts": [["Span", 726], ["Line", 80], ["Text", 63]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 39, "text_extraction_method": "pdftext", "block_counts": [["Span", 507], ["Line", 75], ["Text", 46]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 40, "text_extraction_method": "pdftext", "block_counts": [["Span", 302], ["Line", 53], ["Text", 37]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 41, "text_extraction_method": "pdftext", "block_counts": [["Span", 511], ["Line", 61], ["Text", 49]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 42, "text_extraction_method": "pdftext", "block_counts": [["Span", 625], ["Line", 75], ["Text", 47]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 43, "text_extraction_method": "pdftext", "block_counts": [["Span", 271], ["Line", 57], ["Text", 22]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 44, "text_extraction_method": "pdftext", "block_counts": [["Span", 148], ["Line", 53], ["Text", 18]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 45, "text_extraction_method": "pdftext", "block_counts": [["Span", 24], ["Line", 9], ["Text", 4]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}], "debug_data_path": "debug_data\\14. Self-Taught Optimizer (STOP)"}