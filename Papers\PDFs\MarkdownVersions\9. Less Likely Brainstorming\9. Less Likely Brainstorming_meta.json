{"table_of_contents": [], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 232], ["Line", 99], ["Text", 31]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 265], ["Line", 100], ["Text", 13]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 476], ["Line", 104], ["Text", 48]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 573], ["Line", 123], ["Text", 81]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 550], ["Line", 113], ["Text", 59]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 436], ["Line", 107], ["Text", 52]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 323], ["Line", 82], ["Text", 22]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 377], ["Line", 103], ["Text", 17]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 219], ["Line", 96], ["Text", 26]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 268], ["Line", 108], ["Text", 36]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 266], ["Line", 112], ["Text", 32]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 307], ["Line", 92], ["Text", 31]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 221], ["Line", 83], ["Text", 27]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 150], ["Line", 54], ["Text", 30]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 225], ["Line", 92], ["Text", 26]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 215], ["Line", 36], ["Text", 27]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 233], ["Line", 67], ["Text", 38]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 3], ["Text", 1], ["Line", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 3], ["Text", 1], ["Line", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Text", 1], ["Line", 1], ["Span", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 5], ["Text", 2], ["Line", 2]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 4], ["Text", 1], ["Line", 1]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}], "debug_data_path": "debug_data\\9. Less Likely Brainstorming"}