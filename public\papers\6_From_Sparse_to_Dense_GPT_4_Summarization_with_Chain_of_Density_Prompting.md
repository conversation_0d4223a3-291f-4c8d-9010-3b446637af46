From Sparse to Dense: GPT-4 Summarization with Chain of Density Prompting

> <PERSON> Lehman♡ <EMAIL>

Salesforce AI♢ MIT♡ Columbia University: CS♠, Biomedical Informatics♣

.

<PERSON> <EMAIL>

Human Summary

Figure 1: Chain of Density (**CoD**) summaries grow increasingly entity dense, starting off closer to vanilla GPT-4 summaries and eventually surpassing that of human written summaries. Human annotations suggest that a density similar to that of human-written summaries is preferable–striking the right balance between clarity (favors *less* dense) and informativeness (favors *more* dense).

words is a worthy goal, especially for real-time applications. Yet, how dense is an open question. A summary is uninformative if it contains insufficient detail. If it contains too much information, however, it can become difficult to follow without having to increase the overall length. Conveying more information subject to a fixed token budget requires a combination of abstraction, compression, and fusion. There is a limit to how much space can be made for additional information before becoming illegible or even factually incorrect. In this paper, we seek to identify this limit by soliciting human preferences on a set of increasingly dense summaries produced by GPT-4. Treating entities, and, in particular, the average number of entities per token, as a proxy for density, we generate an initial, entitysparse summary. Then, we iteratively identify and fuse 1-3 missing entities from the previous summary without increasing the overall length (5x overall). Each summary has a higher ratio of entities to tokens than the previous one. Based on human preference data, we determine that humans prefer summaries that are almost as dense as human-written summaries and more

Noémie Elhadad♠,♣ <EMAIL>

Vanilla GPT-4

Human Preferred CoD

Griffin Adams♠,♣ <EMAIL>

Abstract

Selecting the "right" amount of information to include in a summary is a difficult task. A good summary should be detailed and entity-centric without being overly dense and hard to follow. To better understand this tradeoff, we solicit increasingly dense GPT-4 summaries with what we refer to as a "Chain of Density" (CoD) prompt. Specifically, GPT-4 generates an initial entitysparse summary before iteratively incorporating missing salient entities without increasing the length. Summaries generated by CoD are more abstractive, exhibit more fusion, and have less of a lead bias than GPT-4 summaries generated by a vanilla prompt. We conduct a human preference study on 100 CNN DailyMail articles and find that that humans prefer GPT-4 summaries that are more dense than those generated by a vanilla prompt and almost as dense as human written summaries. Qualitative analysis supports the notion that there exists a tradeoff between informativeness and readability. 500 annotated CoD summaries, as well as an extra 5,000 unannotated summaries, are freely available on HuggingFace1

Automatic summarization has come a long way in the past few years, largely due to a paradigm shift away from supervised fine-tuning on labeled datasets to zero-shot prompting with Large Language Models (LLMs), such as GPT-4 (OpenAI, 2023). Without additional training, careful prompting can enable fine-grained control over summary characteristics, such as length (Goyal et al., 2022), topics (Bhaskar et al., 2023), and style (Pu and Demberg, 2023).

An overlooked aspect is the information density of an summary. In theory, as a compression of another text, a summary *should* be denser–containing a higher concentration of information–than the source document. Given the high latency of LLM decoding (Kaddour et al., 2023), covering more information in fewer

1https://huggingface.co/datasets/

griffin/chain_of_density

Faisal Ladhak ♠ <EMAIL>

1 Introduction

arXiv:2309.04269v1 [cs.CL] 8 Sep 2023

Ar t i cl e: { { ARTI CLE} }

Repeat t he f ol l owi ng 2 st eps 5 t i mes.

- Novel : not i n t he pr evi ous summar y. - Fai t hf ul : pr esent i n t he Ar t i cl e. - Anywher e: l ocat ed anywher e i n t he Ar t i cl e.

above Ar t i cl e.

Ent i t i es. A Mi ssi ng Ent i t y i s: - Rel evant : t o t he mai n st or y.

Gui del i nes:

You wi l l gener at e i ncr easi ngl y conci se, ent i t y- dense summar i es of t he

Thi s ar t i cl e di scusses an i nci dent t hat occur r ed dur i ng t he Chi nese Gr and Pr i x i nvol vi ng t wo r aci ng dr i ver s, Jenson But t on and Past or Mal donado. The t wo wer e compet i ng f or t he 13t h pl ace when But t on col l i ded wi t h Mal donado' s vehi cl e, causi ng damage t o bot h car s. The i nci dent r esul t ed i n a penal t y f or But t on, who was demot ed t o 14t h pl ace. Mal donado, on t he ot her hand, had t o r et i r e f r om t he r ace due t o t he damage hi s car sust ai ned.

Chai n of Densi t y ( CoD) Pr ompt CoD Out put s ( Added Det ai l s **f r om pr evi ous**)

Figure 2: Chain of Density (**CoD**) Prompt and example output. At each step, 1-3 additional details (entities) are added to the previous summary without increasing the length. To make room for new entities, existing content is re-written (e.g., compression, fusion). Half the annotators (2/4) prefer the second to last summary, with the others preferring the final one.

Dur i ng t he Chi nese Gr and Pr i x, Jenson But t on, dr i vi ng f or McLar en, col l i ded wi t h Past or Mal donado' s Lot us, whi l e t hey wer e vyi ng f or 13t h pl ace. The cr ash caused Mal donado t o r et i r e and But t on t o r ecei ve a penal t y, dr oppi ng hi m t o 14t h. The i nci dent unf ol ded i n f r ont of Fer nando Al onso, who managed t o avoi d t he col l i si on and move up t wo pl aces.

I n t he Chi nese Gr and Pr i x, Jenson But t on' s McLar en hi t Past or Mal donado' s Lot us, causi ng Mal donado t o r et i r e and But t on t o r ecei ve a f i ve- second penal t y, demot i ng hi m t o 14t h. But t on al so r ecei ved t wo penal t y poi nt s on hi s super l i cence. Fer nando Al onso, who wi t nessed t he i nci dent , advanced t wo pl aces, whi l e But t on was l apped by Ni co Rosber g' s Mer cedes.

Jenson But t on' s McLar en col l i ded wi t h Past or Mal donado' s Lot us dur i ng t he Chi nese Gr and Pr i x, causi ng f r ont wi ng damage t o But t on' s car and r ear - end damage t o Mal donado' s, f or ci ng hi s r et i r ement . But t on r ecei ved a f i ve- second penal t y and t wo super l i cence poi nt s, dr oppi ng hi m t o 14t h. Fer nando Al onso advanced t wo pl aces, whi l e But t on was l apped by Ni co Rosber g and Al onso by Sebast i an Vet t el and Ki mi Rai kkonen.

On l ap 49 of t he i nci dent - packed Chi nese Gr and Pr i x, Jenson But t on' s McLar en hi t Past or Mal donado' s Lot us, causi ng damage and Mal donado' s r et i r ement . But t on r ecei ved a f i ve- second penal t y and t wo super l i cence poi nt s, f al l i ng t o 14t h. Fer nando Al onso, who wi t nessed t he cr ash, advanced t wo pl aces, whi l e But t on was l apped by Ni co Rosber g and Al onso by Fer r ar i ' s Sebast i an Vet t el and Ki mi Rai kkonen.

> To maintain the same length while increasing the number of entities covered, abstraction, fusion, and compression is explicitly encouraged, rather than dropping meaningful content from previous summaries.

> Figure 2 displays the prompt along with an example output. Rather than be prescriptive about the types of entities, we simply define a Missing Entity as:

> > • Specific: descriptive yet concise (5 words or

• Anywhere: located anywhere in the Article.

Data. We randomly sample 100 articles from the CNN/DailyMail summarization (Nallapati et al., 2016) test set for which to generate **CoD** summaries.

Reference Points. For frame of reference, we compare **CoD** summary statistics to human-written bullet-point style reference summaries as well as summaries generated by GPT-4 with a vanilla prompt: "Write a VERY short summary of the Article. Do not exceed 70 words." We set the desired token length to match that of **CoD** summaries (shown in Table 1).

Direct statistics (tokens, entities, entity density) are ones directly controlled for by **CoD**, while Indirect

• Relevant: to the main story.

• Novel: not in the previous summary. • Faithful: present in the Article.

fewer).

3 Statistics

St ep 1. I dent i f y 1- 3 i nf or mat i ve Ent i t i es ( " ; " del i mi t ed) f r om t he Ar t i cl e whi ch ar e mi ssi ng f r om t he pr evi ousl y gener at ed summar y. St ep 2. Wr i t e a new, denser summar y of i dent i cal l engt h whi ch cover s ever y ent i t y and det ai l f r om t he pr evi ous summar y pl us t he Mi ssi ng

- Speci f i c: descr i pt i ve yet conci se ( 5 wor ds or f ewer ) .

( e. g. , " t hi s ar t i cl e di scusses" ) t o r each ~80 wor ds.

- The summar i es shoul d become hi ghl y dense and conci se yet sel f - cont ai ned, e. g. , easi l y under st ood wi t hout t he Ar t i cl e. - Mi ssi ng ent i t i es can appear anywher e i n t he new summar y. - Never dr op ent i t i es f r om t he pr evi ous summar y. I f space cannot be

Remember , use t he exact same number of wor ds f or each summar y. Answer i n JSON. The JSON shoul d be a l i st ( l engt h 5) of di ct i onar i es whose keys ar e " Mi ssi ng_Ent i t i es" and " Denser _Summar y" .

f l ow and make space f or addi t i onal ent i t i es.

Our primary contributions are to:

phr ases l i ke " t he ar t i cl e di scusses" .

made, add f ewer new ent i t i es.

- The f i r st summar y shoul d be l ong ( 4- 5 sent ences, ~80 wor ds) yet hi ghl y non- speci f i c, cont ai ni ng l i t t l e i nf or mat i on beyond t he ent i t i es mar ked as mi ssi ng. Use over l y ver bose l anguage and f i l l er s

- Make ever y wor d count : r e- wr i t e t he pr evi ous summar y t o i mpr ove

- Make space wi t h f usi on, compr essi on, and r emoval of uni nf or mat i ve

dense than those generated by a vanilla GPT-4 prompt.

• Develop a prompt-based iterative method (**CoD**) for making summaries increasingly entity dense. • Conduct both human and automatic evaluation of increasingly dense summaries on CNN/Dailymail articles to better understand the tradeoff between informativeness (favoring more entities)

• Open source GPT-4 summaries, annotations, and a set of 5,000 unannotated **CoD** summaries to

and clarity (favoring fewer entities).

be used for evaluation or distillation.

Prompt. Our goal is to generate a set of summaries with GPT-4 with varying levels of information density, while controlling for length, which has proven to be a strong confounder when evaluating summaries (Fabbri et al., 2021; Liu et al., 2023b). To do this, we formulate a single Chain of Density (**CoD**) prompt, whereby an initial summary is generated and made increasingly entity dense. Specifically, for a fixed number of turns, a set of unique salient entities from the source text are identified and fused into the previous summary without increasing the length. The first summary is entity-sparse as it focuses on only 1-3 initial entities.

2 Chain of Density Prompting

Vanilla GPT-4

Human Summary

entities with Spacy2

2https://spacy.io.

statistics are expected byproducts of densification.

**CoD** Step Tokens Entities Density (E/T) 72 6.4 0.089 67 8.7 0.129 67 9.9 0.148 69 10.8 0.158 72 12.1 0.167 Human 60 8.8 0.151 Vanilla GPT-4 70 8.5 0.122

Table 1: Explicit statistics for GPT-4 **CoD** summaries.

Direct Statistics. In Table 1, we compute tokens with NLTK (Loper and Bird, 2002), measure unique

ratio. The **CoD** prompt largely adheres to a fixed token budget. In fact, the second step leads to an average 5-token (72 to 67) reduction in length as unnecessary words are removed from the initially verbose summary. The entity density rises–starting at 0.089, initially below Human and Vanilla GPT-4 (0.151 and

0.122)–to 0.167 after 5 steps of densification.

Indirect Statistics. *Abstractiveness* should increase with each **CoD** step because summaries are iteratively re-written to make space for each additional entity. We measure abstractiveness with extractive density: the average squared length of extractive fragments (Grusky et al., 2018). Similarly, the level of concept *Fusion* should increase monotonically as entities are added to a fixed-length summary. We proxy fusion as average number of source sentences aligned to each summary sentence. For alignment, we use the relative ROUGE gain method (Zhou et al., 2018), which aligns source sentences to a target sentence until the relative ROUGE gain of an additional sentence is no longer positive. We also expect the *Content Distribution*–the position in the Article from which summary content is sourced–to shift. Specifically, we expect that **CoD** summaries initially exhibit a strong Lead Bias yet gradually start to pull in entities from the

, and compute entity density as the

Human Summary

Figure 3: **CoD**-generated summaries grow increasingly abstractive while exhibiting more fusion and less of a lead bias.

4 Results

Vanilla GPT-4 Human Summary

middle and end of the article. To measure this, we use our alignments from fusion and measure the average sentence rank of all aligned source sentences. Figure 3 confirms these hypotheses: abstractiveness increases with the number of re-writing steps (lower extractive density on the left), the rate of fusion rises (middle figure), and the summaries start to incorporate content from the middle and end of the article (right figure). Interestingly, all **CoD** summaries are more abstractive than both human written and baseline summaries.

To better understand the tradeoffs present with **CoD** summaries, we conduct a preference-based human study and a rating-based evaluation with GPT-4.

> **CoD** % Share of First Place Votes Step Individual Annotators Aggregate 3.0 2.0 13.0 17.4 8.3 25.0 28.0 43.0 31.4 30.8 22.0 28.0 21.0 24.4 23.0 29.0 25.0 13.0 26.7 22.5 21.0 17.0 10.0 16.3 15.5

Table 2: Breakdown of first-place votes for **CoD** summaries by step. Based on aggregate preferences, the modal **CoD** step is 2, median is 3, and expected is 3.06.

Human Preferences. We conduct a human evaluation to assess the impact of densification on human assessments of overall quality. Specifically, the first four authors of the paper were presented with randomly shuffled **CoD** summaries, along with the articles, for the same 100 articles (5 steps * 100 = 500 total summaries). Based on the definition of a "good summary" from Stiennon et al. (2020) (Table 6 from their paper), each annotator indicated their top preferred summary. Table 2 reports the breakdown of first place votes by **CoD** step across annotators–as well as aggregated across annotators. First, we report a low Fleiss' kappa (Fleiss, 1971) of 0.112, which points to the subtle differences between summaries and the subjective nature of the task. Recent work has

Vanilla GPT-4

**CoD** Step Entity Density Informative Quality Coherence Attributable Overall GPT-4 Eval Average 0.089 4.34 4.75 4.96 4.96 4.41 4.69 0.129 4.62 4.79 4.92 5.00 4.58 4.78 0.148 4.67 4.76 4.84 5.00 4.57 4.77 0.158 4.74 4.69 4.75 5.00 4.61 4.76 0.167 4.73 4.65 4.61 4.97 4.58 4.71

Table 3: GPT-4 Likert-scale (1-5) assessments of Chain of Density (**CoD**) Summaries by step.

Figure 4: An example of a human-preferred densification step (left) and one which is not preferred. For the left, the bottom summary is preferred because the addition of "Liverpool" and the goal-scorers is relevant. The second summary makes room with sensible compressions, such as synthesizing "a potential route back into the game" into "a comeback". For the right, the addition of more details on "TVMonde" does not make up for the presence of an awkward fusion of

> to solicit scores for each dimension. Table 3 suggests that densification is correlated with informativeness, yet there is a limit, with the score peaking at Step 4 (4.74). Article-free dimensions: Quality and Coherence, decline sooner (after 2 and 1 steps, respectively). All summaries are deemed Attributable to the source article. The Overall scores skew toward denser and more informative summaries, with Step 4 having the highest score. On average across dimensions, the first and last **CoD** steps are *least* favored, while the middle three are close (4.78, 4.77, and 4.76, respectively). In Appendix A, we report highest summarylevel correlations of the Overall metric to human judgments (0.31 Pearson correlation), yet note low correlations overall–a phenomenon observed by Deutsch et al. (2022) when summaries are of similar quality.

> Qualitative Analysis. There exists a clear trade-off between coherence / readability of summaries and informativeness. To illustrate, in Figure 4, we present two **CoD** steps: one for which the summary is improved with more detail, and one for which the summary is harmed. On average, intermediate **CoD** summaries best achieved this balance, yet we leave it to future work to precisely define and quantify this tradeoff.

> GPT Summarization. Goyal et al. (2022) benchmarked GPT-3 on news article summarization and found that humans preferred GPT-3 summaries over previous supervised baselines, which was

5 Related Work

entities ("cyberattack", and "Yves Bigot"), which was a direct result of having to tighten the previous summary.

similarly noted low instance-level agreement when judging GPT-based summaries (Goyal et al., 2022). Yet, at the system level, some trends start to emerge. For 3 of the 4 annotators, **CoD** step 1 received the largest share of first-place votes across the 100 examples (28, 43, and 31.4%, respectively). Yet, in aggregate, 61% of first placed summaries (23.0+22.5+15.5) involved ≥3 densification steps. The median preferred **CoD** step is in the middle (3),

Based on the average density of Step 3 summaries, we can roughly infer a preferred entity density of ∼ 0.15 across the **CoD** candidates. From Table 1, we can see that this density aligns with human-written summaries (0.151), yet is noticeable higher than summaries produced with a vanilla GPT-4 prompt (0.122).

Automatic Metrics. As an evaluator, GPT-4 has been shown to adequately correlate to human judgments (Fu et al., 2023; Liu et al., 2023a), even potentially outperforming crowd-sourced workers on some annotation tasks (Gilardi et al., 2023). As a complement to our human evaluation (below), we prompt GPT-4 to rate **CoD** summaries (1-5) along 5 dimensions: Informative, Quality, Coherence, Attributable, and Overall. The definitions of Informative, Quality, and Attributable come from Aharoni et al. (2023), while Coherence comes from Fabbri

. Overall aims to capture the qualities

jointly. Please see Appendix A for the prompts used 3Quality and Coherence are article-independent metrics.

and the expected step is 3.06.

et al. (2021)

3

not reflective of existing reference-based and reference-free metrics. Zhang et al. (2023) find that zeroshot GPT-3 summaries perform on par with humans by soliciting high-quality summaries from freelance writers. Entity-Based Summarization. Narayan et al. (2021) proposed generating entity chains as a planning step for supervised fine-tuning of summarization models, in contrast to keywords (Li et al., 2020; Dou et al., 2021) or purely extractive units (Dou et al., 2021; Adams et al., 2023a). Entities have also been incorporated for summarization as a form of control (Liu and Chen, 2021; He et al., 2022; Maddela et al., 2022), to improve faithfulness (Nan et al., 2021; Adams et al., 2022), and as a unit for evaluation (Cao et al., 2022; Adams et al., 2023b).

*Computational Linguistics: EMNLP 2022*, pages 4009–4027, Abu Dhabi, United Arab Emirates.

Griffin Adams, Jason Zucker, and Noémie Elhadad. 2023b. A meta-evaluation of faithfulness metrics for long-form hospital-course summarization. *arXiv*

Roee Aharoni, Shashi Narayan, Joshua Maynez, Jonathan Herzig, Elizabeth Clark, and Mirella Lapata. 2023. Multilingual summarization with factual consistency evaluation. In *Findings of the Association for Computational Linguistics: ACL 2023*, pages 3562–3591, Toronto, Canada. Association for Computational Linguistics. Adithya Bhaskar, Alex Fabbri, and Greg Durrett. 2023. Prompted opinion summarization with GPT-3.5. In *Findings of the Association for Computational Linguistics: ACL 2023*, pages 9282–9300, Toronto, Canada. Association for Computational Linguistics. Meng Cao, Yue Dong, and Jackie Cheung. 2022. Hallucinated but factual! inspecting the factuality of hallucinations in abstractive summarization. In *Proceedings of the 60th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers)*, pages 3340–3354, Dublin, Ireland.

Association for Computational Linguistics.

Association for Computational Linguistics.

Linguistics.

Daniel Deutsch, Rotem Dror, and Dan Roth. 2022. Re-examining system-level correlations of automatic summarization evaluation metrics. In *Proceedings of the 2022 Conference of the North American Chapter of the Association for Computational Linguistics: Human Language Technologies*, pages 6038–6052, Seattle, United States. Association for Computational Linguistics. Zi-Yi Dou, Pengfei Liu, Hiroaki Hayashi, Zhengbao Jiang, and Graham Neubig. 2021. GSum: A general framework for guided neural abstractive summarization. In *Proceedings of the 2021 Conference of the North American Chapter of the Association for Computational Linguistics: Human Language Technologies*, pages 4830–4842, Online. Association for Computational

Alexander R. Fabbri, Wojciech Krysci ´ nski, Bryan McCann, ´ Caiming Xiong, Richard Socher, and Dragomir Radev. 2021. SummEval: Re-evaluating summarization evaluation. *Transactions of the Association for*

Joseph L Fleiss. 1971. Measuring nominal scale agreement among many raters. *Psychological bulletin*, 76(5):378. Jinlan Fu, See-Kiong Ng, Zhengbao Jiang, and Pengfei Liu. 2023. Gptscore: Evaluate as you desire. *arXiv*

Fabrizio Gilardi, Meysam Alizadeh, and Maël Kubli. 2023. Chatgpt outperforms crowd-workers for text-annotation

Tanya Goyal, Junyi Jessy Li, and Greg Durrett. 2022. News summarization and evaluation in the era of gpt-3.

*Computational Linguistics*, 9:391–409.

tasks. *arXiv preprint arXiv:2303.15056*.

*arXiv preprint arXiv:2209.12356*.

*preprint arXiv:2302.04166*.

*preprint arXiv:2303.03948*.

We study the impact of summary densification on human preferences of overall quality. We find that a degree of densification is preferred, yet, when summaries contain too many entities per token, it is very difficult maintain readability and coherence. We open-source annotated test set as well as a larger un-annotated training set for further research into the topic of fixed-length, variable density summarization.

We only analyze **CoD** for a single domain, news summarization. Annotations did not show high summary-level agreement yet did start to show system-level trends, which is in line with previous work on LLM-based evaluation (Goyal et al., 2022). Finally, GPT-4 is a closed source model so we cannot share model weights. We do, however, publish all evaluation data, annotations, as well as 5, 000 un-annotated **CoD** to be used for downstream uses cases, e.g., density distillation into an open-sourced model such as LLAMA-2 (Touvron et al., 2023).

Griffin Adams, Alex Fabbri, Faisal Ladhak, Noémie Elhadad, and Kathleen McKeown. 2023a. Generating EDU extracts for plan-guided summary re-ranking. In *Proceedings of the 61st Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers)*, pages 2680–2697, Toronto, Canada.

Griffin Adams, Han-Chin Shing, Qing Sun, Christopher Winestock, Kathleen McKeown, and Noémie Elhadad. 2022. Learning to revise references for faithful summarization. In *Findings of the Association for*

Association for Computational Linguistics.

6 Conclusion

7 Limitations

References

Max Grusky, Mor Naaman, and Yoav Artzi. 2018. Newsroom: A dataset of 1.3 million summaries with diverse extractive strategies. In *Proceedings of the 2018 Conference of the North American Chapter of the Association for Computational Linguistics: Human Language Technologies, Volume 1 (Long Papers)*, pages 708–719, New Orleans, Louisiana. Association for

*Conference on Computational Natural Language Learning*, pages 280–290, Berlin, Germany. Association

Feng Nan, Ramesh Nallapati, Zhiguo Wang, Cicero Nogueira dos Santos, Henghui Zhu, Dejiao Zhang, Kathleen McKeown, and Bing Xiang. 2021. Entity-level factual consistency of abstractive text summarization. In *Proceedings of the 16th Conference of the European Chapter of the Association for Computational Linguistics: Main Volume*, pages 2727–2733, Online.

Shashi Narayan, Yao Zhao, Joshua Maynez, Gonçalo Simões, Vitaly Nikolaev, and Ryan McDonald. 2021. Planning with learned entity prompts for abstractive summarization. *Transactions of the Association for*

OpenAI. 2023. Gpt-4 technical report. *ArXiv*,

Dongqi Pu and Vera Demberg. 2023. ChatGPT vs human-authored text: Insights into controllable text summarization and sentence style transfer. In *Proceedings of the 61st Annual Meeting of the Association for Computational Linguistics (Volume 4: Student Research Workshop)*, pages 1–18, Toronto, Canada. Association

Nisan Stiennon, Long Ouyang, Jeffrey Wu, Daniel Ziegler, Ryan Lowe, Chelsea Voss, Alec Radford, Dario Amodei, and Paul F Christiano. 2020. Learning to summarize with human feedback. *Advances in Neural Information Processing Systems*, 33:3008–3021.

Hugo Touvron, Louis Martin, Kevin Stone, Peter Albert, Amjad Almahairi, Yasmine Babaei, Nikolay Bashlykov, Soumya Batra, Prajjwal Bhargava, Shruti Bhosale, et al. 2023. Llama 2: Open foundation and fine-tuned chat

Tianyi Zhang, Faisal Ladhak, Esin Durmus, Percy Liang, Kathleen McKeown, and Tatsunori B Hashimoto. 2023. Benchmarking large language models for news summarization. *arXiv preprint arXiv:2301.13848*.

Qingyu Zhou, Nan Yang, Furu Wei, Shaohan Huang, Ming Zhou, and Tiejun Zhao. 2018. Neural document summarization by jointly learning to score and select sentences. In *Proceedings of the 56th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers)*, pages 654–663, Melbourne, Australia. Association for Computational Linguistics.

For the GPT-4 Likert-style evaluation, we use the

models. *arXiv preprint arXiv:2307.09288*.

A GPT-4 Metrics

following prompt template. Article: {{Article}}

Summary: {{Summary}}

Association for Computational Linguistics.

*Computational Linguistics*, 9:1475–1492.

for Computational Linguistics.

abs/2303.08774.

for Computational Linguistics.

Junxian He, Wojciech Kryscinski, Bryan McCann, Nazneen Rajani, and Caiming Xiong. 2022. CTRLsum: Towards generic controllable text summarization. In *Proceedings of the 2022 Conference on Empirical Methods in Natural Language Processing*, pages 5879–5915, Abu Dhabi, United Arab Emirates.

Jean Kaddour, Joshua Harris, Maximilian Mozes, Herbie Bradley, Roberta Raileanu, and Robert McHardy. 2023. Challenges and applications of large language models.

Haoran Li, Junnan Zhu, Jiajun Zhang, Chengqing Zong, and Xiaodong He. 2020. Keywords-guided abstractive sentence summarization. In *Proceedings of the AAAI conference on artificial intelligence*, volume 34, pages

Yang Liu, Dan Iter, Yichong Xu, Shuohang Wang, Ruochen Xu, and Chenguang Zhu. 2023a. Gpteval: Nlg evaluation using gpt-4 with better human alignment.

Yixin Liu, Alex Fabbri, Pengfei Liu, Yilun Zhao, Linyong Nan, Ruilin Han, Simeng Han, Shafiq Joty, Chien-Sheng Wu, Caiming Xiong, and Dragomir Radev. 2023b. Revisiting the gold standard: Grounding summarization evaluation with robust human evaluation. In *Proceedings of the 61st Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers)*, pages 4140–4170, Toronto, Canada.

Association for Computational Linguistics.

Zhengyuan Liu and Nancy Chen. 2021. Controllable neural dialogue summarization with personal named entity planning. In *Proceedings of the 2021 Conference on Empirical Methods in Natural Language Processing*, pages 92–106, Online and Punta Cana, Dominican Republic. Association for Computational Linguistics.

Edward Loper and Steven Bird. 2002. Nltk: The natural language toolkit. *arXiv preprint cs/0205028*.

Mounica Maddela, Mayank Kulkarni, and Daniel Preotiuc-Pietro. 2022. EntSUM: A data set for entity-centric extractive summarization. In *Proceedings of the 60th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers)*, pages 3355–3366, Dublin, Ireland. Association for

Ramesh Nallapati, Bowen Zhou, Cicero dos Santos, Çaglar ˘ Gulçehre, and Bing Xiang. 2016. Abstractive text summarization using sequence-to-sequence RNNs and beyond. In *Proceedings of the 20th SIGNLL*

Computational Linguistics.

Association for Computational Linguistics.

*arXiv preprint arXiv:2307.10169*.

*arXiv preprint arXiv:2303.16634*.

8196–8203.

Computational Linguistics.

Please rate the summary (1=worst to 5=best) with respect to {{Dimension}}.

Below, we present the definitions provided for each

• Informative: An informative summary captures the important information in the article and

• Quality: A high quality summary is comprehen-

• Coherence: A coherent summary is well-

• Attributable: Is all the information in the summary fully attributable to the Article?

• Overall Preference: A good summary should convey the main ideas in the Article in a concise,

The Quality and Coherence prompts do not include the Article in the prompt. These definitions were paraphrased from previous summarization annotation efforts: (Fabbri et al., 2021; Aharoni et al., 2023).

> Dimension Correlation Informative 0.215 Quality 0.120 Coherence 0.178 Attributable 0.245 Overall 0.311

Table 4: Summary-Level Pearson Correlation coefficient between human preferences and GPT-4 Likert ratings.

Meta-Evaluation. To compute the summary-level correlation, we first turned the preference data into a vector representing the number of times that summary received a first-placed vote. Table 4 demonstrates, unsurprisingly, that a prompt designed to capture overall summary rating has the highest summary-level Pearson correlation to overall preferences (31), yet

overall correlations are still low.

presents it accurately and concisely.

sible and understandable.

structured and well-organized.

logical, and coherent fashion.

{{Definition}}

quality metric.

