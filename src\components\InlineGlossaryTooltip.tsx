'use client';

import React from 'react';
import { useGlossary, GlossaryTerm } from '@/context/GlossaryContext';
import Tooltip from '@/components/Tooltip';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

interface InlineGlossaryTooltipProps {
  children: React.ReactNode;
  className?: string;
}

/**
 * A component that renders glossary tooltips while preserving markdown formatting
 */
export default function InlineGlossaryTooltip({ children, className = '' }: InlineGlossaryTooltipProps) {
  const { terms, isLoading } = useGlossary();

  // Helper function for custom markdown components (styling only, no tooltips)
  const customComponents = {
    strong: ({ node, children }: any) => (
      <strong className="text-white font-semibold" style={{ display: 'inline' }}>
        {children}
      </strong>
    ),
    p: ({ node, children }: any) => <span style={{ display: 'inline' }}>{children}</span>
  };

  // These components are used when glossary IS ready and we are processing a STRING child
  // They embed the tooltip generation logic via processGlossaryTokens
  const tooltipEnabledComponents = {
    ...customComponents, // Basic styling
    p: ({ children: pChildren, ...props }: any) => <span style={{ display: 'inline' }}>{processGlossaryTokens(String(pChildren))}</span>,
    strong: ({ children: strongChildren, ...props }: any) => (
      <strong className="text-white font-semibold" style={{ display: 'inline' }}>
        {processGlossaryTokens(String(strongChildren))}
      </strong>
    ),
    li: ({ children: liChildren, ...props }: any) => <li {...props}>{processGlossaryTokens(String(liChildren))}</li>,
  };
  
  // Loading state or no terms available
  if (isLoading || !terms || terms.length === 0) {
    if (typeof children === 'string') {
      // Render string children with basic markdown styling if glossary not ready
      return (
        <ReactMarkdown
          remarkPlugins={[remarkGfm]}
          className={className}
          components={customComponents} // Styling only, no tooltips
        >
          {children}
        </ReactMarkdown>
      );
    } else {
      // Render ReactNode children as-is if glossary not ready
      return <span className={className}>{children}</span>;
    }
  }

  // Glossary is loaded and terms are available
  if (typeof children === 'string') {
    // If glossary is still loading, show children as is or a loading indicator
    if (isLoading) {
      return <span className={`${className} opacity-75 cursor-default`}>{children}</span>; 
    }
    // Glossary loaded, process for tooltips
    const markedText = markGlossaryTerms(children, terms || []); // Pass terms to markGlossaryTerms
    const processedOutput = processGlossaryTokens(markedText);
    return (
      <span className={className}>
        {/* Render processedNodes directly without ReactMarkdown wrapper */}
        {Array.isArray(processedOutput)
          ? processedOutput.map((node: React.ReactNode, index: number) => (
              <React.Fragment key={index}>{node}</React.Fragment>
            ))
          : processedOutput // If it's a string or other non-array, render directly
        }
      </span>
    );
  }

  // Handle ReactNode children (non-string)
  // If glossary is loading, or if children are not a plain string, render as is.
  // Tooltip functionality for arbitrary React nodes is not deeply applied here.
  if (isLoading || typeof children !== 'string') { 
    return <span className={className}>{children}</span>;
  }

  // Fallback or default rendering if none of the above conditions are met
  // This case should ideally not be reached if logic is exhaustive
  return <span className={className}>{children}</span>;
}

// Helper function to process a plain text segment and replace glossary terms
function processPlainTextSegment(segment: string, sortedTerms: GlossaryTerm[], escapePatternRegex: RegExp): string {
  let result = "";
  let remainingSegment = segment;

  while (remainingSegment.length > 0) {
    let earliestMatch: { index: number; term: GlossaryTerm; matchedString: string; patternUsed: string } | null = null;

    for (const term of sortedTerms) {
      // Prioritize full term, then acronym
      const patternsToTry = [{pattern: term.term!, isAcronym: false}];
      if (term.acronym && term.acronym.length >= 2 && term.acronym.toLowerCase() !== term.term!.toLowerCase()) {
        patternsToTry.push({pattern: term.acronym, isAcronym: true});
      }

      for (const p of patternsToTry) {
        const escapedPattern = p.pattern.replace(escapePatternRegex, '\\$&');
        const regex = new RegExp(`\\b(${escapedPattern})\\b`, 'g');
        let matchArr;
        regex.lastIndex = 0; // Reset lastIndex for global regex in loop
        if ((matchArr = regex.exec(remainingSegment)) !== null) {
          if (earliestMatch === null || matchArr.index < earliestMatch.index) {
            earliestMatch = { index: matchArr.index, term: term, matchedString: matchArr[1], patternUsed: p.pattern };
          } else if (matchArr.index === earliestMatch.index && p.pattern.length > earliestMatch.patternUsed.length) {
            // If multiple terms match at the same index, prefer the longer one (already handled by sortedTerms for term vs term)
            // This mainly helps if an acronym and a term start at same spot, or two diff terms.
            earliestMatch = { index: matchArr.index, term: term, matchedString: matchArr[1], patternUsed: p.pattern };
          }
        }
      }
    }

    if (earliestMatch) {
      result += remainingSegment.substring(0, earliestMatch.index);
      const matchedTermObj = earliestMatch.term;
      const encodedDef = encodeURIComponent(matchedTermObj.definition || 'No definition available.');
      
      // Use the actual string that was matched (term or acronym) for the token's display name
      result += `[GLOSS-TERM:${earliestMatch.matchedString}:${encodedDef}]`;
      remainingSegment = remainingSegment.substring(earliestMatch.index + earliestMatch.matchedString.length);
    } else {
      result += remainingSegment;
      remainingSegment = ""; // No more matches
    }
  }
  return result;
}

// Helper function to mark glossary terms in a string
function markGlossaryTerms(input: string, glossaryTerms: GlossaryTerm[]): string {
  if (!glossaryTerms || glossaryTerms.length === 0 || typeof input !== 'string' || !input.trim()) return input;

  const escapeStringForRegexp = '[.*+?^${}()|[\]\\]';
  const escapePatternRegex = new RegExp(escapeStringForRegexp, 'g');

  const sortedTerms = [...(glossaryTerms || [])]
    .filter(term => term.term && term.term.length >= 2) // Min length 2 for terms/acronyms
    .sort((a, b) => {
      // Primary sort: longer terms first to match e.g. "Large Language Model" before "Language Model"
      const lenDiff = (b.term!.length) - (a.term!.length);
      if (lenDiff !== 0) return lenDiff;
      // Secondary sort: (optional but good for consistency) alphabetical for terms of same length
      return a.term!.localeCompare(b.term!);
    });

  const tokenSplitRegex = new RegExp("(\\[GLOSS-TERM:[^:]+:.+?\\])", "g");
  const parts = input.split(tokenSplitRegex);

  const processedParts = parts.map(part => {
    if (part.startsWith('[GLOSS-TERM:') && part.endsWith(']')) {
      const fullTokenMatchRegex = new RegExp("^\[GLOSS-TERM:[^:]+:.+?\]$");
      if (fullTokenMatchRegex.test(part)) {
        return part; // It's a well-formed token, leave it
      }
    }
    // It's a plain text segment or a malformed token; process it
    return processPlainTextSegment(part, sortedTerms, escapePatternRegex);
  });

  return processedParts.join('');
}

// Helper function to parse the marked text and create React nodes with tooltips
function processGlossaryTokens(content: string): React.ReactNode[] | string {
  if (typeof content !== 'string' || !content.includes('[GLOSS-TERM:')) {
    return content; // Return original content if no tokens are present
  }

  const splitRegexString = "(\\[GLOSS-TERM:[^:]+:.+?\\])";
  const splitRegex = new RegExp(splitRegexString, "g");
  const parts = content.split(splitRegex);
  const elements: React.ReactNode[] = [];

  const matchRegexString = "\\[GLOSS-TERM:([^:]+):(.+?)\\]";
  const matchRegex = new RegExp(matchRegexString);

  for (let i = 0; i < parts.length; i++) {
    const part = parts[i];
    if (part.startsWith('[GLOSS-TERM:') && part.endsWith(']')) {
      const match = part.match(matchRegex);
      if (match && match[1] && match[2]) {
        const termName = match[1];
        const encodedDefinition = match[2];
        try {
          const decodedDefinition = decodeURIComponent(encodedDefinition);
          elements.push(
            <Tooltip key={`${termName}-${i}`} text={decodedDefinition}>
              <span className="border-b border-dotted border-gray-400 dark:border-gray-600 cursor-help" style={{ display: 'inline' }}>
                {termName}
              </span>
            </Tooltip>
          );
        } catch (e) {
          console.error('[InlineGlossaryTooltip ERROR] Failed to decode definition:', encodedDefinition, e, 'Original part:', part);
          elements.push(part); // Push original part if decoding fails
        }
      } else {
        elements.push(part); // Push part if regex doesn't match expected structure
      }
    } else {
      elements.push(part); // Push non-token part
    }
  }
  return elements;
}
