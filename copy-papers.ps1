# Source directories
$sourcePDFDir = "C:\Nino\School\2025\Jaar 4\Supportive Narrative\Critical Review\Papers\PDFs\pdf"
$sourceMarkdownDir = "C:\Nino\School\2025\Jaar 4\Supportive Narrative\Critical Review\Papers\PDFs"

# Destination directories
$destPDFDir = ".\public\papers\pdf"
$destMarkdownDir = ".\public\papers"

# Create destination directories if they don't exist
New-Item -ItemType Directory -Force -Path $destPDFDir
New-Item -ItemType Directory -Force -Path $destMarkdownDir

# Copy PDF files
Get-ChildItem -Path $sourcePDFDir -Filter "*.pdf" | ForEach-Object {
    Copy-Item $_.FullName -Destination $destPDFDir
}

# Copy Markdown files (excluding directories)
Get-ChildItem -Path $sourceMarkdownDir -Filter "*.md" | Where-Object { !$_.PSIsContainer } | ForEach-Object {
    Copy-Item $_.FullName -Destination $destMarkdownDir
}

Write-Host "Files copied successfully!"
