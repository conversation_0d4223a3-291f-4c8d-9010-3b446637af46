# Function to sanitize filenames
function Sanitize-FileName {
    param (
        [string]$fileName
    )
    # Remove the extension
    $baseName = [System.IO.Path]::GetFileNameWithoutExtension($fileName)
    $ext = [System.IO.Path]::GetExtension($fileName)
    
    # Replace spaces and special characters with underscores
    $sanitized = $baseName -replace '[\s\.-]+', '_'
    
    # Add back the extension
    return $sanitized + $ext
}

# Directories to process
$dirs = @(
    ".\public\papers\pdf",
    ".\public\papers"
)

foreach ($dir in $dirs) {
    Get-ChildItem -Path $dir -File | ForEach-Object {
        $newName = Sanitize-FileName -fileName $_.Name
        if ($_.Name -ne $newName) {
            Write-Host "Renaming $($_.Name) to $newName"
            Rename-Item -Path $_.FullName -NewName $newName -Force
        }
    }
}

Write-Host "Files renamed successfully!"
