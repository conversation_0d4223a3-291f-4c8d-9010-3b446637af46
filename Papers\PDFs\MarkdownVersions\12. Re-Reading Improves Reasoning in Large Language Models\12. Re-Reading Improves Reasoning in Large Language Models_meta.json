{"table_of_contents": [], "page_stats": [{"page_id": 0, "text_extraction_method": "pdftext", "block_counts": [["Span", 356], ["Line", 106], ["Text", 34]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 1, "text_extraction_method": "pdftext", "block_counts": [["Span", 349], ["Line", 124], ["Text", 46]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 2, "text_extraction_method": "pdftext", "block_counts": [["Span", 373], ["Line", 100], ["Text", 32]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 3, "text_extraction_method": "pdftext", "block_counts": [["Span", 637], ["Line", 80], ["Text", 29]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 4, "text_extraction_method": "pdftext", "block_counts": [["Span", 383], ["Line", 128], ["Text", 53]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 5, "text_extraction_method": "pdftext", "block_counts": [["Span", 463], ["Line", 84], ["Text", 23]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 6, "text_extraction_method": "pdftext", "block_counts": [["Span", 319], ["Line", 139], ["Text", 46]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 7, "text_extraction_method": "pdftext", "block_counts": [["Span", 310], ["Line", 110], ["Text", 22]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 8, "text_extraction_method": "pdftext", "block_counts": [["Span", 240], ["Line", 96], ["Text", 16]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 9, "text_extraction_method": "pdftext", "block_counts": [["Span", 278], ["Line", 110], ["Text", 39]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 10, "text_extraction_method": "pdftext", "block_counts": [["Span", 281], ["Line", 110], ["Text", 38]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 11, "text_extraction_method": "pdftext", "block_counts": [["Span", 272], ["Line", 110], ["Text", 35]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 12, "text_extraction_method": "pdftext", "block_counts": [["Span", 108], ["Line", 42], ["Text", 14]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 13, "text_extraction_method": "pdftext", "block_counts": [["Span", 287], ["Line", 99], ["Text", 52]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 14, "text_extraction_method": "pdftext", "block_counts": [["Span", 293], ["Line", 55], ["Text", 18]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 15, "text_extraction_method": "pdftext", "block_counts": [["Span", 95], ["Line", 37], ["Text", 32]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 16, "text_extraction_method": "pdftext", "block_counts": [["Span", 81], ["Line", 35], ["Text", 31]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 17, "text_extraction_method": "pdftext", "block_counts": [["Span", 93], ["Line", 45], ["Text", 31]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 18, "text_extraction_method": "pdftext", "block_counts": [["Span", 137], ["Line", 22], ["Text", 21]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 19, "text_extraction_method": "pdftext", "block_counts": [["Span", 291], ["Line", 45], ["Text", 33]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 20, "text_extraction_method": "pdftext", "block_counts": [["Span", 175], ["Line", 36], ["Text", 34]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 21, "text_extraction_method": "pdftext", "block_counts": [["Span", 331], ["Line", 55], ["Text", 40]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 22, "text_extraction_method": "pdftext", "block_counts": [["Span", 201], ["Line", 39], ["Text", 33]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 23, "text_extraction_method": "pdftext", "block_counts": [["Span", 221], ["Line", 42], ["Text", 36]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 24, "text_extraction_method": "pdftext", "block_counts": [["Span", 221], ["Line", 37], ["Text", 32]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 25, "text_extraction_method": "pdftext", "block_counts": [["Span", 183], ["Line", 53], ["Text", 46]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}, {"page_id": 26, "text_extraction_method": "pdftext", "block_counts": [["Span", 76], ["Line", 29], ["Text", 26]], "block_metadata": {"llm_request_count": 0, "llm_error_count": 0, "llm_tokens_used": 0}}], "debug_data_path": "debug_data\\12. Re-Reading Improves Reasoning in Large Language Models"}