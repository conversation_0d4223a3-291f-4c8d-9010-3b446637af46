export interface GlossaryTerm {
  term: string;
  acronym?: string;
  definition: string;
  category: 'ai' | 'ml' | 'audio' | 'general';
  relatedTerms?: string[];
  furtherReading?: Array<{ title: string; url: string }>;
}

export const glossaryTerms: GlossaryTerm[] = [
  {
    term: 'Abstractiveness',
    definition: 'In text summarization, a measure of how much a summary deviates from the exact wording of the source text. More abstractive summaries use novel phrasing rather than copying directly from the source. Often measured using extractive density (the average squared length of extractive fragments).',
    category: 'ai',
    relatedTerms: ['Summarization', 'Extractive Density', 'Text Generation', 'Paraphrasing']
  },
  {
    term: 'Adaptive Temperature Sampling',
    acronym: 'AdapT',
    definition: 'A decoding strategy for language models that dynamically adjusts the temperature parameter during text generation based on token predictability. It uses higher temperature for challenging tokens (requiring more creativity) and lower temperature for confident tokens (requiring more precision).',
    category: 'ai',
    relatedTerms: ['Temperature', 'Sampling', 'Decoding Strategy', 'Token Generation']
  },
  {
    term: 'Autoregressive Generation',
    definition: 'A text generation approach where each new token (word or character) is produced based on all previously generated tokens. The model predicts one token at a time, using its own previous outputs as context for the next prediction.',
    category: 'ai',
    relatedTerms: ['Token Generation', 'Language Model', 'Text Generation']
  },
  {
    term: 'Artificial Intelligence',
    acronym: 'AI',
    definition: 'Computer systems designed to perform tasks that typically require human intelligence, such as visual perception, speech recognition, decision-making, and language translation.',
    category: 'ai',
    relatedTerms: ['Machine Learning', 'Neural Network', 'Deep Learning']
  },
  {
    term: 'BRAINSTORM',
    definition: 'A contrastive learning strategy for generating less likely but relevant hypotheses. It incorporates margin loss and similarity loss objectives to help models learn the relationship between input context, indicators, and outputs, enabling more effective generation of alternative hypotheses.',
    category: 'ai',
    relatedTerms: ['Less Likely Brainstorming', 'Contrastive Learning', 'Controllable Text Generation', 'Similarity Loss']
  },
  {
    term: 'Bounded Rationality',
    definition: 'A concept in decision-making theory where rationality of individuals is limited by the information they have, their cognitive limitations, and the finite amount of time they have to make decisions. In text generation, it helps define what hypotheses are likely versus less likely based on known premises.',
    category: 'general',
    relatedTerms: ['Less Likely Brainstorming', 'Decision Making', 'Cognitive Bias', 'Alternative Hypotheses']
  },

  {
    term: 'Audio Signal Processing',
    definition: 'The manipulation of audio signals to achieve specific effects or extract information, commonly used in audio plugins for tasks like filtering, compression, and equalization.',
    category: 'audio',
    relatedTerms: ['DSP', 'Filter', 'Equalization']
  },
  {
    term: 'Chain of Density',
    acronym: 'CoD',
    definition: 'A prompting technique for text summarization that iteratively increases the entity density of summaries. It starts with an entity-sparse summary and progressively adds more entities without increasing the length, requiring compression and fusion of information.',
    category: 'ai',
    relatedTerms: ['Summarization', 'Entity Density', 'Information Compression', 'Fusion']
  },
  {
    term: 'Calculation Error',
    definition: 'A type of error in language model reasoning where the model makes mistakes in arithmetic operations or other numerical calculations, leading to incorrect final answers despite potentially correct reasoning steps.',
    category: 'ai',
    relatedTerms: ['Chain of Thought', 'Reasoning', 'Error Analysis', 'Large Language Model']
  },
  {
    term: 'Chain of Thought',
    acronym: 'CoT',
    definition: 'A prompting technique that encourages language models to show their reasoning process step-by-step, leading to more accurate results for complex problems.',
    category: 'ai',
    relatedTerms: ['Prompting', 'Reasoning', 'Problem Solving']
  },
  {
    term: 'Cognitive Bias',
    definition: 'Systematic patterns of deviation from norm or rationality in judgment. In medical contexts like radiology, biases such as confirmation bias can lead to diagnostic errors when practitioners look for confirmatory evidence while ignoring contradictory information.',
    category: 'general',
    relatedTerms: ['Confirmation Bias', 'Decision Making', 'Less Likely Brainstorming', 'Diagnostic Error']
  },
  {
    term: 'Confirmation Bias',
    definition: 'The tendency to search for, interpret, favor, and recall information in a way that confirms one\'s preexisting beliefs. In medical contexts, this can lead to practitioners focusing on evidence that supports an initial diagnostic hypothesis while ignoring evidence that refutes it.',
    category: 'general',
    relatedTerms: ['Cognitive Bias', 'Diagnostic Error', 'Less Likely Brainstorming', 'Decision Making']
  },
  {
    term: 'Content Distribution',
    definition: 'In text summarization, refers to where in the source document the summary content is drawn from. Summaries with broader content distribution pull information from throughout the document rather than just the beginning.',
    category: 'ai',
    relatedTerms: ['Summarization', 'Lead Bias', 'Information Extraction']
  },
  {
    term: 'Contrastive Decoding',
    definition: 'A text generation technique that re-weights generation probabilities by contrasting the output distributions between different language models. It helps guide the generation process toward desired attributes by leveraging the differences between models trained on different data distributions.',
    category: 'ai',
    relatedTerms: ['Controllable Text Generation', 'Text Generation', 'Decoding Strategy', 'Language Model']
  },
  {
    term: 'Contrastive Learning',
    definition: 'A machine learning technique that learns representations by contrasting similar and dissimilar examples. In NLP, it has been applied to various tasks including sentence representation learning, text generation, and improving natural language understanding by teaching models to differentiate between related and unrelated content.',
    category: 'ml',
    relatedTerms: ['Representation Learning', 'Self-Supervised Learning', 'Natural Language Understanding', 'Similarity Loss']
  },
  {
    term: 'CoNT',
    definition: 'Contrastive Neural Text generation, a technique that uses self-generated outputs as negative samples in a contrastive learning framework to improve text generation quality and control.',
    category: 'ai',
    relatedTerms: ['Contrastive Learning', 'Text Generation', 'Negative Sampling', 'Controllable Text Generation']
  },
  {
    term: 'Controllable Text Generation',
    definition: 'The task of generating text that adheres to specific attributes or constraints, such as style, sentiment, or content. It encompasses both training-time approaches (modifying model weights) and decoding-time methods (guiding generation without changing the model).',
    category: 'ai',
    relatedTerms: ['Text Generation', 'Decoding Strategy', 'Contrastive Decoding', 'Language Model']
  },
  {
    term: 'CTRL',
    acronym: 'State-of-the-art Controlled Text Generation',
    definition: 'A conditional transformer language model trained to associate control codes with specific text attributes, allowing for controlled generation by conditioning on these codes. It enables control over style, content, and task-specific behavior without additional training.',
    category: 'ai',
    relatedTerms: ['Controllable Text Generation', 'Transformer', 'Language Model', 'Control Codes']
  },
  {
    term: 'DEXPERTS',
    definition: 'A controllable text generation method that combines a base language model with "expert" and "antiexpert" models to guide generation. It re-weights token probabilities by contrasting distributions from models trained on desired and undesired text properties.',
    category: 'ai',
    relatedTerms: ['Controllable Text Generation', 'Contrastive Decoding', 'Text Generation', 'Language Model']
  },
  {
    term: 'Digital Audio Workstation',
    acronym: 'DAW',
    definition: 'Software used for recording, editing, and producing audio files. DAWs serve as the main environment where audio plugins are used to process sound.',
    category: 'audio',
    relatedTerms: ['VST', 'Audio Plugin', 'Audio Processing']
  },
  {
    term: 'Digital Signal Processing',
    acronym: 'DSP',
    definition: 'The manipulation of signals (like audio) after they have been converted to digital form. The foundation of most audio plugin technology.',
    category: 'audio',
    relatedTerms: ['Audio Signal Processing', 'Filter', 'Equalization']
  },
  {
    term: 'Entity Density',
    definition: 'A measure of information density in text, calculated as the ratio of entities (people, places, organizations, etc.) to tokens. Higher entity density indicates more information-rich content within the same word count.',
    category: 'ai',
    relatedTerms: ['Summarization', 'Information Density', 'Named Entity Recognition']
  },
  {
    term: 'Entity-sparse',
    definition: 'In text summarization, describes content with a low density of named entities (people, places, organizations, etc.) relative to the total word count. Entity-sparse summaries contain fewer information-rich elements per token.',
    category: 'ai',
    relatedTerms: ['Entity Density', 'Summarization', 'Information Density', 'Chain of Density']
  },
  {
    term: 'Exposure Bias',
    definition: 'A problem in sequence generation models where, during training, the model sees ground truth previous tokens but during inference must rely on its own generated tokens, creating a mismatch that can lead to error accumulation in longer generations.',
    category: 'ml',
    relatedTerms: ['Sequence Generation', 'Error Propagation', 'Teacher Forcing']
  },
  {
    term: 'FACTSCORE',
    definition: 'A metric for evaluating the factual accuracy of language model outputs, particularly for longform text generation. It uses retrieval-augmented language models to fact-check responses and provides a quantitative measure of factual correctness.',
    category: 'ai',
    relatedTerms: ['Evaluation Metrics', 'Factual Accuracy', 'Benchmark']
  },
  {
    term: 'Factored Generation',
    definition: 'A technique where a complex generation task is broken down into independent sub-tasks that are executed separately to avoid interference or bias between components. In verification contexts, it prevents verification questions from being influenced by previous generations.',
    category: 'ai',
    relatedTerms: ['Modular AI', 'Independent Reasoning', 'Decomposition']
  },
  {
    term: 'FUDGE',
    definition: 'A method for controllable text generation that uses a discriminator to guide generation toward desired attributes without modifying the base language model. The discriminator predicts whether text has the desired attributes and guides the generation process.',
    category: 'ai',
    relatedTerms: ['Controllable Text Generation', 'Text Generation', 'Discriminator', 'Attribute Control']
  },
  {
    term: 'Fine-tuning',
    definition: 'The process of further training a pre-trained model on a specific dataset to adapt it for a particular task or domain.',
    category: 'ml',
    relatedTerms: ['Machine Learning', 'Model Tuning', 'Temperature']
  },
  {
    term: 'Future Discriminator',
    definition: 'In controllable text generation, a model that predicts whether future text completions will have desired attributes. Used in methods like FUDGE to guide text generation without fine-tuning the base language model.',
    category: 'ai',
    relatedTerms: ['FUDGE', 'Controllable Text Generation', 'Discriminator', 'Text Generation']
  },
  {
    term: 'Fusion',
    definition: 'In text summarization, the process of combining information from multiple parts of a source document into a single, coherent statement. Fusion requires understanding and integrating related concepts rather than simply extracting sentences.',
    category: 'ai',
    relatedTerms: ['Summarization', 'Abstractiveness', 'Information Compression']
  },
  {
    term: 'Generative AI',
    definition: 'AI systems that can create new content such as text, images, audio, or code based on patterns learned from training data.',
    category: 'ai',
    relatedTerms: ['Text Generation', 'Image Generation', 'Content Creation']
  },
  {
    term: 'Graph of Thoughts',
    acronym: 'GoT',
    definition: 'A prompting framework that models LLM reasoning as an arbitrary graph, where thoughts are vertices and dependencies between thoughts are edges. It enables complex reasoning patterns like thought aggregation, branching, and backtracking beyond what Chain-of-Thought or Tree of Thoughts can provide.',
    category: 'ai',
    relatedTerms: ['Chain of Thought', 'Tree of Thoughts', 'Reasoning', 'Prompting']
  },
  {
    term: 'Greedy Decoding',
    definition: 'A text generation strategy where the model always selects the most probable next token at each step. While efficient, this approach often leads to repetitive and less diverse outputs compared to sampling methods.',
    category: 'ai',
    relatedTerms: ['Decoding Strategy', 'Token Generation', 'Text Generation']
  },
  {
    term: 'Plan-and-Solve Prompting',
    acronym: 'PS',
    definition: 'A zero-shot prompting method that guides language models to first devise a plan for solving a given problem by breaking it into subtasks, and then to execute those subtasks systematically. It helps reduce missing-step errors in complex reasoning tasks.',
    category: 'ai',
    relatedTerms: ['Zero-shot Learning', 'Chain of Thought', 'Prompting', 'Reasoning']
  },
  {
    term: 'Plan-and-Solve+ Prompting',
    acronym: 'PS+',
    definition: 'An enhanced version of Plan-and-Solve prompting that adds more detailed instructions, such as extracting relevant variables, calculating intermediate results, and paying attention to calculation accuracy. It helps reduce both calculation errors and missing-step errors in complex reasoning tasks.',
    category: 'ai',
    relatedTerms: ['Plan-and-Solve Prompting', 'Zero-shot Learning', 'Chain of Thought', 'Reasoning']
  },
  {
    term: 'Semantic Misunderstanding Error',
    definition: 'A type of error in language model reasoning where the model fails to correctly comprehend the problem statement or maintain coherent reasoning throughout the solution process. This error type is often due to limitations in the model\'s understanding capabilities.',
    category: 'ai',
    relatedTerms: ['Chain of Thought', 'Reasoning', 'Error Analysis', 'Large Language Model']
  },
  {
    term: 'Task Similarity',
    definition: 'A measure of how closely related different learning tasks are to each other. In meta-learning contexts, higher task similarity often leads to better transfer of knowledge between tasks and improved performance on new tasks.',
    category: 'ml',
    relatedTerms: ['Meta-Learning', 'Transfer Learning', 'Meta-in-context Learning', 'Task Adaptation']
  },
  {
    term: 'Temperature',
    definition: 'In AI text generation, a parameter that controls randomness. Higher values (e.g., 0.8) make output more random, while lower values (e.g., 0.2) make it more deterministic and focused.',
    category: 'ai',
    relatedTerms: ['Sampling', 'Text Generation', 'Randomness']
  },
  {
    term: 'Greedy Policy',
    definition: 'In reinforcement learning, a decision-making strategy that always selects the action with the highest estimated value. While simple and efficient, it lacks exploration and may get stuck in suboptimal solutions.',
    category: 'ml',
    relatedTerms: ['Reinforcement Learning', 'Exploration-Exploitation Trade-off', 'Upper Confidence Bound', 'Thompson Sampling']
  },
  {
    term: 'Hallucination',
    definition: 'A phenomenon where large language models generate content that is nonsensical, untruthful, or unfaithful to their training data. Hallucinations can produce convincing but factually incorrect information, posing significant challenges for applications requiring accuracy and reliability.',
    category: 'ai',
    relatedTerms: ['Factual Accuracy', 'Misinformation', 'Model Reliability', 'RLHF']
  },
  {
    term: 'Hyperparameter',
    definition: 'Configuration variables that control the behavior of machine learning algorithms but are not learned from the data. Examples include learning rate, batch size, and temperature in text generation.',
    category: 'ml',
    relatedTerms: ['Machine Learning', 'Model Tuning', 'Temperature']
  },
  {
    term: 'In-context Learning',
    acronym: 'ICL',
    definition: 'A technique where a language model learns to perform a task from examples provided within the prompt, without updating its parameters. The model improves at a given task after being provided with task-relevant demonstrations in the input context, adapting its behavior based solely on these examples.',
    category: 'ai',
    relatedTerms: ['Few-shot Learning', 'Prompting', 'Task Adaptation', 'Zero-shot Learning', 'Meta-in-context Learning']
  },
  {
    term: 'Kalman Filtering',
    definition: 'A recursive algorithm that estimates the state of a system from a series of noisy measurements. In reinforcement learning, it can be used to update beliefs about expected rewards based on observed outcomes.',
    category: 'ml',
    relatedTerms: ['Bayesian Inference', 'State Estimation', 'Sequential Estimation', 'Reinforcement Learning']
  },
  {
    term: 'KULLBACK-LEIBLER DIVERGENCE',
    acronym: 'KL-Divergence',
    definition: 'A measure of the difference between two probability distributions. In reinforcement learning, it can be used to quantify the difference between the predicted and actual distributions of rewards.',
    category: 'ml',
    relatedTerms: ['Probability Theory', 'Information Theory', 'Reinforcement Learning', 'Reward Distribution']
  },
  {
    term: 'Large Language Model',
    acronym: 'LLM',
    definition: 'A type of AI model trained on vast amounts of text data, capable of understanding and generating human-like text. Examples include GPT, LLaMA, and Claude.',
    category: 'ai',
    relatedTerms: ['Natural Language Processing', 'Text Generation', 'Transformer']
  },
  {
    term: 'Lead Bias',
    definition: 'In text summarization, the tendency to extract information primarily from the beginning (lead) of a document. This bias can result in summaries that miss important information located later in the text.',
    category: 'ai',
    relatedTerms: ['Summarization', 'Content Distribution', 'Information Extraction']
  },
  {
    term: 'Less Likely Brainstorming',
    definition: 'A text generation task designed to produce less likely but relevant hypotheses or interpretations, helping to reduce cognitive biases in decision-making. It aims to generate outputs that humans would consider to be less likely to happen but still relevant to the context, providing alternative perspectives.',
    category: 'ai',
    relatedTerms: ['Controllable Text Generation', 'Text Generation', 'Contrastive Learning', 'Cognitive Bias']
  },
  {
    term: 'Logical Thoughts',
    acronym: 'LoT',
    definition: 'A self-improvement prompting framework that enhances zero-shot chain-of-thought reasoning in large language models by leveraging principles from symbolic logic, particularly Reductio ad Absurdum. It implements a think-verify-revise cycle to systematically verify and rectify reasoning processes step by step.',
    category: 'ai',
    relatedTerms: ['Chain of Thought', 'Zero-shot Learning', 'Logical Reasoning', 'Reductio ad Absurdum']
  },
  {
    term: 'Logits',
    definition: 'The raw, unnormalized output values from a neural network before they are converted to probabilities. In text generation, logits represent how likely each token in the vocabulary is to be the next token.',
    category: 'ml',
    relatedTerms: ['Neural Network', 'Softmax', 'Probability Distribution']
  },
  {
    term: 'Margin Loss',
    definition: 'A loss function that encourages a model to maintain a margin between the scores of correct and incorrect predictions. In controllable text generation, it can be used to ensure that the model assigns higher probability to target sequences given the correct indicator than with an incorrect indicator.',
    category: 'ml',
    relatedTerms: ['Loss Function', 'Contrastive Learning', 'Controllable Text Generation', 'Model Training']
  },
  {
    term: 'Machine Learning',
    acronym: 'ML',
    definition: 'A subset of AI that enables systems to learn from data and improve from experience without being explicitly programmed to do so.',
    category: 'ml',
    relatedTerms: ['AI', 'Neural Network', 'Training']
  },
  {
    term: 'Mean-Squared Error',
    acronym: 'MSE',
    definition: 'A measure of the average squared difference between estimated values and actual values, commonly used as a loss function in regression problems to evaluate prediction accuracy.',
    category: 'ml',
    relatedTerms: ['Loss Function', 'Regression', 'Root Mean Squared Error', 'Evaluation Metric']
  },
  {
    term: 'Meta-in-context Learning',
    definition: 'A phenomenon where the in-context learning abilities of large language models can be recursively improved via in-context learning itself. By presenting a language model with multiple learning tasks in sequence, its ability to learn from context improves across tasks without any parameter updates.',
    category: 'ai',
    relatedTerms: ['In-context Learning', 'Few-shot Learning', 'Meta-Learning', 'Language Model']
  },
  {
    term: 'Meta-Learning',
    definition: 'A learning paradigm where a model learns how to learn efficiently from new data, often by training on a variety of learning tasks to extract common patterns that can be applied to new tasks.',
    category: 'ml',
    relatedTerms: ['Few-shot Learning', 'Transfer Learning', 'Meta-in-context Learning', 'Adaptation']
  },
  {
    term: 'Natural Language Processing',
    acronym: 'NLP',
    definition: 'A field of AI focused on enabling computers to understand, interpret, and generate human language in a valuable way.',
    category: 'ai',
    relatedTerms: ['Text Analysis', 'Language Understanding', 'Sentiment Analysis']
  },
  {
    term: 'Neural Network',
    definition: 'Computing systems inspired by the human brain\'s structure, consisting of interconnected nodes (neurons) that process and transmit information.',
    category: 'ml',
    relatedTerms: ['Deep Learning', 'Machine Learning', 'AI']
  },
  {
    term: 'pass@k',
    definition: 'A metric used to evaluate code generation models. It measures the probability that at least one correct solution appears among k generated samples for a given problem. Higher pass@k scores indicate better model performance.',
    category: 'ai',
    relatedTerms: ['Code Generation', 'Evaluation Metric', 'Sampling']
  },
  {
    term: 'Pattern-based Fine-tuning',
    acronym: 'PBFT',
    definition: 'A fine-tuning approach that uses the pre-trained language modeling head instead of a randomly initialized classifier. It requires specifying an input pattern to cast the task as a language modeling problem and defining a verbalizer that maps tokens to labels, making it effective for few-shot learning scenarios.',
    category: 'ai',
    relatedTerms: ['Fine-tuning', 'Few-shot Learning', 'Task Adaptation', 'Verbalizer']
  },
  {
    term: 'Pause Training',
    definition: 'A technique for enhancing transformer-based language models by allowing them to perform additional computations before generating a response, improving accuracy and reasoning.',
    category: 'ai',
    relatedTerms: ['Language Models', 'Training', 'Transformer']
  },
  {
    term: 'Post Hoc Explanation',
    definition: 'An explanation generated after a decision or conclusion has already been made. In AI reasoning, it involves having a model explain why a particular statement is true or false after the statement has been generated, which can help identify errors in reasoning.',
    category: 'ai',
    relatedTerms: ['Explainability', 'Model Interpretability', 'Reasoning', 'Decision Making']
  },
  {
    term: 'Probit Regression',
    definition: 'A type of regression where the dependent variable can take only two values, and the model uses the cumulative distribution function of the standard normal distribution to model probabilities.',
    category: 'ml',
    relatedTerms: ['Logistic Regression', 'Binary Classification', 'Generalized Linear Models', 'Statistical Modeling']
  },
  {
    term: 'Program-of-Thought Prompting',
    acronym: 'PoT',
    definition: 'A prompting technique that guides language models to generate a program (typically in Python) as a form of reasoning, then executes that program to obtain the final answer. This approach disentangles computation from reasoning, reducing calculation errors in mathematical tasks.',
    category: 'ai',
    relatedTerms: ['Chain of Thought', 'Code Generation', 'Reasoning', 'Prompting']
  },
  {
    term: 'Radial Basis Function Kernel',
    acronym: 'RBF Kernel',
    definition: 'A popular kernel function used in various kernelized learning algorithms that measures similarity between points based on their Euclidean distance, with similarity decreasing exponentially with distance.',
    category: 'ml',
    relatedTerms: ['Kernel Methods', 'Support Vector Machines', 'Gaussian Processes', 'Similarity Measures']
  },
  {
    term: 'Recurrent Neural Network',
    acronym: 'RNN',
    definition: 'A type of neural network designed to recognize patterns in sequences of data, such as text, genomes, handwriting, or audio signals.',
    category: 'ml',
    relatedTerms: ['LSTM', 'Neural Network', 'Sequence Modeling']
  },
  {
    term: 'Reductio ad Absurdum',
    definition: 'A logical principle used to establish a claim by assuming its negation and then showing that this assumption leads to an absurdity or contradiction. In AI reasoning, it helps verify the validity of logical steps by checking for contradictions in alternative hypotheses.',
    category: 'general',
    relatedTerms: ['Logical Reasoning', 'Contradiction', 'Proof by Contradiction', 'Symbolic Logic']
  },
  {
    term: 'Regret',
    definition: 'In reinforcement learning, the difference between the reward that could have been obtained by taking the optimal action and the reward actually obtained. Lower regret indicates better performance.',
    category: 'ml',
    relatedTerms: ['Reinforcement Learning', 'Multi-armed Bandit', 'Exploration-Exploitation', 'Performance Metric']
  },
  {
    term: 'Relative Uncertainty',
    acronym: 'RU',
    definition: 'In decision-making under uncertainty, the difference in uncertainty between available options. In bandit problems, it can be used to guide exploration toward more uncertain options.',
    category: 'ml',
    relatedTerms: ['Uncertainty Estimation', 'Exploration-Exploitation', 'Reinforcement Learning', 'Decision Making']
  },
  {
    term: 'Retrieval-Augmented Generation',
    acronym: 'RAG',
    definition: 'A technique that enhances language model outputs by incorporating information retrieved from external knowledge sources. This approach grounds model responses in factual information, reducing hallucinations and improving accuracy.',
    category: 'ai',
    relatedTerms: ['Knowledge Retrieval', 'Factual Grounding', 'Information Retrieval']
  },
  {
    term: 'Root Mean Squared Error',
    acronym: 'RMSE',
    definition: 'The square root of the mean squared error, used as a measure of the differences between values predicted by a model and the values observed. Lower RMSE indicates better fit.',
    category: 'ml',
    relatedTerms: ['Evaluation Metric', 'Regression', 'Loss Function', 'Model Accuracy']
  },
  {
    term: 'Self-Consistency with Chain of Thought',
    acronym: 'CoT-SC',
    definition: 'A prompting technique that generates multiple independent chains of thought and selects the one with the best output according to a scoring metric. This approach enhances reasoning by exploring different paths without requiring backtracking.',
    category: 'ai',
    relatedTerms: ['Chain of Thought', 'Reasoning', 'Prompting', 'Self-verification']
  },
  {
    term: 'Self-Critique',
    definition: 'A process where language models evaluate and revise their own outputs to improve quality, accuracy, or adherence to specific criteria. This involves the model analyzing its initial generation and making corrections based on that analysis.',
    category: 'ai',
    relatedTerms: ['Self-verification', 'Iterative Refinement', 'Error Correction']
  },
  {
    term: 'Softmax',
    definition: 'A mathematical function that converts a vector of numbers into a probability distribution. In language models, softmax is applied to logits to determine the probability of each possible next token.',
    category: 'ml',
    relatedTerms: ['Probability Distribution', 'Neural Network', 'Logits']
  },
  {
    term: 'State Evaluator',
    definition: 'In structured reasoning frameworks like Tree of Thoughts or Graph of Thoughts, a component that generates scores for each thought or partial solution. Evaluation can be conducted using an LLM itself or through human scoring to guide the search process.',
    category: 'ai',
    relatedTerms: ['Tree of Thoughts', 'Graph of Thoughts', 'Thought Generator', 'Reasoning']
  },
  {
    term: 'Static Analysis',
    definition: 'The process of examining code without executing it to identify potential errors, bugs, or security vulnerabilities. It focuses on structural properties of the code rather than runtime behavior.',
    category: 'general',
    relatedTerms: ['Code Analysis', 'Bug Detection', 'Software Quality']
  },
  {
    term: 'Summarization',
    definition: 'The process of creating concise versions of longer text while preserving key information and meaning, often used to distill research papers or documents.',
    category: 'ai',
    relatedTerms: ['Text Processing', 'Information Extraction', 'Content Reduction']
  },
  {
    term: 'Token',
    definition: 'The basic unit of text that language models process. Depending on the model, tokens can be characters, subwords, or whole words. Most modern language models use subword tokenization, where common words are single tokens and less common words are split into multiple tokens.',
    category: 'ai',
    relatedTerms: ['Tokenization', 'Language Model', 'Text Generation']
  },
  {
    term: 'Top-k Sampling',
    definition: 'A text generation strategy that restricts the model to choose from only the k most likely next tokens at each step, discarding all other options. This helps balance creativity and quality.',
    category: 'ai',
    relatedTerms: ['Decoding Strategy', 'Text Generation', 'Sampling']
  },
  {
    term: 'Top-p Sampling',
    acronym: 'Nucleus Sampling',
    definition: 'A text generation method that selects the next token from the smallest set of tokens whose cumulative probability exceeds a threshold p. This balances diversity and quality by considering only the most likely tokens.',
    category: 'ai',
    relatedTerms: ['Decoding Strategy', 'Text Generation', 'Sampling']
  },
  {
    term: 'Tree of Thoughts',
    acronym: 'ToT',
    definition: 'A prompting framework that models reasoning as a tree, where each node represents a partial solution. It enhances Chain-of-Thought by enabling branching exploration and backtracking from non-promising paths, using search algorithms like BFS or DFS to navigate the solution space.',
    category: 'ai',
    relatedTerms: ['Chain of Thought', 'Graph of Thoughts', 'Reasoning', 'Prompting']
  },
  {
    term: 'Transformer',
    definition: 'A neural network architecture that uses self-attention mechanisms to process sequential data, forming the foundation of modern language models like GPT and BERT.',
    category: 'ml',
    relatedTerms: ['Attention Mechanism', 'Language Model', 'Neural Network']
  },
  {
    term: 'Agent',
    definition: 'In reinforcement learning, an agent is the subject of a policy that interacts with the environment. In sequential decision-making problems, a smart policy will make decisions at every step by considering all available information collected up to that point.',
    category: 'ai',
    relatedTerms: ['Reinforcement Learning', 'Policy', 'Environment', 'Decision Making']
  },
  {
    term: 'Behavior Cloning',
    acronym: 'BC',
    definition: 'A supervised learning approach in offline reinforcement learning where a policy is trained to mimic the behavior demonstrated in a dataset. It suffers from compounding error when the learned policy deviates from the demonstration data.',
    category: 'ml',
    relatedTerms: ['Imitation Learning', 'Offline RL', 'Supervised Learning', 'Policy Learning']
  },
  {
    term: 'Compounding Error',
    definition: 'In imitation learning and behavior cloning, this refers to the accumulation of errors along a trajectory. When a learned policy makes a mistake, it leads to out-of-distribution states, which then lead to further mistakes, causing errors to compound over time.',
    category: 'ml',
    relatedTerms: ['Behavior Cloning', 'Imitation Learning', 'Distributional Shift', 'Error Propagation']
  },
  {
    term: 'Dynamics Model',
    definition: 'In reinforcement learning, the component of the environment that controls transitions between states based on actions taken. It defines how the state of the environment changes in response to an agent\'s actions.',
    category: 'ml',
    relatedTerms: ['Reinforcement Learning', 'Environment', 'State Transition', 'MDP']
  },
  {
    term: 'Environment',
    definition: 'In reinforcement learning, the external system with which an agent interacts. It includes the dynamics model that controls state transitions and the reward function that provides feedback to the agent.',
    category: 'ml',
    relatedTerms: ['Reinforcement Learning', 'Agent', 'Dynamics Model', 'Reward Function']
  },
  {
    term: 'Imitation Learning',
    acronym: 'IL',
    definition: 'A machine learning approach where a model learns to perform a task by mimicking demonstrations provided by an expert, rather than through explicit reward signals. Unlike offline RL, imitation learning has access to a dynamics model, allowing the current policy to be rolled out in the real environment, which helps alleviate the distributional shift problem.',
    category: 'ml',
    relatedTerms: ['Reinforcement Learning', 'Offline RL', 'Supervised Learning', 'Policy Learning']
  },
  {
    term: 'Inverse Reinforcement Learning',
    acronym: 'IRL',
    definition: 'A machine learning approach that infers the reward function from observed behavior, assuming the behavior is optimal or near-optimal with respect to an unknown reward function. It is a solution to imitation learning problems that first learns a reward model from demonstration data, then uses this reward model combined with a dynamics model to perform online reinforcement learning.',
    category: 'ml',
    relatedTerms: ['Reinforcement Learning', 'Imitation Learning', 'Reward Function', 'Dynamics Model']
  },
  {
    term: 'Learning from Demonstrations',
    acronym: 'LfD',
    definition: 'A reinforcement learning approach that leverages demonstration data as a warm-start for RL. It uses demonstrations to improve exploration in reward-sparse tasks, then returns to RL and a sparse reward function to further refine the policy learned from the demonstrations.',
    category: 'ml',
    relatedTerms: ['Reinforcement Learning', 'Imitation Learning', 'Exploration', 'Sparse Rewards']
  },
  {
    term: 'Markov Decision Process',
    acronym: 'MDP',
    definition: 'A mathematical framework for modeling decision-making in situations where outcomes are partly random and partly under the control of a decision-maker. It provides the formal basis for reinforcement learning algorithms. Formally represented as M = {S, A, T, R, ρ0, γ}, where S is the state space, A is the action space, T is the transition dynamics, R is the reward function, ρ0 is the initial state distribution, and γ is the discount factor.',
    category: 'ml',
    relatedTerms: ['Reinforcement Learning', 'Decision Making', 'State Transition', 'Reward Function']
  },
  {
    term: 'Offline Reinforcement Learning',
    acronym: 'Offline RL',
    definition: 'A paradigm in reinforcement learning where an agent learns from a fixed dataset of experiences without further interaction with the environment. It addresses situations where online interaction is expensive, risky, or impractical. Key challenges include distributional shift and the inability to explore beyond the demonstration data.',
    category: 'ml',
    relatedTerms: ['Reinforcement Learning', 'Imitation Learning', 'Supervised Learning', 'Policy Learning']
  },
  {
    term: 'Online Reinforcement Learning',
    acronym: 'Online RL',
    definition: 'A reinforcement learning setting where an agent actively interacts with the environment through trial and error, receiving immediate feedback in the form of rewards. The agent can explore the environment and learn from its experiences in real-time.',
    category: 'ml',
    relatedTerms: ['Reinforcement Learning', 'Exploration', 'Trial and Error', 'Policy Learning']
  },
  {
    term: 'Policy',
    definition: 'In reinforcement learning, a mapping from states to actions that defines how an agent behaves in its environment. The policy determines which action to take in each state to maximize expected cumulative reward.',
    category: 'ml',
    relatedTerms: ['Reinforcement Learning', 'Agent', 'State', 'Action']
  },
  {
    term: 'Prompt-OIRL',
    definition: 'A method for query-dependent prompt optimization based on offline inverse reinforcement learning. It uses offline datasets from existing evaluations to craft a reward model for evaluating and optimizing prompts on a query-specific level, making prompt optimization more effective and cost-efficient.',
    category: 'ai',
    relatedTerms: ['Prompting', 'Inverse Reinforcement Learning', 'Offline RL', 'LLM']
  },
  {
    term: 'Proximal Policy Optimization',
    acronym: 'PPO',
    definition: 'A reinforcement learning algorithm that uses a clipped surrogate objective function to ensure policy updates are neither too large nor too small, leading to more stable training. In RLHF, PPO is commonly used due to its stability gained from on-policy data and conservative policy updates.',
    category: 'ml',
    relatedTerms: ['Reinforcement Learning', 'Policy Gradient', 'Stable Training']
  },
  {
    term: 'Reinforcement Learning',
    acronym: 'RL',
    definition: 'A machine learning approach where an agent learns to make decisions by taking actions in an environment to maximize a reward signal. Unlike supervised learning, RL doesn\'t require labeled examples but learns through trial and error. The fundamental objective is to find a policy that maximizes the expected cumulative reward over time.',
    category: 'ml',
    relatedTerms: ['Agent', 'Environment', 'Policy', 'Reward Function']
  },
  {
    term: 'Reinforcement Learning from Human Feedback',
    acronym: 'RLHF',
    definition: 'A technique used to align language models with human preferences by using human feedback to train a reward model, which is then used to fine-tune the language model using reinforcement learning. RLHF can be understood as online inverse reinforcement learning with offline demonstration data, leveraging the known dynamics model of language generation to overcome the challenges of offline RL.',
    category: 'ai',
    relatedTerms: ['Reinforcement Learning', 'Human Feedback', 'Reward Model', 'Language Model']
  },
  {
    term: 'Reward Function',
    definition: 'In reinforcement learning, a function that maps states or state-action pairs to numerical values, indicating the desirability of being in a particular state or taking a specific action in a state. In RLHF, a reward model is trained to mimic human preferences and provide feedback to guide the language model.',
    category: 'ml',
    relatedTerms: ['Reinforcement Learning', 'Agent', 'Dynamics Model', 'Reward Function']
  },
  {
    term: 'Supervised Fine-Tuning',
    acronym: 'SFT',
    definition: 'A process where a pre-trained language model is further trained on a specific dataset with human-labeled examples to improve its performance on a particular task or to align its outputs with human preferences. In the context of LLM alignment, SFT corresponds to behavior cloning and can suffer from compounding error problems that RLHF helps address.',
    category: 'ml',
    relatedTerms: ['Machine Learning', 'Model Tuning', 'Temperature']
  },
  {
    term: 'Uncertainty in Clinical Decision Making',
    definition: 'The inherent ambiguity in medical diagnosis and treatment decisions. When facing uncertainty, clinicians may resort to various cognitive strategies that can lead to biases and interpretation errors, such as confirmation bias or premature closure.',
    category: 'general',
    relatedTerms: ['Cognitive Bias', 'Confirmation Bias', 'Diagnostic Error', 'Less Likely Brainstorming']
  },
  {
    term: 'Virtual Studio Technology',
    acronym: 'VST',
    definition: 'An interface standard for connecting audio synthesizer and effect plugins to audio editors and recording systems, widely used in digital audio workstations.',
    category: 'audio',
    relatedTerms: ['Plugin', 'DAW', 'Audio Processing']
  },
  {
    term: 'Zero-shot',
    definition: 'A capability where AI models can perform tasks they weren\'t explicitly trained on, without any examples. In code generation, zero-shot refers to generating code based solely on a description, without seeing similar examples first.',
    category: 'ai',
    relatedTerms: ['Few-shot', 'Prompt Engineering', 'Transfer Learning']
  },
  {
    term: 'Abstract Syntax Tree',
    acronym: 'AST',
    definition: 'A tree representation of the abstract syntactic structure of source code. Used in compilers, code analysis tools, and programming language processing to analyze code structure, detect patterns, and identify potential issues like API misuse.',
    category: 'general',
    relatedTerms: ['Code Analysis', 'Static Analysis', 'Compiler Design']
  },
  {
    term: 'API Misuse',
    definition: 'The improper implementation of Application Programming Interface (API) calls that may lead to bugs, security vulnerabilities, or system failures. Common examples include missing exception handling, improper resource management, and violation of API usage patterns.',
    category: 'general',
    relatedTerms: ['Software Engineering', 'Code Quality', 'Error Handling']
  },
  {
    term: 'Functional Correctness',
    definition: 'A measure of whether code produces the expected output for given inputs, regardless of its implementation details or potential long-term reliability issues. Often the primary focus of code evaluation benchmarks.',
    category: 'general',
    relatedTerms: ['Software Testing', 'Code Quality', 'Verification']
  },
  {
    term: 'ROBUSTAPI',
    definition: 'A benchmark designed to evaluate the reliability and robustness of code generated by large language models, focusing on proper API usage rather than just functional correctness. It analyzes code using Abstract Syntax Trees to detect API misuse patterns.',
    category: 'ai',
    relatedTerms: ['API Misuse', 'Code Generation', 'LLM Evaluation']
  },
  {
    term: 'State Evaluator',
    definition: 'In structured reasoning frameworks like Tree of Thoughts or Graph of Thoughts, a component that generates scores for each thought or partial solution. Evaluation can be conducted using an LLM itself or through human scoring to guide the search process.',
    category: 'ai',
    relatedTerms: ['Tree of Thoughts', 'Graph of Thoughts', 'Thought Generator', 'Reasoning']
  },
  {
    term: 'Static Analysis',
    definition: 'The process of examining code without executing it to identify potential errors, bugs, or security vulnerabilities. It focuses on structural properties of the code rather than runtime behavior.',
    category: 'general',
    relatedTerms: ['Code Analysis', 'Bug Detection', 'Software Quality']
  },
  {
    term: 'Beam Search',
    definition: 'A search algorithm that explores a graph by expanding the most promising nodes in a limited set. In code generation and language model scaffolding, it maintains a set of top-k solutions and expands them iteratively, selecting the best solutions at each step based on a utility function.',
    category: 'ai',
    relatedTerms: ['Tree Search', 'Code Generation', 'Language Model Scaffolding', 'Optimization']
  },
  {
    term: 'Genetic Algorithm',
    definition: 'A metaheuristic optimization algorithm inspired by natural selection. In code generation, it creates a population of candidate solutions, evaluates their fitness, and evolves them through operations like crossover and mutation to produce better solutions over successive generations.',
    category: 'ml',
    relatedTerms: ['Evolutionary Algorithm', 'Optimization', 'Metaheuristic', 'Code Generation']
  },
  {
    term: 'Language Model Scaffolding',
    definition: 'A program that structures multiple calls to language models to generate better outputs for a specific task. Scaffolding programs typically provide a framework for systematic reasoning, exploration, or optimization when using language models.',
    category: 'ai',
    relatedTerms: ['Code Generation', 'Language Model', 'Prompt Engineering', 'Chain of Thought']
  },
  {
    term: 'Multi-Armed Prompt Bandit',
    definition: 'An approach to prompt optimization that treats different prompt variations as arms in a multi-armed bandit problem. It balances exploration of new prompts with exploitation of known good prompts to maximize performance over time.',
    category: 'ai',
    relatedTerms: ['Reinforcement Learning', 'Prompt Engineering', 'Exploration-Exploitation Tradeoff']
  },
  {
    term: 'Recursively Self-Improving Code Generation',
    definition: 'A technique where code that applies a language model to improve solutions is applied recursively to improve itself within a defined scope. Unlike full recursive self-improvement, only the scaffolding code is improved while the underlying language model remains unchanged.',
    category: 'ai',
    relatedTerms: ['Code Generation', 'Self-Improvement', 'Language Model Scaffolding', 'Optimization']
  },
  {
    term: 'Self-Taught Optimizer',
    acronym: 'STOP',
    definition: 'A method in which code that applies a language model to improve arbitrary solutions is applied recursively to improve itself. It starts with a seed improver and iteratively refines it by using the improver to improve itself, evaluated based on its performance on downstream tasks.',
    category: 'ai',
    relatedTerms: ['Recursively Self-Improving Code Generation', 'Language Model Scaffolding', 'Optimization']
  },
  {
    term: 'Simulated Annealing',
    definition: 'A probabilistic optimization technique that mimics the physical process of annealing in metallurgy. In code generation, it gradually decreases a temperature parameter that controls the probability of accepting worse solutions, allowing escape from local optima early in the search while converging to a good solution later.',
    category: 'ml',
    relatedTerms: ['Optimization', 'Metaheuristic', 'Code Generation', 'Local Search']
  },
  {
    term: 'Extrinsic Hallucination',
    definition: 'In text summarization, a type of factual error where the summary includes information that is not present in the source text. This is contrasted with intrinsic hallucinations, which involve inconsistencies between information in the summary and the source.',
    category: 'ai',
    relatedTerms: ['Hallucination', 'Factual Accuracy', 'Summarization', 'Intrinsic Hallucination']
  },
  {
    term: 'Intrinsic Hallucination',
    definition: 'In text summarization, a type of factual error where there are inconsistencies between the factual information in the summary and the source text. This differs from extrinsic hallucinations, which introduce entirely new information not found in the source.',
    category: 'ai',
    relatedTerms: ['Hallucination', 'Factual Accuracy', 'Summarization', 'Extrinsic Hallucination']
  },
  {
    term: 'Factual Consistency',
    definition: 'The degree to which a generated summary accurately represents the factual information contained in the source text without introducing errors or misrepresentations. A key quality metric in text summarization.',
    category: 'ai',
    relatedTerms: ['Summarization', 'Hallucination', 'Evaluation Metrics', 'Factual Accuracy']
  },
  {
    term: 'Cross-lingual Summarization',
    definition: 'A text summarization task where the summary is generated in a different language than the source text, requiring both compression of information and translation between languages.',
    category: 'ai',
    relatedTerms: ['Summarization', 'Machine Translation', 'Natural Language Processing']
  },
  {
    term: 'Multi-news Summarization',
    definition: 'A text summarization task that involves condensing multiple news articles on the same topic into a single coherent summary, requiring information fusion and redundancy elimination across sources.',
    category: 'ai',
    relatedTerms: ['Summarization', 'Information Fusion', 'Multi-document Summarization']
  },
  {
    term: 'Dialogue Summarization',
    definition: 'A text summarization task focused on condensing conversations or dialogues into concise summaries that capture the key points, decisions, or outcomes of the discussion.',
    category: 'ai',
    relatedTerms: ['Summarization', 'Conversation Analysis', 'Natural Language Processing']
  },
  {
    term: 'Code Summarization',
    definition: 'A specialized text summarization task that involves generating natural language descriptions of source code functionality, helping developers understand code purpose and behavior without reading the implementation details.',
    category: 'ai',
    relatedTerms: ['Summarization', 'Code Understanding', 'Documentation Generation']
  },
  {
    term: 'Extrinsic Evaluation',
    definition: 'Measuring the effectiveness of a summary by using it as input to another task (e.g., question-answering or decision-making), to determine if essential information has been retained. This approach evaluates summaries based on their practical utility rather than linguistic properties.',
    category: 'ai',
    relatedTerms: ['Evaluation Metrics', 'Summarization', 'Task-based Evaluation']
  },
  {
    term: 'Customized Summarization',
    definition: 'An approach to text summarization that tailors summaries to individual user preferences, reading history, or expertise level, personalizing the content selection and presentation to better meet specific user needs.',
    category: 'ai',
    relatedTerms: ['Personalization', 'Summarization', 'User Modeling']
  },
  {
    term: 'Real-time Summarization',
    definition: 'The process of condensing information streams (like live broadcasts, social media feeds, or financial data) as they are generated, providing up-to-date summaries of evolving content with minimal latency.',
    category: 'ai',
    relatedTerms: ['Summarization', 'Stream Processing', 'Information Filtering']
  },
  {
    term: 'Interactive Summarization',
    definition: 'A summarization approach where the system engages with users during the summarization process, soliciting clarification or feedback to refine the summary and better meet user information needs.',
    category: 'ai',
    relatedTerms: ['Summarization', 'Human-AI Interaction', 'User Feedback']
  },
  {
    term: 'Pause Token',
    definition: 'A special token appended to a language model\'s input to delay the model\'s output generation, allowing for additional computational processing before producing a response. Unlike regular tokens, pause tokens are not meant to be predicted by the model during training.',
    category: 'ai',
    relatedTerms: ['Transformer', 'Language Model', 'Computational Pathways', 'Delayed Computation']
  },
  {
    term: 'Pause-Training',
    definition: 'A technique for training language models with special pause tokens that delay output generation, enabling the model to perform additional computation before generating a response. Includes both pause-pretraining (inserting pause tokens during initial model training) and pause-finetuning (using pause tokens during task-specific training).',
    category: 'ai',
    relatedTerms: ['Pause Token', 'Language Model', 'Transformer', 'Computational Pathways']
  },
  {
    term: 'Pause-Inference',
    definition: 'A technique where special pause tokens are appended to a language model\'s input during inference to delay the model\'s response, allowing for additional computational processing that can improve performance on complex tasks without increasing the model\'s parameter count.',
    category: 'ai',
    relatedTerms: ['Pause Token', 'Inference', 'Computational Pathways', 'Delayed Computation']
  },
  {
    term: 'Computational Pathway',
    definition: 'In transformer-based language models, the flow of information through the model\'s layers when processing tokens. Pause tokens create wider computational pathways by allowing the model to process additional vectors before generating an output token.',
    category: 'ai',
    relatedTerms: ['Transformer', 'Self-Attention', 'Pause Token', 'Language Model']
  },
  {
    term: 'Delayed Computation',
    definition: 'A paradigm in language model inference where the model\'s output is intentionally delayed to allow for additional processing, potentially improving performance on complex tasks by expanding the computational width without increasing the model\'s parameter count.',
    category: 'ai',
    relatedTerms: ['Pause Token', 'Computational Pathway', 'Transformer', 'Implementation Capacity']
  },
  {
    term: 'Implementation Capacity',
    definition: 'The number of operations a language model can effectively implement for a given input, which is distinct from its raw parameter count. In standard transformers, this capacity is bottlenecked by the number of input tokens, but can be expanded through techniques like pause tokens.',
    category: 'ai',
    relatedTerms: ['Transformer', 'Computational Pathway', 'Parameter Count', 'Model Capacity']
  },
  {
    term: 'Computational Width',
    definition: 'In transformer-based language models, the number of parallel computations performed per layer, typically equal to the number of input tokens. Techniques like pause tokens increase computational width by allowing the model to process additional vectors before generating an output.',
    category: 'ai',
    relatedTerms: ['Transformer', 'Computational Pathway', 'Parallel Processing', 'Pause Token']
  },
  {
    term: 'TreePrompt',
    definition: 'A prompt construction paradigm for visual grounding that follows the structure of syntax trees, composing prompts in a bottom-up manner to provide explainability. It deconstructs complex sentences into tree structures and uses modular networks to generate intermediate prompts at each node, making the reasoning process interpretable.',
    category: 'ai',
    relatedTerms: ['Visual Grounding', 'Prompt Tuning', 'Explainable AI', 'Dependency Parsing Tree']
  },
  {
    term: 'Visual Grounding',
    acronym: 'VG',
    definition: 'A computer vision task that aims to identify and localize objects or regions in an image that are referred to by natural language expressions. Also known as referring expression comprehension, it bridges the gap between visual and textual understanding.',
    category: 'ai',
    relatedTerms: ['Referring Expression Comprehension', 'Vision-Language Models', 'Object Detection', 'Multimodal Learning']
  },
  {
    term: 'Prompt Tuning',
    definition: 'A parameter-efficient fine-tuning approach that keeps the parameters of pre-trained models fixed while optimizing a small set of continuous vectors (prompts) that are prepended to the input. This technique adapts large pre-trained models to downstream tasks with minimal computational cost.',
    category: 'ai',
    relatedTerms: ['Transfer Learning', 'Fine-tuning', 'Pre-trained Models', 'Parameter-Efficient Learning']
  },
  {
    term: 'Dependency Parsing Tree',
    acronym: 'DPT',
    definition: 'A syntactic structure that represents grammatical relationships between words in a sentence, showing which words depend on or modify other words. In visual grounding and NLP tasks, DPTs help decompose complex sentences into structured representations for better understanding.',
    category: 'ai',
    relatedTerms: ['Natural Language Processing', 'Syntactic Analysis', 'TreePrompt', 'Sentence Parsing']
  },
  {
    term: 'Continuous Prompt',
    definition: 'In prompt tuning, a set of learnable vector embeddings that are optimized during training rather than using fixed, discrete text prompts. These continuous vectors serve as task-specific parameters that can be learned to effectively trigger knowledge from pre-trained models.',
    category: 'ai',
    relatedTerms: ['Prompt Tuning', 'Discrete Prompt', 'Transfer Learning', 'Parameter-Efficient Learning']
  },
  {
    term: 'Discrete Prompt',
    definition: 'In prompt engineering, natural language text templates manually designed to guide pre-trained models toward specific tasks. Unlike continuous prompts, discrete prompts use actual words and phrases rather than learned vector embeddings.',
    category: 'ai',
    relatedTerms: ['Prompt Tuning', 'Continuous Prompt', 'Prompt Engineering', 'Natural Language Processing']
  },
  {
    term: 'Referring Expression Comprehension',
    definition: 'A task in computer vision and natural language processing that involves localizing objects or regions in an image based on natural language descriptions. It requires understanding both visual content and linguistic references to establish correct correspondences.',
    category: 'ai',
    relatedTerms: ['Visual Grounding', 'Vision-Language Models', 'Object Detection', 'Natural Language Understanding']
  },
  {
    term: 'MEMWALKER',
    definition: 'An interactive reading approach that treats language models as agents that navigate through long texts. It processes long contexts into a tree of summary nodes and allows the model to traverse this tree to find relevant information when answering queries, enabling effective processing beyond context window limits.',
    category: 'ai',
    relatedTerms: ['Interactive Reading', 'Memory Tree', 'Context Window', 'Long-Text Understanding']
  },
  {
    term: 'Memory Tree',
    definition: 'A hierarchical data structure used in MEMWALKER where long text is segmented into chunks that are summarized into nodes. These summary nodes are recursively summarized into higher-level nodes until reaching a root node, creating a navigable structure for language models to traverse when seeking information.',
    category: 'ai',
    relatedTerms: ['MEMWALKER', 'Tree Structure', 'Text Summarization', 'Hierarchical Representation']
  },
  {
    term: 'Interactive Reading',
    definition: 'A paradigm where language models actively decide how to process text through iterative prompting rather than consuming the entire text at once. This approach allows models to navigate through content, focusing on relevant sections based on specific queries or tasks.',
    category: 'ai',
    relatedTerms: ['MEMWALKER', 'Iterative Prompting', 'Long-Text Understanding', 'Reasoning Agent']
  },
  {
    term: 'Context Window',
    definition: 'The maximum number of tokens a language model can process at once. This limitation affects how models handle long texts and is a fundamental constraint of the self-attention mechanism used in transformer-based models.',
    category: 'ai',
    relatedTerms: ['Transformer', 'Self-Attention', 'Token Limit', 'Long-Text Understanding']
  },
  {
    term: 'Working Memory',
    definition: 'In interactive reading systems like MEMWALKER, a mechanism that carries information from previously visited nodes throughout the navigation trajectory. This allows the model to maintain context and make informed decisions as it traverses through content structures.',
    category: 'ai',
    relatedTerms: ['MEMWALKER', 'Interactive Reading', 'Memory Tree', 'Context Retention']
  },
  {
    term: 'Reasoning Agent',
    definition: 'A language model that acts as an autonomous agent, making decisions about how to process information through reasoning. In systems like MEMWALKER, the agent navigates content structures by generating reasons to justify navigation choices before taking actions.',
    category: 'ai',
    relatedTerms: ['Interactive Reading', 'Decision Making', 'Reasoning Justification', 'MEMWALKER']
  },
  {
    term: 'Long-Text Understanding',
    definition: 'The capability of language models to comprehend and extract information from texts that exceed their standard context window. This involves techniques like recurrence, retrieval, context window extension, or interactive reading to overcome the limitations of fixed-length input processing.',
    category: 'ai',
    relatedTerms: ['Context Window', 'MEMWALKER', 'Interactive Reading', 'Recurrence']
  },
  {
    term: 'Recurrence',
    definition: 'A technique for processing long texts where information from preceding text segments is carried forward into subsequent segments. While this allows for handling infinite-length sequences, it often struggles to retain information from earlier segments effectively.',
    category: 'ai',
    relatedTerms: ['Long-Text Understanding', 'Context Window', 'Sequential Processing', 'Information Retention']
  },
  {
    term: 'Write Once, Run Anywhere',
    acronym: 'WORA',
    definition: 'A slogan created by Sun Microsystems to describe Java\'s cross-platform capabilities. It refers to the ability to write code once and run it on any device with a Java Virtual Machine, regardless of the underlying hardware or operating system.',
    category: 'general',
    relatedTerms: ['Java', 'Cross-platform Development', 'Virtual Machine']
  },
  {
    term: 'Code Reliability',
    definition: 'The ability of code to consistently perform its intended functions without failures, even under varying conditions or extended use. Goes beyond functional correctness to include proper error handling, resource management, and adherence to API usage patterns.',
    category: 'general',
    relatedTerms: ['Software Quality', 'Robustness', 'Error Handling']
  },
  {
    term: 'Code Robustness',
    definition: 'The capability of code to handle unexpected inputs, edge cases, and environmental changes without failing. Robust code includes proper error handling, input validation, and follows best practices for API usage.',
    category: 'general',
    relatedTerms: ['Error Handling', 'Defensive Programming', 'Software Quality']
  },
  {
    term: 'Chain of Verification',
    acronym: 'CoVe',
    definition: 'A method to reduce hallucinations in large language models by having the model verify its own outputs through a structured process: (1) draft an initial response, (2) plan verification questions to fact-check the draft, (3) answer those questions independently to avoid bias, and (4) generate a final verified response.',
    category: 'ai',
    relatedTerms: ['Hallucination', 'Self-verification', 'Factual Accuracy', 'Chain of Thought']
  },
  {
    term: 'Few-shot Chain-of-Thought',
    definition: 'A prompting technique that provides the language model with a few manually crafted step-by-step reasoning demonstrations before asking it to solve a new problem, enabling the model to generate intermediate reasoning steps and improve task accuracy.',
    category: 'ai',
    relatedTerms: ['Chain of Thought', 'Few-shot Learning', 'Prompting', 'Reasoning']
  },
  {
    term: 'Hallucination',
    definition: 'In AI, the generation of plausible yet incorrect factual information by language models. Hallucinations occur when models produce content that appears convincing but is factually inaccurate or has no basis in their training data or provided context.',
    category: 'ai',
    relatedTerms: ['Factual Accuracy', 'Misinformation', 'Model Reliability']
  },
  {
    term: 'Entity-sparse',
    definition: 'In text summarization, describes content with a low density of named entities (people, places, organizations, etc.) relative to the total word count. Entity-sparse summaries contain fewer information-rich elements per token.',
    category: 'ai',
    relatedTerms: ['Entity Density', 'Summarization', 'Information Density', 'Chain of Density']
  },
  {
    term: 'Exposure Bias',
    definition: 'A problem in sequence generation models where, during training, the model sees ground truth previous tokens but during inference must rely on its own generated tokens, creating a mismatch that can lead to error accumulation in longer generations.',
    category: 'ml',
    relatedTerms: ['Sequence Generation', 'Error Propagation', 'Teacher Forcing']
  },
  {
    term: 'Factored Generation',
    definition: 'A technique where a complex generation task is broken down into independent sub-tasks that are executed separately to avoid interference or bias between components. In verification contexts, it prevents verification questions from being influenced by previous generations.',
    category: 'ai',
    relatedTerms: ['Modular AI', 'Independent Reasoning', 'Decomposition']
  },
  {
    term: 'Self-Consistency with Chain of Thought',
    acronym: 'CoT-SC',
    definition: 'A prompting technique that generates multiple independent chains of thought and selects the one with the best output according to a scoring metric. This approach enhances reasoning by exploring different paths without requiring backtracking.',
    category: 'ai',
    relatedTerms: ['Chain of Thought', 'Reasoning', 'Prompting', 'Self-verification']
  },
  {
    term: 'Self-Critique',
    definition: 'A process where language models evaluate and revise their own outputs to improve quality, accuracy, or adherence to specific criteria. This involves the model analyzing its initial generation and making corrections based on that analysis.',
    category: 'ai',
    relatedTerms: ['Self-verification', 'Iterative Refinement', 'Error Correction']
  },
  {
    term: 'Retrieval-Augmented Generation',
    acronym: 'RAG',
    definition: 'A technique that enhances language model outputs by incorporating information retrieved from external knowledge sources. This approach grounds model responses in factual information, reducing hallucinations and improving accuracy.',
    category: 'ai',
    relatedTerms: ['Knowledge Retrieval', 'Factual Grounding', 'Information Retrieval']
  },
  {
    term: 'FACTSCORE',
    definition: 'A metric for evaluating the factual accuracy of language model outputs, particularly for longform text generation. It uses retrieval-augmented language models to fact-check responses and provides a quantitative measure of factual correctness.',
    category: 'ai',
    relatedTerms: ['Evaluation Metrics', 'Factual Accuracy', 'Benchmark']
  },
  {
    term: 'Logical Thoughts',
    acronym: 'LoT',
    definition: 'A self-improvement prompting framework that enhances zero-shot chain-of-thought reasoning in large language models by leveraging principles from symbolic logic, particularly Reductio ad Absurdum. It implements a think-verify-revise cycle to systematically verify and rectify reasoning processes step by step.',
    category: 'ai',
    relatedTerms: ['Chain of Thought', 'Zero-shot Learning', 'Logical Reasoning', 'Reductio ad Absurdum']
  },
  {
    term: 'Reductio ad Absurdum',
    definition: 'A logical principle used to establish a claim by assuming its negation and then showing that this assumption leads to an absurdity or contradiction. In AI reasoning, it helps verify the validity of logical steps by checking for contradictions in alternative hypotheses.',
    category: 'general',
    relatedTerms: ['Logical Reasoning', 'Contradiction', 'Proof by Contradiction', 'Symbolic Logic']
  },
  {
    term: 'Zero-shot Chain-of-Thought',
    acronym: 'Zero-shot-CoT',
    definition: 'A prompting technique that improves the reasoning capabilities of large language models without requiring examples, typically by adding simple phrases like "Let\'s think step by step" before the model generates a response. This encourages the model to break down complex problems into smaller, more manageable steps.',
    category: 'ai',
    relatedTerms: ['Chain of Thought', 'Zero-shot Learning', 'Prompting Techniques', 'Reasoning']
  },
  {
    term: 'Post Hoc Explanation',
    definition: 'An explanation generated after a decision or conclusion has already been made. In AI reasoning, it involves having a model explain why a particular statement is true or false after the statement has been generated, which can help identify errors in reasoning.',
    category: 'ai',
    relatedTerms: ['Explainable AI', 'Reasoning', 'Error Detection', 'Verification']
  },
  {
    term: 'Contraposition',
    definition: 'A logical principle stating that a conditional statement is logically equivalent to its contrapositive. For example, "If P, then Q" is equivalent to "If not Q, then not P". This principle can help language models approach reasoning problems from different angles.',
    category: 'general',
    relatedTerms: ['Logical Reasoning', 'Logical Equivalence', 'Conditional Statements', 'Symbolic Logic']
  },
  {
    term: 'Neurosymbolic Models',
    definition: 'AI systems that combine neural networks with symbolic representations and reasoning techniques. These models leverage the pattern recognition capabilities of neural networks while incorporating the explicit rule-based reasoning of symbolic systems, resulting in more interpretable and explainable AI.',
    category: 'ml',
    relatedTerms: ['Neural Networks', 'Symbolic AI', 'Hybrid AI', 'Explainable AI']
  },
  {
    term: 'Logical Equivalence',
    definition: 'A relationship between two statements that are either both true or both false under all possible interpretations. In language models, expressing the same logical content in different but equivalent ways can help the model approach reasoning problems from multiple perspectives.',
    category: 'general',
    relatedTerms: ['Symbolic Logic', 'Logical Reasoning', 'Contraposition', 'Logical Operators']
  },
  {
    term: 'Think-Verify-Revise Framework',
    definition: 'A structured approach for language models to solve complex problems by first generating an initial solution (Think), then critically evaluating it (Verify), and finally improving it based on the evaluation (Revise).',
    category: 'ai',
    relatedTerms: ['Reasoning', 'Self-verification', 'Iterative Improvement']
  },
  {
    term: 'Thought Generator',
    definition: 'In structured reasoning frameworks like Tree of Thoughts or Graph of Thoughts, a component that constructs new thoughts or partial solutions based on existing ones, enabling the exploration of different reasoning paths.',
    category: 'ai',
    relatedTerms: ['Tree of Thoughts', 'Graph of Thoughts', 'State Evaluator', 'Reasoning']
  },
  {
    term: 'In-context Learning',
    acronym: 'ICL',
    definition: 'A task adaptation strategy for language models that does not update model weights. Instead, it adapts a model to a task by conditioning it on a sequence of demonstrations (input-output pairs) within the model\'s context window, allowing the model to learn from examples without parameter updates.',
    category: 'ai',
    relatedTerms: ['Few-shot Learning', 'Prompting', 'Task Adaptation', 'Zero-shot Learning']
  },
  {
    term: 'Pattern-based Fine-tuning',
    acronym: 'PBFT',
    definition: 'A fine-tuning approach that uses the pre-trained language modeling head instead of a randomly initialized classifier. It requires specifying an input pattern to cast the task as a language modeling problem and defining a verbalizer that maps tokens to labels, making it effective for few-shot learning scenarios.',
    category: 'ai',
    relatedTerms: ['Fine-tuning', 'Few-shot Learning', 'Task Adaptation', 'Verbalizer']
  },
  {
    term: 'Verbalizer',
    definition: 'A mapping mechanism in pattern-based fine-tuning that connects tokens in a language model\'s vocabulary to specific labels or classes. For example, mapping "Yes" and "No" tokens to "entailment" and "not-entailment" classes in natural language inference tasks.',
    category: 'ai',
    relatedTerms: ['Pattern-based Fine-tuning', 'Few-shot Learning', 'Task Adaptation']
  },
  {
    term: 'Out-of-domain Generalization',
    acronym: 'OOD',
    definition: 'The ability of a model to perform well on data distributions different from those seen during training. In language models, this often refers to performance on challenge datasets designed to test whether models adopt particular heuristics or make predictions based on spurious correlations.',
    category: 'ml',
    relatedTerms: ['Generalization', 'Distribution Shift', 'Robustness', 'Challenge Datasets']
  },
  {
    term: 'Covariate Shift',
    definition: 'A type of distribution shift where the input distribution changes between training and testing, but the conditional distribution of outputs given inputs remains the same. In language models, this often manifests when evaluating on out-of-domain data that follows different patterns than the training data.',
    category: 'ml',
    relatedTerms: ['Distribution Shift', 'Out-of-domain Generalization', 'Robustness']
  },
  {
    term: 'Challenge Datasets',
    definition: 'Specialized evaluation datasets designed to test whether models adopt particular heuristics or make predictions based on spurious correlations. Examples include HANS for natural language inference and PAWS for paraphrase identification, which help assess model robustness beyond standard benchmarks.',
    category: 'ml',
    relatedTerms: ['Evaluation', 'Robustness', 'Out-of-domain Generalization', 'Spurious Correlations']
  },
  {
    term: 'Parameter-efficient Fine-tuning',
    definition: 'Methods that update only a small subset of a pre-trained model\'s parameters during adaptation to downstream tasks. These approaches allow reusing most of a model\'s weights across tasks, reducing storage requirements and enabling more efficient adaptation of large language models.',
    category: 'ml',
    relatedTerms: ['Fine-tuning', 'Task Adaptation', 'Transfer Learning', 'LoRA']
  },
  {
    term: 'Low-Rank Adaptation',
    acronym: 'LoRA',
    definition: 'A parameter-efficient fine-tuning method that injects trainable low-rank matrices into each layer of a transformer model, significantly reducing the number of trainable parameters while maintaining performance comparable to full fine-tuning.',
    category: 'ml',
    relatedTerms: ['Parameter-efficient Fine-tuning', 'Task Adaptation', 'Transfer Learning']
  },
  {
    term: 'Graph of Thoughts',
    acronym: 'GoT',
    definition: 'A prompting framework that models LLM reasoning as an arbitrary graph, where thoughts are vertices and dependencies between thoughts are edges. It enables complex reasoning patterns like thought aggregation, branching, and backtracking beyond what Chain-of-Thought or Tree of Thoughts can provide.',
    category: 'ai',
    relatedTerms: ['Chain of Thought', 'Tree of Thoughts', 'Reasoning', 'Prompting']
  },
  {
    term: 'Volume of Thought',
    definition: 'A metric for evaluating prompting strategies, defined as the number of LLM thoughts from which one can reach a given thought using directed edges. It measures how many thoughts potentially contributed to a final solution, with higher volumes indicating more complex reasoning patterns.',
    category: 'ai',
    relatedTerms: ['Graph of Thoughts', 'Tree of Thoughts', 'Reasoning', 'Prompting']
  },
  {
    term: 'Self-Consistency',
    acronym: 'SC',
    definition: 'A technique that improves the reliability of language model outputs by generating multiple reasoning paths for the same problem and selecting the most consistent answer through majority voting. It helps reduce the impact of randomness in model outputs.',
    category: 'ai',
    relatedTerms: ['Chain of Thought', 'Reasoning', 'Sampling', 'Ensemble Methods']
  },
  {
    term: 'Step Missing Error',
    definition: 'A type of error in language model reasoning where the model omits necessary intermediate steps in the reasoning process, particularly in complex multi-step problems. This often leads to incorrect final answers despite the model understanding the problem.',
    category: 'ai',
    relatedTerms: ['Chain of Thought', 'Reasoning', 'Error Analysis', 'Plan-and-Solve Prompting']
  },
  {
    term: 'RE2',
    definition: 'Re-Reading, a prompting technique that enhances reasoning in large language models by repeating the input question, allowing the model to process it twice. This approach improves understanding by enabling a pseudo-bidirectional encoding in unidirectional decoder-only LLMs, as the first pass provides global information for the second pass.',
    category: 'ai',
    relatedTerms: ['Chain of Thought', 'Prompting', 'Reasoning', 'Bidirectional Understanding']
  },
  {
    term: 'Bidirectional Understanding',
    definition: 'In language models, the ability to process text while considering both preceding and following context. Traditionally limited in decoder-only LLMs which have unidirectional attention, techniques like RE2 (Re-Reading) can simulate this capability by repeating input text, allowing tokens in the second pass to attend to later tokens from the first pass.',
    category: 'ai',
    relatedTerms: ['Attention Mechanism', 'Transformer', 'Unidirectional Attention', 'RE2']
  },
  {
    term: 'Unidirectional Attention',
    definition: 'A constraint in decoder-only language models where each token can only attend to previous tokens in the sequence, limiting the model\'s ability to understand the full context of a word in relation to words that follow it. This is in contrast to bidirectional attention used in encoder models.',
    category: 'ai',
    relatedTerms: ['Attention Mechanism', 'Transformer', 'Decoder-Only LLM', 'Bidirectional Understanding']
  },
  {
    term: 'Input Phase Understanding',
    definition: 'The process by which language models comprehend the initial query or problem statement before generating a response. Techniques like RE2 focus on enhancing this phase, in contrast to output-focused methods like Chain-of-Thought that primarily improve the reasoning process during response generation.',
    category: 'ai',
    relatedTerms: ['RE2', 'Prompting', 'Reasoning', 'Chain of Thought']
  },
  {
    term: 'Reasoning Gap',
    definition: 'The phenomenon where step-by-step reasoning through intermediate variables produces more accurate results than direct inference, particularly when the training data has local structure where strongly related variables frequently co-occur. This gap explains why chain-of-thought reasoning is effective in language models.',
    category: 'ai',
    relatedTerms: ['Chain of Thought', 'Local Structure', 'Intermediate Variables', 'Conditional Probability']
  },
  {
    term: 'Local Structure',
    definition: 'A property of training data where variables that strongly influence each other frequently co-occur in training samples, while variables with weak dependencies rarely appear together. This structure enables language models to learn accurate local statistical dependencies that can be chained together through reasoning.',
    category: 'ml',
    relatedTerms: ['Reasoning Gap', 'Chain of Thought', 'Data Distribution', 'Statistical Dependencies']
  },
  {
    term: 'Intermediate Variables',
    definition: 'In reasoning tasks, variables that connect observed information to target conclusions, allowing step-by-step inference. When language models generate values for these variables during chain-of-thought reasoning, they can make more accurate predictions by chaining together local statistical dependencies.',
    category: 'ai',
    relatedTerms: ['Chain of Thought', 'Reasoning Gap', 'Step-by-step Reasoning', 'Conditional Inference']
  },
  {
    term: 'Scaffolded Generation',
    definition: 'A reasoning technique where a language model is guided to reason through specific pre-determined intermediate variables before making a final prediction. This approach helps models leverage local statistical dependencies learned during training to make accurate inferences about variables rarely seen together.',
    category: 'ai',
    relatedTerms: ['Chain of Thought', 'Intermediate Variables', 'Reasoning Gap', 'Conditional Inference']
  },
  {
    term: 'Free Generation',
    definition: 'A reasoning approach where language models spontaneously generate their own intermediate variables and values before making a final prediction. This technique allows models to select which variables to reason through, potentially finding efficient reasoning paths through complex problems.',
    category: 'ai',
    relatedTerms: ['Chain of Thought', 'Intermediate Variables', 'Reasoning Gap', 'Self-directed Reasoning']
  },
  {
    term: 'D-separation',
    definition: 'A concept from probabilistic graphical models that identifies when a set of variables blocks all information flow between two other variables. In the context of reasoning, generating a d-separating set of intermediate variables ensures that all relevant information for prediction is captured in the reasoning process.',
    category: 'ml',
    relatedTerms: ['Bayesian Networks', 'Conditional Independence', 'Graphical Models', 'Intermediate Variables']
  },
  {
    term: 'Observation Distribution',
    definition: 'The distribution that determines which subsets of variables are observed together during training. In systems with local structure, this distribution tends to include variables that are close to each other in the underlying dependency graph, mimicking how humans experience the world from a first-person perspective.',
    category: 'ml',
    relatedTerms: ['Local Structure', 'Training Data', 'Data Distribution', 'First-person Perspective']
  },
  {
    term: 'Data Efficiency',
    definition: 'In the context of reasoning models, the ability to achieve high performance with less training data by leveraging structured reasoning approaches. Research shows that models trained on locally structured data with chain-of-thought reasoning can match the performance of models trained on much larger datasets.',
    category: 'ml',
    relatedTerms: ['Local Structure', 'Chain of Thought', 'Sample Efficiency', 'Training Data']
  },
  {
    term: 'Prompt Engineering',
    definition: 'The practice of crafting optimal textual inputs (prompts) for generative AI models to achieve specific outcomes. It involves understanding model capabilities, limitations, and the context of use, requiring domain knowledge and methodical approaches to tailor prompts for different contexts and applications.',
    category: 'ai',
    relatedTerms: ['Large Language Models', 'Chain of Thought', 'Prompt Design', 'Zero-shot Learning']
  },
  {
    term: 'Tree of Thought',
    acronym: 'ToT',
    definition: 'A prompting technique that enables language models to explore multiple reasoning pathways simultaneously, similar to how humans consider different possible solutions. The model creates a tree of alternative reasoning trajectories, evaluates each branch for logical consistency, and selects the most coherent line of reasoning.',
    category: 'ai',
    relatedTerms: ['Chain of Thought', 'Reasoning', 'Problem Solving', 'Decision Making']
  },
  {
    term: 'Automatic Prompt Engineering',
    acronym: 'APE',
    definition: 'A methodology that automates the process of prompt creation by using language models to generate, evaluate, and refine prompts. APE follows a cycle of prompt generation, scoring against metrics like clarity and specificity, and iterative refinement to optimize prompts for specific tasks.',
    category: 'ai',
    relatedTerms: ['Prompt Engineering', 'Prompt Optimization', 'Automated AI Systems', 'Meta-prompting']
  },
  {
    term: 'Retrieval Augmented Generation',
    acronym: 'RAG',
    definition: 'A technique that enhances language model outputs by dynamically incorporating external knowledge sources. RAG formulates queries from input prompts to fetch relevant information from diverse sources, integrating this retrieved content into the model\'s workflow to generate more informed and contextually relevant responses.',
    category: 'ai',
    relatedTerms: ['External Knowledge', 'Information Retrieval', 'Knowledge Integration', 'FLARE']
  },
  {
    term: 'Forward-looking Active Retrieval Augmented Generation',
    acronym: 'FLARE',
    definition: 'An advanced RAG technique that iteratively enhances language model outputs by predicting potential content and using these predictions to guide information retrieval. Unlike traditional RAG, FLARE engages in continuous, dynamic retrieval throughout the generation process, ensuring each segment is supported by relevant external information.',
    category: 'ai',
    relatedTerms: ['RAG', 'Dynamic Retrieval', 'Confidence-based Generation', 'Information Integration']
  },
  {
    term: 'Reasoning without Observation',
    acronym: 'ReWOO',
    definition: 'A technique for LLM-based agents that enables them to construct reasoning plans without immediate access to external data. The agent first develops a meta-plan for solving a problem, then executes this plan once relevant data becomes available, making it efficient for scenarios where data retrieval is costly or uncertain.',
    category: 'ai',
    relatedTerms: ['LLM Agents', 'Meta-planning', 'Reasoning', 'Plan Execution']
  },
  {
    term: 'Reason and Act',
    acronym: 'ReAct',
    definition: 'A framework that enhances language models\'s problem-solving capabilities by interleaving reasoning traces with actionable steps. This approach creates a dynamic feedback loop between thinking and acting, allowing the model to adjust its reasoning based on the outcomes of its actions.',
    category: 'ai',
    relatedTerms: ['LLM Agents', 'Reasoning', 'Action Planning', 'Problem Solving']
  },
  {
    term: 'Dialog-Enabled Resolving Agents',
    acronym: 'DERA',
    definition: 'A collaborative agent framework where multiple specialized agents engage in dialogue to resolve complex queries and make decisions. Each agent has a specific role (e.g., planner, researcher, critic) and they work together through structured dialogue, enabling nuanced problem-solving that mirrors human decision-making processes.',
    category: 'ai',
    relatedTerms: ['Multi-agent Systems', 'Collaborative AI', 'Dialogue Systems', 'Specialized Agents']
  },
  {
    term: 'Self-Consistency',
    definition: 'A technique that improves the reliability of language model outputs by generating multiple answers to the same question and selecting the most consistent answer. The approach assumes that similar responses generated independently increase the likelihood of accuracy, making it valuable for applications requiring factual precision.',
    category: 'ai',
    relatedTerms: ['Reliability', 'Ensemble Methods', 'Consistency Evaluation', 'Fact Verification']
  },
  {
    term: 'Reflection',
    definition: 'A process where language models review and critique their own outputs to improve quality. After generating an initial response, the model evaluates it against criteria like factual accuracy and logical consistency, then iteratively refines the response based on this self-assessment.',
    category: 'ai',
    relatedTerms: ['Self-evaluation', 'Iterative Refinement', 'Quality Improvement', 'Meta-cognition']
  },
  {
    term: 'Expert Prompting',
    definition: 'A technique that guides language models to simulate expert-level responses by adopting the persona of specialists in relevant fields. It often employs a multi-expert strategy where the model integrates insights from various expert perspectives, creating more nuanced and well-considered responses to complex queries.',
    category: 'ai',
    relatedTerms: ['Role-playing', 'Domain Expertise', 'Perspective Integration', 'Specialized Knowledge']
  },
  {
    term: 'Canonical Forms',
    definition: 'Standardized structures or templates in prompt engineering that guide language models to generate outputs with consistent formats. These forms act as modeling languages that shape the structure and delivery of responses, helping maintain quality and adherence to specific parameters.',
    category: 'ai',
    relatedTerms: ['Rails', 'Prompt Templates', 'Response Formatting', 'Structured Output']
  },
  {
    term: 'Rails',
    definition: 'A strategic approach in prompt engineering that directs language model outputs within predefined boundaries, ensuring relevance, safety, and factual integrity. Rails can include topical constraints, fact-checking mechanisms, or safeguards against circumventing operational constraints.',
    category: 'ai',
    relatedTerms: ['Canonical Forms', 'Output Constraints', 'Safety Mechanisms', 'Guided Generation']
  },
  {
    term: 'Affordances',
    definition: 'Functions defined within prompts that language models are explicitly instructed to use when responding. For example, telling a model to use a CALC() function when encountering mathematical expressions. These structured interaction points can improve performance on specific tasks by providing clear pathways for certain operations.',
    category: 'ai',
    relatedTerms: ['Function Calling', 'Tool Use', 'Structured Prompting', 'Task-specific Operations']
  }
];
