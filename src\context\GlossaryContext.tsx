'use client';

import React, { createContext, useContext, ReactNode, useState, useEffect } from 'react';

// Define the structure for glossary terms
export interface GlossaryTerm {
  term: string;
  acronym?: string;
  definition: string;
  category: 'ai' | 'ml' | 'audio' | 'general';
  relatedTerms?: string[];
}

// Define the context structure
interface GlossaryContextType {
  terms: GlossaryTerm[];
  isLoading: boolean;
  getDefinition: (term: string) => GlossaryTerm | undefined;
}

// Create the context with default values
const GlossaryContext = createContext<GlossaryContextType>({
  terms: [],
  isLoading: true,
  getDefinition: () => undefined,
});

// Hook to use the glossary context
export const useGlossary = () => useContext(GlossaryContext);

// Provider component
export const GlossaryProvider = ({ children }: { children: ReactNode }) => {
  const [terms, setTerms] = useState<GlossaryTerm[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load glossary terms
  useEffect(() => {
    const fetchGlossaryTerms = async () => {
      try {
        // In a real implementation, you might fetch this from an API
        // For now, we'll fetch it from a static JSON file
        const response = await fetch('/api/glossary');
        if (!response.ok) {
          throw new Error('Failed to fetch glossary terms');
        }
        const data = await response.json();
        setTerms(data);
      } catch (error) {
        console.error('Error loading glossary terms:', error);
        // Set some default terms in case of error
        setTerms([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchGlossaryTerms();
  }, []);

  // Function to get a term's definition
  const getDefinition = (searchTerm: string): GlossaryTerm | undefined => {
    // Case insensitive search
    const lowerSearchTerm = searchTerm.toLowerCase();
    
    // First try exact match on term
    const exactMatch = terms.find(
      term => term.term.toLowerCase() === lowerSearchTerm
    );
    if (exactMatch) return exactMatch;
    
    // Then try match on acronym
    const acronymMatch = terms.find(
      term => term.acronym?.toLowerCase() === lowerSearchTerm
    );
    if (acronymMatch) return acronymMatch;
    
    // No match found
    return undefined;
  };

  return (
    <GlossaryContext.Provider value={{ terms, isLoading, getDefinition }}>
      {children}
    </GlossaryContext.Provider>
  );
};
