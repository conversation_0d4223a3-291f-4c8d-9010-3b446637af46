import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import { NextRequest } from 'next/server';

// Define available models
const AVAILABLE_MODELS = ['o3', 'Gemini', 'Sonnet'];
const DEFAULT_MODEL = 'o3';

export async function GET(request: NextRequest) {
  try {
    // Get the model from query parameters or use default
    const searchParams = request.nextUrl.searchParams;
    const requestedModel = searchParams.get('model') || DEFAULT_MODEL;
    
    // Validate the model parameter
    const model = AVAILABLE_MODELS.includes(requestedModel) ? requestedModel : DEFAULT_MODEL;
    
    // Path to the evaluations directory
    const evaluationsDir = path.join(process.cwd(), 'public', 'papers', 'evaluations');
    
    // Check if we're using the new directory structure
    const dirContents = fs.readdirSync(evaluationsDir);
    const hasSubdirectories = dirContents.some(item => {
      const itemPath = path.join(evaluationsDir, item);
      return fs.existsSync(itemPath) && fs.statSync(itemPath).isDirectory();
    });
    
    let evaluationFiles: string[] = [];
    
    if (hasSubdirectories) {
      // New structure: Paper folders with model-specific files
      for (const folder of dirContents) {
        const folderPath = path.join(evaluationsDir, folder);
        
        if (fs.existsSync(folderPath) && fs.statSync(folderPath).isDirectory()) {
          try {
            const folderFiles = fs.readdirSync(folderPath);
            // Look for files matching the requested model
            const modelFile = folderFiles.find(file => 
              file.endsWith(`_${model}.json`) && file.startsWith('Evaluation_')
            );
            
            if (modelFile) {
              evaluationFiles.push(`${folder}/${modelFile}`);
            }
          } catch (err) {
            console.warn(`Could not read folder ${folder}:`, err);
          }
        }
      }
    } else {
      // Legacy structure: Direct JSON files
      evaluationFiles = dirContents.filter(file => 
        file.startsWith('Evaluation_') && file.endsWith('.json')
      );
    }
    
    // Process each evaluation file to extract metadata
    const papers = await Promise.all(
      evaluationFiles.map(async (filePath) => {
        try {
          // Determine the full path to the file
          const fullFilePath = path.join(evaluationsDir, filePath);
          const fileContent = fs.readFileSync(fullFilePath, 'utf8');
          
          // Check if file is empty or whitespace only
          if (!fileContent.trim()) {
            console.warn(`File ${filePath} is empty or contains only whitespace. Skipping.`);
            return null;
          }
          
          // Try to parse the JSON
          let jsonData;
          try {
            jsonData = JSON.parse(fileContent);
          } catch (parseError) {
            console.error(`Error parsing JSON in file ${filePath}:`, parseError);
            return null;
          }
          
          // Validate that the JSON has the required structure
          if (!jsonData.metadata || !jsonData.metadata.title) {
            console.warn(`File ${filePath} is missing required metadata. Skipping.`);
            return null;
          }
          
          // Extract the filename from the path
          const filename = filePath.includes('/') ? filePath.split('/')[1] : filePath;
          
          // Extract the paper ID from the filename
          // Example: "Evaluation_Adaptive_Temperature_Sampling_o3.json" -> "adaptive_temperature_sampling"
          const id = filename
            .replace('Evaluation_', '')
            // Remove any model suffix (o3, Gemini, Sonnet) from the end
            .replace(/_o3\.json$/, '')
            .replace(/_Gemini\.json$/, '')
            .replace(/_Sonnet\.json$/, '')
            .replace(/\.json$/, '')
            .toLowerCase()
            .replace(/\s+/g, '_');
          
          // Extract the total weighted score if available
          let totalScore = 0;
          
          // First check if there's a total_weighted_score at the top level of scores
          if (jsonData.scores && jsonData.scores.total_weighted_score !== undefined) {
            totalScore = jsonData.scores.total_weighted_score;
          }
          // If not found, try to calculate from individual scores
          else if (jsonData.scores) {
            const scores = jsonData.scores;
            
            // Check if we have category totals we can average
            const categoryTotals = [];
            
            // Look for total scores in each category
            for (const category in scores) {
              if (typeof scores[category] === 'object' && scores[category].total !== undefined) {
                categoryTotals.push(scores[category].total);
              }
            }
            
            // Calculate average if we found any category totals
            if (categoryTotals.length > 0) {
              totalScore = Math.round(categoryTotals.reduce((sum, score) => sum + score, 0) / categoryTotals.length);
            }
          }
          
          // Create a paper object
          return {
            id,
            title: jsonData.metadata.title,
            authors: jsonData.metadata.authors,
            year: jsonData.metadata.year.toString(),
            summary: jsonData.paper_summary || '',
            // Extract categories from the paper if available, or use default
            categories: ['AI', 'Research'], // Default categories
            score: totalScore
          };
        } catch (error) {
          console.error(`Error processing file ${filePath}:`, error);
          return null;
        }
      })
    );
    
    // Filter out any null entries (failed processing)
    const validPapers = papers.filter(paper => paper !== null);
    
    return NextResponse.json(validPapers);
  } catch (error) {
    console.error('Error in API route:', error);
    return NextResponse.json(
      { error: 'Failed to load papers' },
      { status: 500 }
    );
  }
}
