'use client';

import Link from 'next/link';
import { useEffect, useState } from 'react';
import { useModelContext } from '@/context/ModelContext';
import AverageScoreDisplay from '@/components/AverageScoreDisplay';
import Tooltip from '@/components/Tooltip';
import { SparklesIcon } from '@heroicons/react/20/solid';

interface Paper {
  id: string;
  title: string;
  authors: string;
  year: string;
  summary: string;
  categories: string[];
  score: number;
}

type SortType = 'alpha_asc' | 'alpha_desc' | 'score_asc' | 'score_desc' | 'none';

export default function SourceMaterial() {
  const [papers, setPapers] = useState<Paper[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sortType, setSortType] = useState<SortType>('alpha_asc'); // Default to alphabetical A-Z
  const [sortedPapers, setSortedPapers] = useState<Paper[]>([]);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const { selectedModel } = useModelContext();
  
  // Function to truncate author names
  const truncateAuthors = (authors: string, maxLength: number = 60) => {
    if (authors.length <= maxLength) return authors;
    return authors.substring(0, maxLength) + '...';
  };

  // Effect to load papers from API
  useEffect(() => {
    const loadPapers = async () => {
      try {
        setLoading(true);
        // Use our API endpoint to get all papers with the selected model
        const response = await fetch(`/api/papers?model=${selectedModel}`);
        if (!response.ok) {
          throw new Error('Failed to load paper data');
        }
        
        const papers = await response.json();
        console.log(`Loaded papers for model ${selectedModel}:`, papers);
        
        // Set the papers in state
        setPapers(papers);
        setSortedPapers(papers); // Initialize sorted papers with all papers
        setLoading(false);
      } catch (error) {
        console.error(`Error loading papers for model ${selectedModel}:`, error);
        setError(`Failed to load papers for model ${selectedModel}`);
        setLoading(false);
      }
    };
    
    loadPapers();
  }, [selectedModel]); // Re-fetch when selected model changes
  
  // Effect to handle sorting when sort type changes
  useEffect(() => {
    if (papers.length === 0) return;
    
    const sorted = [...papers];
    
    switch (sortType) {
      case 'alpha_asc':
        sorted.sort((a, b) => a.title.localeCompare(b.title));
        break;
      case 'alpha_desc':
        sorted.sort((a, b) => b.title.localeCompare(a.title));
        break;
      case 'score_asc':
        sorted.sort((a, b) => a.score - b.score);
        break;
      case 'score_desc':
        sorted.sort((a, b) => b.score - a.score);
        break;
      default:
        // No sorting, use original order
        break;
    }
    
    setSortedPapers(sorted);
  }, [papers, sortType]);
  
  // Function to set sort type
  const setSortingType = (type: SortType) => {
    setSortType(type);
    setDropdownOpen(false); // Close dropdown after selection
  };
  
  // Function to toggle dropdown
  const toggleDropdown = () => {
    setDropdownOpen(!dropdownOpen);
  };
  
  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (!target.closest('.sort-dropdown')) {
        setDropdownOpen(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-zinc-900 font-roboto">
      {/* Banner Section - Matching Introduction Page Exactly */}
      <section className="relative isolate text-white pt-12 md:pt-16 pb-16 md:pb-20 bg-[url('/images/banner-bg.jpg')] bg-cover bg-center">
        {/* Gradient Overlay */}
        <div className="absolute inset-0 bg-gradient-to-b from-sky-700/50 via-indigo-800/70 to-purple-900/90 dark:from-sky-700/60 dark:via-indigo-800/80 dark:to-purple-900/95 backdrop-blur-sm"></div>
        
        {/* Content Container - Adjusted for full-width banner structure */}
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="max-w-4xl mx-auto">
            {/* Updated h1 to match intro */}
            <h1 className="text-4xl font-bold text-black dark:text-white mb-4">
              Source Material
            </h1>
            {/* Updated p to match intro */}
            <p className="text-xl text-black dark:text-white max-w-3xl mx-auto">
              A curated collection of research papers and articles that form the foundation of this thesis.
            </p>
          </div>
        </div>
        
        {/* Updated Bottom Gradient Bar to match intro */}
        <div className="absolute bottom-0 left-0 right-0 h-1 w-full bg-gradient-to-l from-blue-400 to-purple-400"></div>
      </section>

      {/* Main Content Wrapper - Constrained Width */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Combined Controls Row: Back Link, Paper Scores, Sort Dropdown */}
        <div className="flex items-center justify-between mb-8">
          {/* Back Link */}
          <div>
            <Link 
              href="/critical-review" 
              className="text-sm font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 flex items-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="w-5 h-5 mr-2">
                <path fillRule="evenodd" d="M17 10a.75.75 0 01-.75.75H5.56l2.72 2.72a.75.75 0 11-1.06 1.06l-4-4a.75.75 0 010-1.06l4-4a.75.75 0 011.06 1.06L5.56 9.25H16.25A.75.75 0 0117 10z" clipRule="evenodd" />
              </svg>
              Back to Critical Review Overview
            </Link>
          </div>

          {/* Average Score Display */}
          <div className="flex-grow mx-4"> 
            <AverageScoreDisplay />
          </div>

          {/* Sort Dropdown */}
          <div className="relative sort-dropdown">
            <button
              onClick={toggleDropdown}
              className="inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-zinc-800 hover:bg-gray-50 dark:hover:bg-zinc-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Sort Papers
              <svg className={`ml-2 -mr-1 h-5 w-5 transform transition-transform ${dropdownOpen ? 'rotate-180' : ''}`} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fillRule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 10.94l3.71-3.71a.75.75 0 111.06 1.06l-4.25 4.25a.75.75 0 01-1.06 0L5.23 8.29a.75.75 0 01.02-1.06z" clipRule="evenodd" />
              </svg>
            </button>
            {dropdownOpen && (
              <div className="origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white dark:bg-zinc-800 ring-1 ring-black ring-opacity-5 focus:outline-none z-10">
                <div className="py-1" role="menu" aria-orientation="vertical" aria-labelledby="options-menu">
                  <button onClick={() => setSortingType('alpha_asc')} className="block w-full px-4 py-2 text-left text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-zinc-700" role="menuitem">Alphabetical (A-Z)</button>
                  <button onClick={() => setSortingType('alpha_desc')} className="block w-full px-4 py-2 text-left text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-zinc-700" role="menuitem">Alphabetical (Z-A)</button>
                  <button onClick={() => setSortingType('score_desc')} className="block w-full px-4 py-2 text-left text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-zinc-700" role="menuitem">Score (High to Low)</button>
                  <button onClick={() => setSortingType('score_asc')} className="block w-full px-4 py-2 text-left text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-zinc-700" role="menuitem">Score (Low to High)</button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Loading and Error States */}
        {loading && (
          <div className="col-span-1 md:col-span-2 lg:col-span-3 text-center py-12">
            <div className="animate-pulse text-xl font-semibold">Loading papers from {selectedModel} model...</div>
          </div>
        )}
        
        {error && (
          <div className="col-span-1 md:col-span-2 lg:col-span-3 text-center py-12">
            <div className="text-red-500 dark:text-red-400 text-xl font-semibold mb-4">Error</div>
            <p className="text-gray-600 dark:text-gray-400">{error}</p>
          </div>
        )}

        {/* Papers Grid */}
        {!loading && !error && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {sortedPapers.length > 0 ? sortedPapers.map((paper) => {
              let scoreBorderColor = 'border-t-red-500 dark:border-t-red-400'; // Default for score < 60
              if (paper.score >= 80) {
                scoreBorderColor = 'border-t-green-500 dark:border-t-green-400';
              } else if (paper.score >= 60) {
                scoreBorderColor = 'border-t-yellow-500 dark:border-t-yellow-400';
              }
              return (
                <div key={paper.id} className={`bg-white dark:bg-zinc-900 rounded-lg shadow-sm dark:shadow-zinc-800/30 overflow-hidden flex flex-col h-full border border-gray-200 dark:border-gray-700 border-t-4 ${scoreBorderColor}`}>
                  {/* Paper Preview */}
                  <div className="p-8 flex-grow">
                    <div className="flex items-start justify-between">
                      <div>
                        <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
                          {paper.title}
                        </h2>
                        <div className="text-sm text-gray-500 dark:text-gray-400 mb-2">
                          <Tooltip text={paper.authors}>
                            <span>{truncateAuthors(paper.authors)} • {paper.year}</span>
                          </Tooltip>
                        </div>
                      </div>
                      {/* Score Badge */}
                      <div className="ml-2 flex-shrink-0">
                        <div className={`px-3 py-1 rounded-full text-sm font-medium ${paper.score >= 80 ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300 border border-green-200 dark:border-green-800' : paper.score >= 60 ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300 border border-yellow-200 dark:border-yellow-800' : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300 border border-red-200 dark:border-red-800'}`}>
                          {paper.score}/100
                        </div>
                      </div>
                    </div>
                    <div className="mb-4">
                      <div className="flex flex-wrap gap-2">
                        {paper.categories.map((category) => (
                          <span
                            key={category}
                            className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 border border-blue-200 dark:border-blue-800"
                          >
                            {category}
                          </span>
                        ))}
                      </div>
                    </div>
                    <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
                      {paper.summary.split('.')[0].trim() + '.'}
                    </p>
                  </div>

                  {/* Action Links */}
                  <div className="border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-zinc-800 px-6 py-4 mt-auto">
                    <div className="flex justify-between items-center">
                      <Link
                        href={`/critical-review/source-material/${paper.id}`}
                        className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium group transition-colors flex items-center"
                      >
                        View Full Paper <span className="transform group-hover:translate-x-1 transition-transform ml-1">→</span>
                      </Link>
                      <Link
                        href={`/critical-review/evaluation/${paper.id}`}
                        className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium group transition-colors flex items-center"
                      >
                        View Evaluation 
                        <SparklesIcon className="h-4 w-4 text-yellow-400 dark:text-yellow-300 ml-1" />
                        <span className="transform group-hover:translate-x-1 transition-transform ml-1">→</span>
                      </Link>
                    </div>
                  </div>
                </div>
              )}) : (
                <div className="col-span-1 md:col-span-2 lg:col-span-3 text-center py-12">
                  <p className="text-gray-600 dark:text-gray-400">No papers found for the {selectedModel} model. Ensure evaluation JSON files exist in <code>public/papers/evaluations/</code> with the appropriate model suffix.</p>
                </div>
              )}
          </div>
        )}
      </div>
    </div>
  );
}
