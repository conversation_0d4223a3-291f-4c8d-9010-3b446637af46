import React, { useState, useEffect } from 'react';
import { useModelContext } from '@/context/ModelContext';
import ScoreToggle from './ScoreToggle';
import { ChevronDownIcon, ChevronUpIcon } from '@heroicons/react/24/outline';

interface SubScore {
  [key: string]: number;
}

interface CategoryScore {
  total: number;
  subscores: SubScore;
}

interface ModelCategoryScores {
  [category: string]: CategoryScore;
}

interface DetailedModelScore {
  total: number;
  categories: ModelCategoryScores;
}

interface ModelScore {
  [key: string]: number;
}

interface PaperScore {
  id: string;
  title: string;
  modelScores: ModelScore;
  detailedModelScores: {
    [key: string]: DetailedModelScore;
  };
  averageScore: number;
  averageDetailedScores: {
    categories: {
      [category: string]: {
        total: number;
        subscores: SubScore;
      };
    };
  };
}

// Define sort options
type SortOption = 'default' | 'average-high' | 'average-low' | 'model-high' | 'model-low' | 'alpha-az' | 'alpha-za';

const SORT_OPTIONS: { value: SortOption; label: string }[] = [
  { value: 'default', label: 'Default Order' },
  { value: 'average-high', label: 'Average: High to Low' },
  { value: 'average-low', label: 'Average: Low to High' },
  { value: 'model-high', label: 'Model Score: High to Low' },
  { value: 'model-low', label: 'Model Score: Low to High' },
  { value: 'alpha-az', label: 'Title: A to Z' },
  { value: 'alpha-za', label: 'Title: Z to A' },
];

// Define score categories and their labels
const SCORE_CATEGORIES = [
  {
    id: 'ai_techniques',
    label: 'AI Techniques',
    subscores: [
      { id: 'methods', label: 'Methods' },
      { id: 'ai_integration', label: 'AI Integration' },
      { id: 'collaboration', label: 'Collaboration' }
    ]
  },
  {
    id: 'process_enhancement',
    label: 'Process Enhancement',
    subscores: [
      { id: 'task_improvements', label: 'Task Improvements' },
      { id: 'lifecycle_integration', label: 'Lifecycle Integration' },
      { id: 'challenge_solutions', label: 'Challenge Solutions' }
    ]
  },
  {
    id: 'implementation',
    label: 'Implementation',
    subscores: [
      { id: 'steps', label: 'Steps' },
      { id: 'examples', label: 'Examples' },
      { id: 'requirements', label: 'Requirements' }
    ]
  },
  {
    id: 'measurable_impact',
    label: 'Measurable Impact',
    subscores: [
      { id: 'efficiency', label: 'Efficiency' },
      { id: 'metrics', label: 'Metrics' },
      { id: 'benefits', label: 'Benefits' }
    ]
  },
  {
    id: 'developer_experience',
    label: 'Developer Experience',
    subscores: [
      { id: 'adoption', label: 'Adoption' },
      { id: 'workflow', label: 'Workflow' },
      { id: 'practicality', label: 'Practicality' }
    ]
  }
];

const AverageScoreDisplay: React.FC = () => {
  const [paperScores, setPaperScores] = useState<PaperScore[]>([]);
  const [sortedPapers, setSortedPapers] = useState<PaperScore[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showAverageScores, setShowAverageScores] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [expandedPapers, setExpandedPapers] = useState<Record<string, boolean>>({});
  const [expandedCategories, setExpandedCategories] = useState<Record<string, Record<string, boolean>>>({});
  const [sortOption, setSortOption] = useState<SortOption>('default');
  const [sortDropdownOpen, setSortDropdownOpen] = useState(false);
  const sortDropdownRef = React.useRef<HTMLDivElement>(null);
  const { selectedModel } = useModelContext();

  // Add click outside handler for sorting dropdown
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (sortDropdownRef.current && !sortDropdownRef.current.contains(event.target as Node)) {
        setSortDropdownOpen(false);
      }
    }
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Handle sorting logic
  useEffect(() => {
    if (!paperScores.length) return;
    
    const papers = [...paperScores];
    
    // Apply sorting based on selected option
    switch (sortOption) {
      case 'average-high':
        papers.sort((a, b) => b.averageScore - a.averageScore);
        break;
      case 'average-low':
        papers.sort((a, b) => a.averageScore - b.averageScore);
        break;
      case 'model-high':
        papers.sort((a, b) => (b.modelScores[selectedModel] || 0) - (a.modelScores[selectedModel] || 0));
        break;
      case 'model-low':
        papers.sort((a, b) => (a.modelScores[selectedModel] || 0) - (b.modelScores[selectedModel] || 0));
        break;
      case 'alpha-az':
        papers.sort((a, b) => a.title.localeCompare(b.title));
        break;
      case 'alpha-za':
        papers.sort((a, b) => b.title.localeCompare(a.title));
        break;
      case 'default':
      default:
        // Leave in original order (which is the order from the API)
        break;
    }
    
    setSortedPapers(papers);
  }, [paperScores, sortOption, selectedModel]);

  useEffect(() => {
    async function fetchPaperScores() {
      try {
        setLoading(true);
        const response = await fetch('/api/paper-scores');
        if (!response.ok) {
          throw new Error('Failed to fetch paper scores');
        }
        const data = await response.json();
        setPaperScores(data);
        setSortedPapers(data); // Initialize sorted papers with the original data
        
        // Initialize expanded states
        const paperStates: Record<string, boolean> = {};
        const categoryStates: Record<string, Record<string, boolean>> = {};
        
        data.forEach((paper: PaperScore) => {
          paperStates[paper.id] = false;
          categoryStates[paper.id] = {};
          SCORE_CATEGORIES.forEach(category => {
            categoryStates[paper.id][category.id] = false;
          });
        });
        
        setExpandedPapers(paperStates);
        setExpandedCategories(categoryStates);
      } catch (error) {
        console.error('Error fetching paper scores:', error);
        setError('Failed to load paper scores');
      } finally {
        setLoading(false);
      }
    }

    fetchPaperScores();
  }, []);

  // Toggle expanded state for a paper
  const togglePaperExpanded = (paperId: string) => {
    setExpandedPapers(prev => ({
      ...prev,
      [paperId]: !prev[paperId]
    }));
  };

  // Toggle expanded state for a category within a paper
  const toggleCategoryExpanded = (paperId: string, categoryId: string) => {
    setExpandedCategories(prev => ({
      ...prev,
      [paperId]: {
        ...prev[paperId],
        [categoryId]: !prev[paperId][categoryId]
      }
    }));
  };

  // Get score color based on value
  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600 dark:text-green-400';
    if (score >= 60) return 'text-blue-600 dark:text-blue-400';
    if (score >= 40) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  };
  
  // Helper to get current sort option label
  const getCurrentSortLabel = () => {
    return SORT_OPTIONS.find(option => option.value === sortOption)?.label || 'Default Order';
  };

  return (
    <div className="relative w-full">
      {/* Collapsible Header - Now gets the primary styling for collapsed state */}
      <button 
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full flex justify-between items-center focus:outline-none group py-2 px-4 bg-white dark:bg-zinc-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 transition-colors duration-150"
      >
        <h2 className="text-md font-semibold text-gray-800 dark:text-gray-100 flex items-center"> {/* Adjusted font size for consistency */}
          <span className="mr-2">Scoreboard</span>
          <span className="text-xs font-normal text-gray-500 dark:text-gray-400 ml-1">
            {isExpanded ? '(collapse)' : '(expand)'}
          </span>
        </h2>
        {isExpanded ? (
          <ChevronUpIcon className="h-5 w-5 text-gray-500 dark:text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300 transition-colors" />
        ) : (
          <ChevronDownIcon className="h-5 w-5 text-gray-500 dark:text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300 transition-colors" />
        )}
      </button>

      {/* Collapsible Content - Now an absolutely positioned overlay */}
      {isExpanded && (
        <div className="absolute top-full left-0 right-0 mt-1 z-50 bg-white dark:bg-zinc-900 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 p-4 max-h-[75vh] overflow-y-auto">
          {loading ? (
            <div className="animate-pulse space-y-2">
              <div className="h-5 bg-gray-200 dark:bg-gray-700 rounded w-2/3"></div>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                {[...Array(6)].map((_, index) => (
                  <div key={index} className="h-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
                ))}
              </div>
            </div>
          ) : error ? (
            <div className="text-red-500 dark:text-red-400 text-sm">
              {error}
            </div>
          ) : paperScores.length === 0 ? (
            <div className="text-gray-600 dark:text-gray-400 text-sm">
              No paper scores available.
            </div>
          ) : (
            <>
              <div className="flex justify-between items-center mb-3">
                {/* Sorting Dropdown */}
                <div className="relative" ref={sortDropdownRef}>
                  <button 
                    onClick={() => setSortDropdownOpen(!sortDropdownOpen)}
                    className="flex items-center text-xs bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded px-2 py-1 hover:bg-gray-50 dark:hover:bg-gray-700"
                  >
                    <span className="mr-1">Sort:</span>
                    <span className="font-medium">{getCurrentSortLabel()}</span>
                    <ChevronDownIcon className="h-3 w-3 ml-1" />
                  </button>
                  
                  {sortDropdownOpen && (
                    <div className="absolute left-0 top-full mt-1 z-10 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded shadow-lg py-1 w-60">
                      <div className="px-3 py-1 text-[10px] uppercase font-semibold text-gray-500 dark:text-gray-400 border-b border-gray-100 dark:border-gray-700">Sort Options</div>
                      {SORT_OPTIONS.map(option => {
                        // Get sort direction indicator
                        let directionIcon = null;
                        if (option.value.includes('-high')) {
                          directionIcon = <span className="text-gray-400">↓</span>;
                        } else if (option.value.includes('-low')) {
                          directionIcon = <span className="text-gray-400">↑</span>;
                        } else if (option.value === 'alpha-az') {
                          directionIcon = <span className="text-gray-400">A→Z</span>;
                        } else if (option.value === 'alpha-za') {
                          directionIcon = <span className="text-gray-400">Z→A</span>;
                        }
                        
                        return (
                          <button
                            key={option.value}
                            className={`w-full text-left px-3 py-1.5 text-xs hover:bg-gray-100 dark:hover:bg-gray-700 flex justify-between items-center ${sortOption === option.value ? 'bg-blue-50 dark:bg-blue-900/30 font-medium' : ''}`}
                            onClick={() => {
                              setSortOption(option.value);
                              setSortDropdownOpen(false);
                            }}
                          >
                            <span>{option.label}</span>
                            {directionIcon && <span className="ml-2">{directionIcon}</span>}
                          </button>
                        );
                      })}
                    </div>
                  )}
                </div>
                
                {/* Score Toggle */}
                <ScoreToggle 
                  showAverageScores={showAverageScores} 
                  setShowAverageScores={setShowAverageScores} 
                />
              </div>

              {/* Compact Paper Cards Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-3">
                {sortedPapers.map((paper) => (
                  <div 
                    key={paper.id} 
                    className="border border-gray-200 dark:border-gray-700 rounded-lg transition-colors text-sm"
                  >
                    {/* Paper Header with Score Badge */}
                    <div className="p-2 bg-gray-50 dark:bg-gray-800 rounded-t-lg flex justify-between items-start border-b border-gray-200 dark:border-gray-700">
                      <h3 className="font-medium text-gray-900 dark:text-gray-100 line-clamp-1 flex-1 pr-2">
                        {paper.title}
                      </h3>
                      
                      {/* Score Badge */}
                      <div className="flex-shrink-0">
                        <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getScoreColor(showAverageScores ? paper.averageScore : (paper.modelScores[selectedModel] || 0))} bg-opacity-10 dark:bg-opacity-20`}>
                          {showAverageScores ? paper.averageScore : (paper.modelScores[selectedModel] || 'N/A')}
                        </div>
                      </div>
                    </div>
                    
                    {/* Paper Body */}
                    <div className="p-2">
                      {/* Toggle Details + Quick Model Scores */}
                      <div className="flex flex-wrap items-center gap-1 mb-1">
                        <button 
                          onClick={() => togglePaperExpanded(paper.id)}
                          className="text-xs text-blue-600 dark:text-blue-400 hover:underline inline-flex items-center mr-2"
                        >
                          {expandedPapers[paper.id] ? (
                            <>
                              <ChevronUpIcon className="h-3 w-3 mr-0.5" />
                              Hide
                            </>
                          ) : (
                            <>
                              <ChevronDownIcon className="h-3 w-3 mr-0.5" />
                              Details
                            </>
                          )}
                        </button>
                        
                        {/* Quick Model Score Pills */}
                        {!expandedPapers[paper.id] && Object.entries(paper.modelScores).map(([model, score]) => (
                          <div key={model} className="inline-flex items-center px-1.5 py-0.5 bg-gray-100 dark:bg-gray-700 rounded text-xs">
                            <span className="text-gray-600 dark:text-gray-400 mr-1">{model}</span>
                            <span className={getScoreColor(score)}>{score}</span>
                          </div>
                        ))}
                      </div>
                      
                      {/* Expanded Details */}
                      {expandedPapers[paper.id] && (
                        <div className="space-y-3 mt-2">
                          {/* Category Score Pills */}
                          <div className="space-y-2">
                            {SCORE_CATEGORIES.map(category => {
                              const categoryScores = showAverageScores 
                                ? paper.averageDetailedScores.categories[category.id]
                                : paper.detailedModelScores[selectedModel]?.categories[category.id];
                              
                              if (!categoryScores) return null;
                              
                              return (
                                <div key={category.id} className="bg-gray-50 dark:bg-gray-800 rounded px-2 py-1.5">
                                  {/* Category Header */}
                                  <div className="flex justify-between items-center">
                                    <div className="flex items-center">
                                      <h5 className="text-xs font-medium text-gray-700 dark:text-gray-300">{category.label}</h5>
                                      <div className={`ml-1.5 text-xs font-bold ${getScoreColor(categoryScores.total)}`}>
                                        {categoryScores.total}
                                      </div>
                                    </div>
                                    
                                    {/* Toggle Button */}
                                    <button 
                                      onClick={() => toggleCategoryExpanded(paper.id, category.id)}
                                      className="p-0.5 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 focus:outline-none"
                                    >
                                      {expandedCategories[paper.id][category.id] ? (
                                        <ChevronUpIcon className="h-3 w-3" />
                                      ) : (
                                        <ChevronDownIcon className="h-3 w-3" />
                                      )}
                                    </button>
                                  </div>
                                  
                                  {/* Subscores */}
                                  {expandedCategories[paper.id][category.id] && (
                                    <div className="mt-1.5 grid grid-cols-3 gap-1.5">
                                      {category.subscores.map(subscore => {
                                        const subscoreValue = categoryScores.subscores[subscore.id];
                                        return subscoreValue !== undefined ? (
                                          <div key={subscore.id} className="text-xs py-1 px-1.5 bg-white dark:bg-gray-700 rounded flex justify-between">
                                            <span className="text-gray-600 dark:text-gray-400">{subscore.label}:</span>
                                            <span className={getScoreColor(subscoreValue)}>{subscoreValue}</span>
                                          </div>
                                        ) : null;
                                      })}
                                    </div>
                                  )}
                                </div>
                              );
                            })}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </>
          )}
        </div>
      )}
    </div>
  );
};

export default AverageScoreDisplay;
