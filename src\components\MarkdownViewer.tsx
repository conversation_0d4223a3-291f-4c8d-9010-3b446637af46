import { marked } from 'marked';

interface MarkdownViewerProps {
  content: string;
}

export default function MarkdownViewer({ content }: MarkdownViewerProps) {
  const htmlContent = marked(content);

  return (
    <div 
      className="prose max-w-none
        [&>*]:text-gray-900 dark:[&>*]:text-gray-100
        [&_p]:text-gray-900 dark:[&_p]:text-gray-100
        [&_li]:text-gray-900 dark:[&_li]:text-gray-100
        [&_blockquote]:text-gray-900 dark:[&_blockquote]:text-gray-100
        prose-headings:text-gray-900 dark:prose-headings:text-gray-100
        prose-strong:text-gray-900 dark:prose-strong:text-gray-100"
      dangerouslySetInnerHTML={{ __html: htmlContent }}
    />
  );
}
