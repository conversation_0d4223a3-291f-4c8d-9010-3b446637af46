@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: Arial, Helvetica, sans-serif;
}

/* Custom markdown styling */
.custom-markdown strong {
  color: white;
  font-weight: 700;
}

.custom-markdown p {
  margin-bottom: 1rem;
}

.dark .custom-markdown strong {
  color: white;
}

.custom-markdown ul li {
  margin-bottom: 0.75rem;
}

/* Glossary tooltip styles */
.glossary-term {
  border-bottom: 1px dotted #9ca3af;
  cursor: help;
  position: relative;
  display: inline;
}

/* Ensure tooltips are visible above other content */
#glossary-tooltip {
  z-index: 9999;
  pointer-events: none;
}

/* Exclude certain elements from glossary processing */
.no-glossary * {
  border-bottom: none !important;
  cursor: inherit !important;
}

/* Dark mode support for tooltips */
@media (prefers-color-scheme: dark) {
  #glossary-tooltip {
    background-color: #18181b !important;
    color: white !important;
  }
  
  #glossary-tooltip > div {
    background-color: #18181b !important;
  }
  
  .glossary-term {
    border-bottom-color: #6b7280;
  }
}

.dark #glossary-tooltip {
  background-color: #18181b !important;
  color: white !important;
}

.dark #glossary-tooltip > div {
  background-color: #18181b !important;
}

.dark .glossary-term {
  border-bottom-color: #6b7280;
}

/* Custom Scrollbar Styles */
.custom-styled-scrollbar::-webkit-scrollbar {
  width: 8px; /* Width of the scrollbar */
}

.custom-styled-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1; /* Light mode track color */
  border-radius: 10px;
}

.custom-styled-scrollbar::-webkit-scrollbar-thumb {
  background: #c1c1c1; /* Light mode thumb color */
  border-radius: 10px;
}

.custom-styled-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8; /* Light mode thumb hover color */
}

/* Dark mode scrollbar styles */
.dark .custom-styled-scrollbar::-webkit-scrollbar-track {
  background: #2d2d2d; /* Dark mode track color */
}

.dark .custom-styled-scrollbar::-webkit-scrollbar-thumb {
  background: #555; /* Dark mode thumb color */
}

.dark .custom-styled-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #777; /* Dark mode thumb hover color */
}

/* Firefox specific scrollbar styling */
.custom-styled-scrollbar {
  scrollbar-width: thin; /* "auto" or "thin" */
  scrollbar-color: #c1c1c1 #f1f1f1; /* thumb and track color for light mode */
}

.dark .custom-styled-scrollbar {
  scrollbar-color: #555 #2d2d2d; /* thumb and track color for dark mode */
}
