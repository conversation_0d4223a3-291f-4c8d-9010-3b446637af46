'use client';

import React, { useEffect, useRef } from 'react';
import { useGlossary } from '@/context/GlossaryContext';
import { usePathname } from 'next/navigation';

interface GlossaryEnhancerProps {
  children: React.ReactNode;
}

export default function GlossaryEnhancer({ children }: GlossaryEnhancerProps) {
  const { terms, isLoading } = useGlossary();
  const contentRef = useRef<HTMLDivElement>(null);
  const pathname = usePathname();
  
  // Skip processing on the glossary page
  const shouldSkip = pathname === '/glossary';
  
  useEffect(() => {
    // Skip if no terms, still loading, or on glossary page
    if (isLoading || !terms || terms.length === 0 || shouldSkip || !contentRef.current) {
      return;
    }
    
    // Create a map of terms for faster lookup
    const termMap = new Map();
    terms.forEach(term => {
      // Skip very short terms
      if (term.term.length < 3) return;
      
      termMap.set(term.term.toLowerCase(), {
        term: term.term,
        definition: `${term.term}${term.acronym ? ` (${term.acronym})` : ''}: ${term.definition}`
      });
      
      if (term.acronym && term.acronym.length >= 2) {
        termMap.set(term.acronym.toLowerCase(), {
          term: term.acronym,
          definition: `${term.term}${term.acronym ? ` (${term.acronym})` : ''}: ${term.definition}`
        });
      }
    });
    
    // Elements to exclude from processing
    const excludeSelectors = [
      'script', 'style', 'pre', 'code', 'textarea', 'input',
      'a', 'button', 'select', 'option',
      '[data-no-glossary]', '.no-glossary'
    ].join(',');
    
    // Function to process the DOM
    const processDOM = () => {
      // Get all text nodes that are not inside excluded elements
      const walker = document.createTreeWalker(
        contentRef.current!,
        NodeFilter.SHOW_TEXT,
        {
          acceptNode: (node) => {
            // Skip empty text nodes
            if (!node.textContent || node.textContent.trim() === '') {
              return NodeFilter.FILTER_REJECT;
            }
            
            // Skip if parent is excluded
            const parent = node.parentElement;
            if (!parent || parent.closest(excludeSelectors) || parent.classList.contains('glossary-processed')) {
              return NodeFilter.FILTER_REJECT;
            }
            
            return NodeFilter.FILTER_ACCEPT;
          }
        }
      );
      
      // Process each text node
      let node;
      while ((node = walker.nextNode())) {
        processTextNode(node as Text);
      }
    };
    
    // Function to process a text node
    const processTextNode = (textNode: Text) => {
      const text = textNode.textContent;
      if (!text || text.trim().length < 3) return;
      
      const parent = textNode.parentElement;
      if (!parent) return;
      
      // Mark as processed to avoid re-processing
      parent.classList.add('glossary-processed');
      
      // Split text into words and check each one
      const words = text.split(/(\s+|[.,!?;:()"'\[\]{}]+)/);
      const fragment = document.createDocumentFragment();
      
      for (const word of words) {
        const cleanWord = word.toLowerCase().trim();
        
        // Skip short words and punctuation
        if (cleanWord.length < 2) {
          fragment.appendChild(document.createTextNode(word));
          continue;
        }
        
        // Check if this word is a glossary term
        const termInfo = termMap.get(cleanWord);
        
        if (termInfo) {
          // Check if we're inside a paragraph tag
          const isInsideParagraph = parent.tagName === 'P' || parent.closest('p');
          
          if (isInsideParagraph) {
            // For paragraphs, use a simpler approach with just spans to avoid nesting issues
            const tooltipSpan = document.createElement('span');
            tooltipSpan.className = 'border-b border-dotted border-gray-400 cursor-help relative';
            tooltipSpan.setAttribute('title', termInfo.definition);
            tooltipSpan.textContent = word;
            
            // Use a data attribute to store the definition for potential JS-based tooltips
            tooltipSpan.setAttribute('data-tooltip', termInfo.definition);
            fragment.appendChild(tooltipSpan);
          } else {
            // For other elements, use the full tooltip structure
            const tooltipWrapper = document.createElement('span');
            tooltipWrapper.className = 'relative group inline-block';
            
            // Create term span
            const termSpan = document.createElement('span');
            termSpan.className = 'border-b border-dotted border-gray-400 cursor-help';
            termSpan.textContent = word;
            tooltipWrapper.appendChild(termSpan);
            
            // Create tooltip content as a span instead of div
            const tooltipContent = document.createElement('span');
            tooltipContent.className = 'absolute z-10 invisible group-hover:visible opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-white dark:bg-zinc-900 text-gray-900 dark:text-gray-100 p-2 rounded-md shadow-lg dark:shadow-zinc-800/30 text-sm w-64 left-1/2 -translate-x-1/2 bottom-full mb-2';
            tooltipContent.textContent = termInfo.definition;
            
            // Create tooltip arrow as a span
            const tooltipArrow = document.createElement('span');
            tooltipArrow.className = 'absolute -bottom-2 left-1/2 -ml-2 w-4 h-4 bg-white dark:bg-zinc-900 transform rotate-45';
            tooltipContent.appendChild(tooltipArrow);
            
            tooltipWrapper.appendChild(tooltipContent);
            fragment.appendChild(tooltipWrapper);
          }
        } else {
          fragment.appendChild(document.createTextNode(word));
        }
      }
      
      // Replace the original text node with our processed fragment
      parent.replaceChild(fragment, textNode);
    };
    
    // Process the DOM with a delay to ensure it's fully rendered
    const timeoutId = setTimeout(() => {
      try {
        processDOM();
        
        // Set up a mutation observer to process new content
        const observer = new MutationObserver((mutations) => {
          mutations.forEach(mutation => {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
              // Process the DOM again when new nodes are added
              setTimeout(processDOM, 100);
            }
          });
        });
        
        observer.observe(contentRef.current!, {
          childList: true,
          subtree: true
        });
        
        // Clean up observer on unmount
        return () => observer.disconnect();
      } catch (error) {
        console.error('Error processing glossary terms:', error);
      }
    }, 500);
    
    return () => clearTimeout(timeoutId);
  }, [terms, isLoading, shouldSkip]);
  
  // Simple pass-through if no terms, still loading, or on glossary page
  if (isLoading || !terms || terms.length === 0 || shouldSkip) {
    return <>{children}</>;
  }
  
  return (
    <div ref={contentRef} className="glossary-enhanced-content">
      {children}
    </div>
  );
}
