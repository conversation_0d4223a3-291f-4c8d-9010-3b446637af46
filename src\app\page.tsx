import { Inter } from 'next/font/google'
import { BookOpenIcon, SparklesIcon, UsersIcon, PuzzlePieceIcon, ChatBubbleLeftRightIcon, LightBulbIcon, EyeIcon, MagnifyingGlassIcon, ChevronRightIcon, UserIcon } from '@heroicons/react/24/outline';

export default function Home() {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-zinc-900 font-merriweather">
      {/* Hero Section */}
      <section className="relative isolate text-white py-8 md:py-12 bg-[url('/images/banner-bg.jpg')] bg-cover bg-center">
        {/* Enhanced Overlay */}
        <div className="absolute inset-0 bg-gradient-to-b from-sky-700/50 via-indigo-800/70 to-purple-900/90 dark:from-sky-700/60 dark:via-indigo-800/80 dark:to-purple-900/95 backdrop-blur-sm"></div>

        {/* Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 md:py-12 relative z-10">
          <div className="max-w-4xl">
            <h1 className="text-3xl md:text-4xl font-bold mb-6 text-black dark:text-white">
             Supportive Narrative - Developing an AI-Powered Research Tool.
            </h1>
            <div className="w-full md:w-1/2 border-t-2 border-dotted border-purple-400 dark:border-blue-400 mb-8"></div>
            <p className="text-lg md:text-xl mb-8 text-black dark:text-white">
              This website documents the design, creation, and application of an AI-assisted system for analyzing and evaluating research papers.
            </p>
            <div className="flex items-center space-x-2 text-lg text-black dark:text-white opacity-80 dark:opacity/80">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
              <span>Nino van Orsouw</span>
            </div>
          </div>
        </div>
        {/* Added Bottom Gradient Bar */}
        <div className="absolute bottom-0 left-0 right-0 h-1 w-full bg-gradient-to-l from-blue-400 to-purple-400"></div>
      </section>

      {/* Site Guide Card Section */}
      <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 md:py-16">
        {/* Standalone Title for the Site Guide section */}
        <div className="mb-12 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-gray-100 flex items-center justify-center">
            <BookOpenIcon className="h-8 w-8 md:h-10 md:w-10 mr-3 text-sky-600 dark:text-sky-300 shrink-0" />
            Site Guide
          </h2>
        </div>

        <div className="grid grid-cols-1 gap-8">
          {/* Card 1: The Website */}
          <div className="bg-white dark:bg-zinc-900 rounded-lg shadow-lg p-6 md:p-8 border border-gray-200 dark:border-zinc-700 border-t-4 border-t-sky-500 dark:border-t-sky-400">
            <section className="text-gray-700 dark:text-gray-300 text-base leading-relaxed">
              <h3 className="text-xl md:text-2xl font-semibold text-gray-800 dark:text-gray-200 mb-3 flex items-center">
                <UsersIcon className="h-6 w-6 md:h-7 md:w-7 mr-3 text-sky-500" />
                Welcome!
              </h3>
              <p>
                Please take a moment to read the information on this page to get started with this website.<br /> <br /> 
                Creating my supportive narrative as a website comes with benefits and limitations. For example; it allows readers to navigate freely, and experience the content in a non-linear way. This in turn can be confusing if a general direction is not given. That is where this page comes in.<br /><br /> 
                Below you will find more information, please read everything carefully.
              </p>
            </section>
          </div>

          {/* Card 2: Interpreting the Content */}
          <div className="bg-white dark:bg-zinc-900 rounded-lg shadow-lg p-6 md:p-8 border border-gray-200 dark:border-zinc-700 border-t-4 border-t-purple-500 dark:border-t-purple-400">
            <section className="text-gray-700 dark:text-gray-300 text-base leading-relaxed">
              <h3 className="text-xl md:text-2xl font-semibold text-gray-800 dark:text-gray-200 mb-3 flex items-center">
                <EyeIcon className="h-6 w-6 md:h-7 md:w-7 mr-3 text-purple-500" />
                Interpreting the Content
              </h3>
              <p className="mb-3">
                To ensure clarity about the origins of content, the site uses visual cues:
              </p>
              <ul className="list-none space-y-3 pl-4 border-l-2 border-purple-500/30 dark:border-purple-400/30">
                <li>
                  <strong className="font-merriweather text-purple-700 dark:text-purple-300">Merriweather Font:</strong> This indicates the text is human written.
                </li>
                <li>
                  <UserIcon className="inline-block h-5 w-5 text-yellow-500 dark:text-yellow-400 mr-1.5 flex-shrink-0" />
                  <strong className="text-gray-700 dark:text-gray-200 mr-1">Person Icon</strong>
                  <span className="text-gray-600 dark:text-gray-200">Indicates content is human-written.</span>
                </li>
                <li>
                  <strong className="font-roboto text-teal-600 dark:text-teal-300">Roboto Font:</strong>  <span className="font-roboto"> This indicates the text is AI generated.</span>
                </li>
                <li>
                  <SparklesIcon className="inline-block h-5 w-5 text-amber-500 mx-1" />
                  <strong className="text-gray-700 dark:text-gray-200 mr-1">Sparkles Icon</strong>
                  <span className="text-gray-600 dark:text-gray-200">Indicates content is AI generated.</span>
                </li>
              </ul>
              <p className="mt-3">At the top right of the page, there is also an information icon. Hovering over this will display the above information so that it is accesible on any page of the website. <br /><br />
               Clarifying further, it means for instance that by looking at the navigation bar at the top of the page that the person icon tells us the content on this page is human-written.</p>
            </section>
          </div>

          {/* Card 3: Navigating This Site */}
          <div className="bg-white dark:bg-zinc-900 rounded-lg shadow-lg p-6 md:p-8 border border-gray-200 dark:border-zinc-700 border-t-4 border-t-yellow-500 dark:border-t-yellow-400">
            <section className="text-gray-700 dark:text-gray-300 text-base leading-relaxed">
              <h3 className="text-xl md:text-2xl font-semibold text-gray-800 dark:text-gray-200 mb-3 flex items-center">
                <MagnifyingGlassIcon className="h-6 w-6 md:h-7 md:w-7 mr-3 text-yellow-500" />
                Navigation
              </h3>
              <p>
                Use the navigation bar at the top of the page to jump between pages. On pages like <span className="font-semibold">Methods</span> and <span className="font-semibold">Results</span>, you&apos;ll find search bars and filter options to help you pinpoint specific information within the AI-generated data.
              </p>
            </section>
          </div>

          {/* Card 4: Site Contents */}
          <div className="bg-white dark:bg-zinc-900 rounded-lg shadow-lg p-6 md:p-8 border border-gray-200 dark:border-zinc-700 border-t-4 border-t-green-500 dark:border-t-green-400">
            <section className="text-gray-700 dark:text-gray-300 text-base leading-relaxed">
              <h3 className="text-xl md:text-2xl font-semibold text-gray-800 dark:text-gray-200 mb-3 flex items-center">
                <PuzzlePieceIcon className="h-6 w-6 md:h-7 md:w-7 mr-3 text-green-500" />
                Site Contents
              </h3>
              <p className="mb-3">
                An overview describing the pages of this website and their contents following the order of the navigation bar:
              </p>
              <div className="pl-4 border-l-2 border-green-500/30 dark:border-green-400/30">
                <ol className="space-y-3 list-decimal pl-4">
                  <li>
                    <strong>Home:</strong> Your starting point and introduction to this supportive narrative. <span className="text-sm italic text-gray-500 dark:text-gray-400">(Human-Authored)</span>
                  </li>
                  <li>
                    <strong>Introduction:</strong> Information about the project, author and process. <span className="text-sm italic text-gray-500 dark:text-gray-400">(Human-Authored)</span>
                  </li>
                  <li>
                    <strong>Critical Review:</strong> Analysis of AI research papers, featuring:
                    <ul className="list-disc list-inside space-y-1.5 pl-4 mt-1.5 text-gray-700 dark:text-gray-300 text-sm">
                      <li>
                        Process Description: The methodology and process used to analyze the research papers. <span className="italic text-gray-500 dark:text-gray-400">(Human-Authored)</span>
                      </li>
                      <li>
                        Source Material: A browsable database of the original papers and their LLM generated evaluations. <span className="italic text-gray-500 dark:text-gray-400">(Partly AI-Generated)</span> <SparklesIcon className="inline-block h-4 w-4 text-amber-500 ml-1 align-middle" />
                      </li>
                    </ul>
                  </li>
                  <li>
                    <strong>Methods:</strong> Browse methodologies extracted from analyzed papers by AI. <span className="text-sm italic text-gray-500 dark:text-gray-400">(AI-Generated)</span> <SparklesIcon className="inline-block h-4 w-4 text-amber-500 ml-1 align-middle" />
                  </li>
                  <li>
                    <strong>Results:</strong> Explore AI-analyzed insights based on paper evaluation. <span className="text-sm italic text-gray-500 dark:text-gray-400">(AI-Generated)</span> <SparklesIcon className="inline-block h-4 w-4 text-amber-500 ml-1 align-middle" />
                  </li>
                  <li>
                    <strong>Reflection:</strong> Personal insights on the project, its development and implications. <span className="text-sm italic text-gray-500 dark:text-gray-400">(Human-Authored, Under Development)</span>
                  </li>
                  <li>
                    <strong>Ethics:</strong> Discussing ethical aspects of using AI in research. and using AI to build this project. <span className="text-sm italic text-gray-500 dark:text-gray-400">(Human-Authored, Under Development)</span>
                  </li>
                  <li>
                    <strong>Glossary:</strong> Key terms defined with AI assistance and used to display site-wide tooltips. <span className="text-sm italic text-gray-500 dark:text-gray-400">(AI-Generated)</span> <SparklesIcon className="inline-block h-4 w-4 text-amber-500 ml-1 align-middle" />
                  </li>
                </ol>
              </div>
            </section>
          </div>

          {/* Card 5: Site Capabilities & Features */}
          <div className="bg-white dark:bg-zinc-900 rounded-lg shadow-lg p-6 md:p-8 border border-gray-200 dark:border-zinc-700 border-t-4 border-t-red-500 dark:border-t-red-400">
            <section className="text-gray-700 dark:text-gray-300 text-base leading-relaxed">
              <h3 className="text-xl md:text-2xl font-semibold text-gray-800 dark:text-gray-200 mb-3 flex items-center">
                <LightBulbIcon className="h-6 w-6 md:h-7 md:w-7 mr-3 text-red-500" />
                Features
              </h3>
              <div className="pl-5 border-l-2 border-red-500/30 dark:border-red-400/30">
                <ul className="list-disc space-y-3 pl-4">
                  <li><strong>Interactive Exploration:</strong> Search, filter, and sort through AI-analyzed research papers in the Methods and Results sections.</li>
                  <li><strong>Mini-menu:</strong> When viewing content belonging to any specific paper, a mini-menu appears on the right to navigate between the different content of that paper.</li>
                  <li><strong>Dynamic Glossary:</strong> Key terms are explained, with inline tooltips for quick reference.</li>
                  <li><strong>Theme Switching:</strong> Use the gear icon on the top right to toggle between light/dark theme.</li>
                  <li><strong>Model Selection:</strong> Use the gear icon on the top right to switch between LLMs, this will change the content visible in the evaluation, methods and results pages.</li>
                  <li><strong>Content Transparency:</strong> Clear visual distinction between human and AI generated content.</li>
                  <li><strong>Responsive Design:</strong> Makes it possible to assess this supportive narrative from your mobile device.</li>
                </ul>
              </div>
            </section>
          </div>

          {/* Card 6: Getting Started (User Guidance) */}
          <div className="bg-white dark:bg-zinc-900 rounded-lg shadow-lg p-6 md:p-8 border border-gray-200 dark:border-zinc-700 border-t-4 border-t-indigo-500 dark:border-t-indigo-400">
            <section className="text-gray-700 dark:text-gray-300 text-base leading-relaxed">
              <h3 className="text-xl md:text-2xl font-semibold text-gray-800 dark:text-gray-200 mb-3 flex items-center">
                <ChatBubbleLeftRightIcon className="h-6 w-6 md:h-7 md:w-7 mr-3 text-indigo-500" />
                Where to Start?
              </h3>
              <p className="mb-3">
                Here are a couple of ways to begin exploring this supportive narrative:
              </p>
              <ul className="list-none space-y-4 pl-4 border-l-2 border-indigo-500/30 dark:border-indigo-400/30">
                <li>
                  <strong>Understand the Project:</strong>
                  <span className="block text-sm text-gray-600 dark:text-gray-400 mt-1">
                    Visit the <a href="/introduction" className="text-indigo-600 dark:text-indigo-400 hover:underline font-semibold">Introduction</a> page to learn about the project's objectives, the research process, and the author.
                  </span>
                </li>
                <li>
                  <strong>Explore the Research Database:</strong>
                  <span className="block text-sm text-gray-600 dark:text-gray-400 mt-1">
                    Go directly to the <a href="/critical-review/source-material" className="text-indigo-600 dark:text-indigo-400 hover:underline font-semibold">Critical Review's Source Material</a> to browse the database of research papers and their AI-generated evaluations.
                  </span>
                </li>
              </ul>
              <p className="mt-5">
                Feel free to explore other sections as well!
              </p>
            </section>
          </div>
        </div>
      </section>
    </div>
  )
}
