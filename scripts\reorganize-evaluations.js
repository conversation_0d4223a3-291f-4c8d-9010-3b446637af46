/**
 * <PERSON><PERSON><PERSON> to reorganize evaluation files into the new directory structure
 * 
 * This script will:
 * 1. Create a folder for each paper in the evaluations directory
 * 2. Move the existing evaluation file into that folder with the GPT4 suffix
 * 3. Create placeholder files for other models
 */

const fs = require('fs');
const path = require('path');

// Configuration
const EVALUATIONS_DIR = path.join(__dirname, '..', 'public', 'papers', 'evaluations');
const DEFAULT_MODEL = 'o3';
const DEFAULT_MODEL_FILE_SUFFIX = 'GPT4'; // The actual suffix used in filenames for o3 model
const MODELS = ['o3', 'Gemini', 'Sonnet'];

// Create backup directory
const BACKUP_DIR = path.join(__dirname, '..', 'public', 'papers', 'evaluations_backup_' + Date.now());
fs.mkdirSync(BACKUP_DIR, { recursive: true });

// Get all evaluation files
const files = fs.readdirSync(EVALUATIONS_DIR);
const evaluationFiles = files.filter(file => 
  file.startsWith('Evaluation_') && 
  file.endsWith('.json') && 
  fs.statSync(path.join(EVALUATIONS_DIR, file)).isFile()
);

console.log(`Found ${evaluationFiles.length} evaluation files to reorganize.`);

// Process each file
evaluationFiles.forEach(file => {
  try {
    // Create backup
    fs.copyFileSync(
      path.join(EVALUATIONS_DIR, file),
      path.join(BACKUP_DIR, file)
    );
    console.log(`✓ Backed up ${file}`);

    // Extract paper slug
    const paperSlug = file.replace(/^Evaluation_/, '').replace(/\.json$/, '');
    
    // Create paper directory
    const paperDir = path.join(EVALUATIONS_DIR, paperSlug);
    if (!fs.existsSync(paperDir)) {
      fs.mkdirSync(paperDir, { recursive: true });
      console.log(`✓ Created directory for ${paperSlug}`);
    }

    // Move the file to the paper directory with the default model suffix
    const newFilename = `Evaluation_${paperSlug}_${DEFAULT_MODEL_FILE_SUFFIX}.json`;
    const newFilePath = path.join(paperDir, newFilename);
    
    // Read the original file
    const fileContent = fs.readFileSync(path.join(EVALUATIONS_DIR, file), 'utf8');
    
    // Write to the new location
    fs.writeFileSync(newFilePath, fileContent);
    console.log(`✓ Created ${newFilename} in ${paperSlug} directory`);

    // Create placeholder files for other models
    const placeholderContent = JSON.stringify({
      metadata: {
        title: `[Placeholder for ${paperSlug}]`,
        authors: "[Placeholder]",
        year: 0,
        doi: "[Placeholder]"
      },
      paper_summary: `This is a placeholder file for the ${paperSlug} paper. Please generate an evaluation with this model.`,
      resultsInsights: {
        claimedOutcomes: "Placeholder - Please generate an evaluation with this model.",
        contextualizedBenefits: {
          audioPluginApplications: "Placeholder",
          problemSolvingPotential: "Placeholder"
        },
        contextualizedDrawbacks: {
          limitationsForAudio: "Placeholder",
          implementationHurdles: "Placeholder"
        },
        feasibilityAssessment: "Placeholder",
        keyTakeawaysForAudioDev: "Placeholder"
      }
    }, null, 2);

    // Create placeholder files for other models
    MODELS.forEach(model => {
      if (model !== DEFAULT_MODEL) {
        const placeholderFilename = `Evaluation_${paperSlug}_${model}.json`;
        const placeholderPath = path.join(paperDir, placeholderFilename);
        fs.writeFileSync(placeholderPath, placeholderContent);
        console.log(`✓ Created placeholder ${placeholderFilename} in ${paperSlug} directory`);
      }
    });

  } catch (error) {
    console.error(`Error processing ${file}:`, error);
  }
});

console.log('\nReorganization complete!');
console.log(`Original files backed up to: ${BACKUP_DIR}`);
console.log('\nNext steps:');
console.log('1. Verify that the new directory structure is correct');
console.log('2. Generate evaluations for the other models (Gemini, Sonnet)');
console.log('3. Update the placeholder files with the actual evaluations');
