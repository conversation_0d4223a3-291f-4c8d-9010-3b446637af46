/**
 * Utility functions for glossary processing
 */

/**
 * Adds a data attribute to exclude an element from glossary processing
 * @param element The DOM element to exclude
 */
export function excludeFromGlossary(element: HTMLElement) {
  element.setAttribute('data-no-glossary', 'true');
}

/**
 * CSS class to exclude elements from glossary processing
 */
export const NO_GLOSSARY_CLASS = 'no-glossary';
