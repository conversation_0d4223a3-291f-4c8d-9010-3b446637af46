'use client';

import React from 'react';
import { SparklesIcon } from '@heroicons/react/24/outline';
import Tooltip from '@/components/Tooltip';

interface SparklesIconWithTooltipProps {
  className?: string;
  style?: React.CSSProperties;
}

/**
 * A SparklesIcon component with a consistent tooltip indicating AI-generated content.
 * This component should be used wherever SparklesIcon is displayed to provide
 * consistent user feedback about AI-generated content.
 */
export default function SparklesIconWithTooltip({
  className = "h-5 w-5",
  style
}: SparklesIconWithTooltipProps) {
  return (
    <Tooltip text="This content was AI generated">
      <SparklesIcon
        className={className}
        style={style}
      />
    </Tooltip>
  );
}
