'use client';

import React, { useMemo } from 'react';
import { useGlossary } from '@/context/GlossaryContext';
import Tooltip from '@/components/Tooltip';
import ReactMarkdown from 'react-markdown';

interface GlossaryTextProps {
  text: string;
  className?: string;
}

// Helper component to render markdown content with preserved formatting and tooltips
const MarkdownWithTooltips = ({ content, className = '' }: { content: string; className?: string }) => {
  const { terms, isLoading } = useGlossary();
  
  // Process the content to add tooltips to terms
  const processedContent = useMemo(() => {
    if (isLoading || !terms || terms.length === 0 || !content) {
      return content;
    }
    
    // Create a map of terms for faster lookup
    const termMap = new Map<string, { display: string; definition: string }>();
    terms.forEach(term => {
      // Skip very short terms to avoid false positives
      if (term.term.length >= 3) {
        termMap.set(term.term.toLowerCase(), {
          display: term.term,
          definition: `${term.term}${term.acronym ? ` (${term.acronym})` : ''}: ${term.definition}`
        });
      }
      
      if (term.acronym && term.acronym.length >= 2) {
        termMap.set(term.acronym.toLowerCase(), {
          display: term.acronym,
          definition: `${term.term}${term.acronym ? ` (${term.acronym})` : ''}: ${term.definition}`
        });
      }
    });
    
    // Sort terms by length (longest first) to handle nested terms
    const sortedTerms = Array.from(termMap.keys()).sort((a, b) => b.length - a.length);
    
    let processed = content;
    
    // Process each term and wrap in a tooltip
    sortedTerms.forEach(term => {
      const termInfo = termMap.get(term);
      if (!termInfo) return;
      
      // Create a regex that matches the term as a whole word
      const regex = new RegExp(`\\b(${term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})\\b`, 'gi');
      
      // Replace each occurrence with a tooltip marker
      processed = processed.replace(regex, (match) => {
        // Don't process if already inside a tooltip to avoid nesting
        if (match.includes('data-glossary-term')) return match;
        return `{{GLOSSARY:${match}:${encodeURIComponent(termInfo.definition)}}}`;
      });
    });
    
    return processed;
  }, [content, terms, isLoading]);
  
  // Custom component to render markdown with our styles
  const MarkdownContent = ({ content, className = '' }: { content: string; className?: string }) => (
    <ReactMarkdown 
      className={className}
      components={{
        // Handle bold text with white color
        strong: ({ node, ...props }) => (
          <strong className="text-white font-semibold" {...props} />
        ),
        // Handle inline code (if needed)
        code: ({ node, ...props }) => (
          <code className="bg-gray-100 dark:bg-gray-800 px-1 rounded" {...props} />
        ),
        // Handle paragraphs
        p: ({ node, ...props }) => (
          <p className="mb-4 leading-relaxed" {...props} />
        ),
        // Handle links
        a: ({ node, ...props }) => (
          <a className="text-blue-500 hover:underline" target="_blank" rel="noopener noreferrer" {...props} />
        )
      }}
    >
      {content}
    </ReactMarkdown>
  );
  
  // Process the content to replace our markers with tooltips
  const renderMarkdown = () => {
    // If no terms to process, just render the markdown
    if (isLoading || !terms || terms.length === 0 || !processedContent) {
      return <MarkdownContent content={content} className={className} />;
    }
    
    // Process our custom markers
    const parts: (string | React.ReactNode)[] = [];
    let remaining = processedContent;
    let lastIndex = 0;
    
    // Find all our custom markers
    const markerRegex = /\{\{GLOSSARY:(.*?):(.*?)\}\}/g;
    let match;
    
    while ((match = markerRegex.exec(processedContent)) !== null) {
      // Add text before the marker
      if (match.index > lastIndex) {
        const beforeText = processedContent.substring(lastIndex, match.index);
        parts.push(
          <MarkdownContent 
            key={`before-${match.index}`} 
            content={beforeText} 
          />
        );
      }
      
      // Add the tooltip component
      const term = match[1];
      const definition = decodeURIComponent(match[2]);
      
      parts.push(
        <Tooltip key={`tooltip-${match.index}`} text={definition}>
          <span className="border-b border-dotted border-gray-400 cursor-help">
            <MarkdownContent content={term} />
          </span>
        </Tooltip>
      );
      
      lastIndex = match.index + match[0].length;
    }
    
    // Add any remaining text
    if (lastIndex < processedContent.length) {
      const remainingText = processedContent.substring(lastIndex);
      parts.push(
        <MarkdownContent 
          key="remaining" 
          content={remainingText} 
        />
      );
    }
    
    // If we found markers, render the processed parts
    if (parts.length > 1) {
      return (
        <span className={className}>
          {parts.map((part, i) => (
            <React.Fragment key={i}>{part}</React.Fragment>
          ))}
        </span>
      );
    }
    
    // Otherwise, just render the original content with markdown
    return <MarkdownContent content={content} className={className} />;
  };
  
  return renderMarkdown();
};

export default function GlossaryText({ text, className = '' }: GlossaryTextProps) {
  if (!text) return null;
  
  return (
    <MarkdownWithTooltips 
      content={text} 
      className={className} 
    />
  );
}
