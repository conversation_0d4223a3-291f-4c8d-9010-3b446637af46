'use client';

import React from 'react';
import { useGlossary } from '@/context/GlossaryContext';
import Tooltip from '@/components/Tooltip';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

interface DirectGlossaryTooltipProps {
  children: React.ReactNode;
  className?: string;
}

/**
 * A simplified approach to glossary tooltips with proper formatting
 */
export default function DirectGlossaryTooltip({ children, className = '' }: DirectGlossaryTooltipProps) {
  const { terms, isLoading } = useGlossary();

  // If terms aren't loaded or children is undefined, just return the children
  if (isLoading || !terms || terms.length === 0 || children === undefined) {
    return <span className={className} style={{ display: 'inline' }}>{children}</span>;
  }

  // More robust handling of non-string content
  // This is a critical section for handling title text
  let content = '';

  if (typeof children === 'string') {
    content = children;
  } else if (children === null || children === undefined) {
    content = '';
  } else if (typeof children === 'object') {
    // For object, try to get a meaningful representation
    try {
      // If it's a React element, try to extract text content
      if (React.isValidElement(children)) {
        // If it has props.children that is a string, use that
        const props = children.props as any;
        if (props && typeof props.children === 'string') {
          content = props.children;
        } else {
          content = '[React Element]';
        }
      } else {
        // For other objects, try JSON stringify with fallback
        try {
          content = JSON.stringify(children);
        } catch (e) {
          content = Object.prototype.toString.call(children);
        }
      }
    } catch (e) {
      content = '[Object]';
    }
  } else {
    // For all other types, coerce to string
    content = String(children);
  }

  // Skip processing if content is empty
  if (!content.trim()) {
    return <span className={className} style={{ display: 'inline' }}>{content}</span>;
  }

  // Process the content to add tooltips
  const elements: React.ReactNode[] = [];

  // Create array of all potential matches with their positions
  const matches: Array<{
    index: number;
    length: number;
    text: string;
    definition: string;
    termData: any;
  }> = [];

  // Collect all matches (both terms and acronyms) in a single pass
  for (const term of terms) {
    // Skip short terms
    if (!term.term || term.term.length < 3) continue;

    // Check for full term matches
    const termPattern = `\\b${term.term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`;
    const termRegex = new RegExp(termPattern, 'gi');
    let match;

    while ((match = termRegex.exec(content)) !== null) {
      matches.push({
        index: match.index,
        length: match[0].length,
        text: match[0],
        definition: `${term.term}${term.acronym ? ` (${term.acronym})` : ''}: ${term.definition}`,
        termData: term
      });
    }

    // Check for acronym matches
    if (term.acronym && term.acronym.length >= 2) {
      const acronymPattern = `\\b${term.acronym.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`;
      const acronymRegex = new RegExp(acronymPattern, 'gi');

      while ((match = acronymRegex.exec(content)) !== null) {
        matches.push({
          index: match.index,
          length: match[0].length,
          text: match[0],
          definition: `${term.term}${term.acronym ? ` (${term.acronym})` : ''}: ${term.definition}`,
          termData: term
        });
      }
    }
  }

  // Sort matches by position
  matches.sort((a, b) => a.index - b.index);

  // Remove overlapping matches (keep the first one found)
  const filteredMatches = [];
  for (const match of matches) {
    const hasOverlap = filteredMatches.some(existing =>
      (match.index < existing.index + existing.length) &&
      (match.index + match.length > existing.index)
    );
    if (!hasOverlap) {
      filteredMatches.push(match);
    }
  }

  // Build the final elements array
  let lastIndex = 0;
  for (const match of filteredMatches) {
    // Add text before the match
    if (match.index > lastIndex) {
      elements.push(content.substring(lastIndex, match.index));
    }

    // Add tooltip for the matched term
    elements.push(
      <Tooltip key={`tooltip-${match.index}-${match.text}`} text={match.definition}>
        <span className="border-b border-dotted border-gray-400 cursor-help" style={{ display: 'inline' }}>
          {match.text}
        </span>
      </Tooltip>
    );

    lastIndex = match.index + match.length;
  }

  // Add any remaining text
  if (lastIndex < content.length) {
    elements.push(content.substring(lastIndex));
  }

  // Preserve the strong text formatting for titles by adding explicit styling
  return elements.length > 0 ? (
    <span className={`${className} text-white font-semibold`} style={{ display: 'inline' }}>
      {elements}
    </span>
  ) : (
    <span className={`${className} text-white font-semibold`} style={{ display: 'inline' }}>{content}</span>
  );
}
