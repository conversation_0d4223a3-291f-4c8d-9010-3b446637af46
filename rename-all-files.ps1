# Function to clean filenames
function Get-CleanFileName {
    param (
        [string]$fileName
    )
    
    # Get the base name and extension
    $baseName = [System.IO.Path]::GetFileNameWithoutExtension($fileName)
    $ext = [System.IO.Path]::GetExtension($fileName)
    
    # Keep the number prefix but clean up the rest
    if ($baseName -match '^\d+\.?\s*(.+)$') {
        $number = $baseName.Split('.')[0]
        $rest = $matches[1]
        
        # Clean up the rest of the name
        $cleanRest = $rest -replace '[^a-zA-Z0-9]', '_'
        # Remove multiple underscores
        $cleanRest = $cleanRest -replace '_+', '_'
        # Remove trailing underscores
        $cleanRest = $cleanRest.Trim('_')
        
        return "${number}_${cleanRest}${ext}"
    }
    
    # If no number prefix, just clean the whole name
    $cleanName = $baseName -replace '[^a-zA-Z0-9]', '_'
    $cleanName = $cleanName -replace '_+', '_'
    $cleanName = $cleanName.Trim('_')
    
    return "${cleanName}${ext}"
}

# Create a mapping of old to new names
$fileMapping = @{}

# Process PDF files
Get-ChildItem -Path ".\public\papers\pdf\*.pdf" | ForEach-Object {
    $newName = Get-CleanFileName -fileName $_.Name
    $fileMapping[$_.Name] = $newName
    Write-Host "Will rename: $($_.Name) -> $newName"
}

# Process MD files (excluding evaluations directory)
Get-ChildItem -Path ".\public\papers\*.md" -Exclude "evaluations/*" | ForEach-Object {
    $newName = Get-CleanFileName -fileName $_.Name
    $fileMapping[$_.Name] = $newName
    Write-Host "Will rename: $($_.Name) -> $newName"
}

# Output the mapping for verification
Write-Host "`nFile mapping preview:`n"
$fileMapping.GetEnumerator() | Sort-Object Name | Format-Table -AutoSize

# Perform the renaming
foreach ($entry in $fileMapping.GetEnumerator()) {
    $oldPath = ".\public\papers\pdf\$($entry.Name)"
    $newPath = ".\public\papers\pdf\$($entry.Value)"
    if (Test-Path $oldPath) {
        Rename-Item -Path $oldPath -NewName $entry.Value -Force
        Write-Host "Renamed PDF: $($entry.Name) -> $($entry.Value)"
    }
    
    $oldPath = ".\public\papers\$($entry.Name)"
    $newPath = ".\public\papers\$($entry.Value)"
    if (Test-Path $oldPath) {
        Rename-Item -Path $oldPath -NewName $entry.Value -Force
        Write-Host "Renamed MD: $($entry.Name) -> $($entry.Value)"
    }
}

Write-Host "`nFile renaming complete. Please update the paperMetadata object with these new filenames."

# Output the new paperMetadata format
Write-Host "`nNew paperMetadata format (copy this to your code):`n"
Write-Host "const paperMetadata: { [key: string]: { display: string; file: string } } = {"
$fileMapping.GetEnumerator() | Sort-Object Name | ForEach-Object {
    $oldName = $_.Name
    $newName = $_.Value
    $baseName = [System.IO.Path]::GetFileNameWithoutExtension($oldName)
    $newBaseName = [System.IO.Path]::GetFileNameWithoutExtension($newName)
    $routeKey = $newBaseName.ToLower() -replace '[^a-z0-9]', '_'
    Write-Host "  '${routeKey}': {"
    Write-Host "    display: '${baseName}',"
    Write-Host "    file: '${newBaseName}'"
    Write-Host "  },"
}
Write-Host "};" 
