'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

// Define the available models
export const AVAILABLE_MODELS = ['o3', 'Gemini', 'Sonnet'] as const;
export type ModelType = typeof AVAILABLE_MODELS[number] | 'legacy';

// Define the context type
interface ModelContextType {
  selectedModel: ModelType;
  setSelectedModel: (model: ModelType) => void;
  availableModels: ModelType[];
  isModelSelectorReady: boolean;
}

// Create the context with default values
const ModelContext = createContext<ModelContextType>({
  selectedModel: 'o3',
  setSelectedModel: () => {},
  availableModels: ['legacy'],
  isModelSelectorReady: false,
});

// Hook for using the context
export const useModelContext = () => useContext(ModelContext);

// Provider component
export function ModelProvider({ children }: { children: ReactNode }) {
  // Initialize selectedModel from localStorage or default to 'o3'
  const [selectedModel, setSelectedModelState] = useState<ModelType>(() => {
    // Only access localStorage on the client side
    if (typeof window !== 'undefined') {
      const savedModel = localStorage.getItem('selectedModel') as ModelType;
      return savedModel && AVAILABLE_MODELS.includes(savedModel as typeof AVAILABLE_MODELS[number]) ? savedModel : 'o3';
    }
    return 'o3';
  });
  
  const [availableModels, setAvailableModels] = useState<ModelType[]>(['legacy']);
  const [isModelSelectorReady, setIsModelSelectorReady] = useState(false);
  
  // Custom setter that updates both state and localStorage
  const setSelectedModel = (model: ModelType) => {
    setSelectedModelState(model);
    if (typeof window !== 'undefined') {
      localStorage.setItem('selectedModel', model);
    }
  };

  // Fetch available models on mount
  useEffect(() => {
    async function fetchAvailableModels() {
      try {
        const response = await fetch('/api/list-evaluations');
        if (!response.ok) {
          throw new Error('Failed to fetch available models');
        }
        const data = await response.json();
        
        if (data.availableModels && Array.isArray(data.availableModels)) {
          setAvailableModels(data.availableModels);
          
          // Set the selected model to the first available model if it's not already in the list
          if (!data.availableModels.includes(selectedModel)) {
            setSelectedModel(data.availableModels[0]);
          }
        }
        
        setIsModelSelectorReady(true);
      } catch (error) {
        console.error('Error fetching available models:', error);
        setIsModelSelectorReady(true); // Still mark as ready so UI doesn't hang
      }
    }

    fetchAvailableModels();
  }, [selectedModel]);

  // Provide the context value
  const contextValue = {
    selectedModel,
    setSelectedModel,
    availableModels,
    isModelSelectorReady,
  };

  return (
    <ModelContext.Provider value={contextValue}>
      {children}
    </ModelContext.Provider>
  );
}
